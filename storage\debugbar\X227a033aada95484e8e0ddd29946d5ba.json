{"__meta": {"id": "X227a033aada95484e8e0ddd29946d5ba", "datetime": "2025-07-31 15:52:25", "utime": **********.441476, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:52:25] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.42598, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753977143.34258, "end": **********.441539, "duration": 2.098958969116211, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": 1753977143.34258, "relative_start": 0, "end": **********.06629, "relative_end": **********.06629, "duration": 1.7237098217010498, "duration_str": "1.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.066371, "relative_start": 1.7237908840179443, "end": **********.441544, "relative_end": 5.0067901611328125e-06, "duration": 0.37517309188842773, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50696544, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.383123, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03536, "accumulated_duration_str": "35.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266061, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 17.845}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.301337, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 17.845, "width_percent": 3.054}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.311169, "duration": 0.02615, "duration_str": "26.15ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 20.899, "width_percent": 73.954}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.350667, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.853, "width_percent": 5.147}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-658064911 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-658064911\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-271537577 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-271537577\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-884834326 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884834326\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-849090991 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iml1Y3pURGFVMXQ4V2ZRZVFzYmYvWmc9PSIsInZhbHVlIjoiLy9TYlFWRjh0NUx2YUlEeDNyWTNWUjJseUl3OVJlTXRVWTJxYktiNE1HYXJSSXUwUFVyYi9KQm9pSmZ5dzQ2VnJaUEtsOE10ckpXaWdvNW5JcFhjN1JPcTc3dUFjSEN3Mk1OWmVNRVk2TnkwUkpNNmdyVjFxWWVqa1RpUnQ0enYzUjZ3OHlaelBRbjRWZkRaKzVkNXQ1WU5yTjd2bjVvRm9SaXRIWlFMQ0ZrK0Rxc0VDcEY5UEEvR1F2YXJCT09vdytObnI1YVZBQmlaL3hENk9LYkVDZklzQWZpU2VpZHRSeDZsZzB5eFBCNUFsQytreEdqRmxjdXlKV1BjOU9PZkhDdU5pQjVnd2xUZHdsQnRBbVQ3dkphM29Sejh0OEszdEtvMmpuaXdxM3lPVlRGWUMzc3JheVAvbXRjRmhNRVhwWUsxTTB3ZG01ZmxCQzA5NnJRaWlRcW8xMXRzeDNuaTZJWFlzZ1o4bGEybUxuSmxiQ0NvQk1IVFJCNHRqM2YxcG5YNHlzcVJvTE94R1JzOGtZM1hSeUQ5QVN2OUVvTW9mMnVIUDhPMDhWdmZuc2RHN01LaHE0VlgvSXBuQ1V4TmQvSjlCaXJBRU5lZ2pvY2FCNzVLSGFEaU5IaHVMSnlPYzBkNGRkaGJTa2g1enVzS09lT1hQcUxuWUdKQ1M0SlIiLCJtYWMiOiJkNDhhMGY2N2NkYjY0NWNiY2M5YTJiYjY3YzhmZDg3YmQyZWE1ODA0MmJlYmY5NjhkZmU1NGU1NWU2MDUzN2Y4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImUvU1J4MGpFU2ZRaW00cGZEL1Z1Ymc9PSIsInZhbHVlIjoiWG5BZG5ndHJOenRlVXZIdG9FcXRETVVwTGs4WDhNZ2xLS0lFQ0ZvVlNITXJQR3dZdjRJMnpZQ2d6WW1xT0pacXUwYnBrQk83NTAvQzNYUi8yQmQ0Ri9sVDlNWVZXaEpSSW9Rak1GczRObWd2dFNsc3VEVndJWExEc041a1VOWmswSGZSZVRndURlZU13aVZtZDRSNXNDdWtIWUszTWQvV2hmWFBnNVZWUUV3RFovM2M3WGNobWd1MGQ0dFJYVE9SeGpUenplWlB2dEJjaitiWTN3a050R3djdjhza285c0FnZmVyVkpGcmhZekx4QSs4NEg1TXp3OVEwbk9ZWk5jZzdHSmMwcnRMYzV1QURwQWEyNUl0QWIwM3JFMVk3cXhmZjNOS0dZaGlPZGU2by9xcUdjRkhabnJjWW9UZ0h3aWZ1bm11a0VwSW1GL2IrQ3JkQjBlQjVndEVXN3o2b2pudTBEUWRWbklKa3l2Y3pHcXV0MW9GOUMxc0tEbGFhZkxDYU15VllMa1IxdGpyc0h2WHJPYnZUKzMrY2ZodXNCYVNoblVyK3o3dU5IbzR6dlcxdEFBOU1LRkgrSTZzNHdjd3JFMUVadXhXVHN3dUFLTkhyWkF1M2tUQ25Nc0Z4NFFrRWxMNEpiSDA0MmV3ZzNwWUlqa2wyZUl1MUl4M0dxdEEiLCJtYWMiOiJlOGU1ZDE4ZTYwMTM3MzgyODU5NTM0MGE1MDAzZWFlNjE5NWQwMWNjNmI1OGE1NmE5YmQ5M2E4YzE2Y2FlZjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849090991\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1281005927 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tkY9CRlDbFfp5lomd7oRDJqrNFwJ8jruTsGOoaTE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281005927\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1163869890 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik96Sk5JYk96akJPbHovY2RHYkZVQ3c9PSIsInZhbHVlIjoibVhIa3JUSHF4a2FTVUUvR0hjcVVORUo5RDNQbnd4VTZxczR2Njhhd2gwZkUyS01HblNDTDZQbW5JOVF5aEhnd2dlVHd2bE5XMEFTb0xGQVI4N09MdUxJeFZ0eTdmSHZWd3drWHV0S2FPbzFmZ2xLcHlyTUFTeHJsMjdGbnVmc2l5OStGQnNIclVZRWowZ3kzRno2ZnFlaUZqb0Q3SFFRYjYrMlh4UVUxejFtd09PMklITTREZTNhZ2NLU1lYL2RycEZIM1RnNGp4dXNjaHNEL3V0UFdpbC9kSXk5YkZkQmxmaitvUUUxTnhRU0JJUVBZdW9Cbll0b0ozWkhsb3FSQkN3REZUU1Q0cUdPRXA5cTd0WjFROENvKzJYb3VQU0gwSy80VkVNblVTYTBmQXZEaG5HY3hTbnUzSjI1NXdTTHpMSXB4b0N0M0xSaVlieWpTK3V5d04vamhUMkx4UWlyaUpyZ3RYT3MrK08yV2gvZ21ITC8zcC95Ukw0dWk4dWViWVAwQ09sNmNLUXNqL1JhelRxaUlnUGZvU1NhaHpBTEJEQzduV0diS3U4YVM1MjVHdjNvNnhEY0lsRm5xdm41L1BrS09VcU1RVTlSRVRKdzZKSHN0dlo5Ni9nRXV0N25OSlp3VjJtOUUxUlIyaWNSQ1owM3FNZWhmWDgrWXU5Q0oiLCJtYWMiOiIxODcwY2QxNTMzOGEzZGQ1NWVmODBlNzI4MGM4OTI3ZTc4ZjFlMzAyYjY2Mjg4NDNiMTdhYjQ3NzVjZmUxN2U3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imc4SExSSlNaQXl3SU5HdjNjbW9lYXc9PSIsInZhbHVlIjoiNVdQL0VIa05kSW43WE1BODZUTFZJUlpwZnlGWlZabFpRU3VXdEFpUXowb29halRhRmY0Skp6c1BRQlFBSmEveVJQTUxvMDRLa2ZmeHlXMzFqVHk3STloRU1xak9lempVNGZNeVBxUWhKQjQwRFlhOHZVckRJRTlRd1pKNFhiUXMrRGpiOGRqNS94Z0ExcGN3UUJsYlB0SEF4NEVkNDg2UXBBMHR1N1REdGVmY251VkQvQWlHeXRtSyt1Z2VPcDM3bmFFZS9jaUtyOXUwUVd4M0RGTlprOXd6UmpwbkRvU29Ydm5SeWx3UEFXYmhmR2pOTnZSQ1JPYThDK1NKUlExS2VKb2JtNHBSVUhlTjVDK202Q09DOFJmQlFkZEhqUHRBR2JsUURjTHJJdGJEVDNJSUtISFZqUXVaWnU0Z0IvSXdRaXIvZnpTOGdDemdKcFdzOUVpS0NOa1R5MzFDR3pIa1psUXhuUWxGQmtpZDVwWEkwSFR4TFp4emtneHZ4YzJzMytjM1VESHc2dFZSNy9LMmttS1AxSFMwOWxQOUI4Q3k1eXdVNThjYXlrSXZFWm9UMEpQSHg1QTdGZzlJQnJDMTEzc1JNaEhnbjlNZHB4TWpmNXZqZmZreXpRWERVMXhjZWFEbkpCa3B4MzQwS1loS1VrQ3BweXlpeGF5VzZNdW4iLCJtYWMiOiIwNmZjN2I0M2FhOThiYTZhMTBjZGMwN2I1NjlmNmMyYTViYmJiNGY3Y2Q1N2I5ZWE1ZTBjZDZmNjgzN2E4NTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik96Sk5JYk96akJPbHovY2RHYkZVQ3c9PSIsInZhbHVlIjoibVhIa3JUSHF4a2FTVUUvR0hjcVVORUo5RDNQbnd4VTZxczR2Njhhd2gwZkUyS01HblNDTDZQbW5JOVF5aEhnd2dlVHd2bE5XMEFTb0xGQVI4N09MdUxJeFZ0eTdmSHZWd3drWHV0S2FPbzFmZ2xLcHlyTUFTeHJsMjdGbnVmc2l5OStGQnNIclVZRWowZ3kzRno2ZnFlaUZqb0Q3SFFRYjYrMlh4UVUxejFtd09PMklITTREZTNhZ2NLU1lYL2RycEZIM1RnNGp4dXNjaHNEL3V0UFdpbC9kSXk5YkZkQmxmaitvUUUxTnhRU0JJUVBZdW9Cbll0b0ozWkhsb3FSQkN3REZUU1Q0cUdPRXA5cTd0WjFROENvKzJYb3VQU0gwSy80VkVNblVTYTBmQXZEaG5HY3hTbnUzSjI1NXdTTHpMSXB4b0N0M0xSaVlieWpTK3V5d04vamhUMkx4UWlyaUpyZ3RYT3MrK08yV2gvZ21ITC8zcC95Ukw0dWk4dWViWVAwQ09sNmNLUXNqL1JhelRxaUlnUGZvU1NhaHpBTEJEQzduV0diS3U4YVM1MjVHdjNvNnhEY0lsRm5xdm41L1BrS09VcU1RVTlSRVRKdzZKSHN0dlo5Ni9nRXV0N25OSlp3VjJtOUUxUlIyaWNSQ1owM3FNZWhmWDgrWXU5Q0oiLCJtYWMiOiIxODcwY2QxNTMzOGEzZGQ1NWVmODBlNzI4MGM4OTI3ZTc4ZjFlMzAyYjY2Mjg4NDNiMTdhYjQ3NzVjZmUxN2U3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imc4SExSSlNaQXl3SU5HdjNjbW9lYXc9PSIsInZhbHVlIjoiNVdQL0VIa05kSW43WE1BODZUTFZJUlpwZnlGWlZabFpRU3VXdEFpUXowb29halRhRmY0Skp6c1BRQlFBSmEveVJQTUxvMDRLa2ZmeHlXMzFqVHk3STloRU1xak9lempVNGZNeVBxUWhKQjQwRFlhOHZVckRJRTlRd1pKNFhiUXMrRGpiOGRqNS94Z0ExcGN3UUJsYlB0SEF4NEVkNDg2UXBBMHR1N1REdGVmY251VkQvQWlHeXRtSyt1Z2VPcDM3bmFFZS9jaUtyOXUwUVd4M0RGTlprOXd6UmpwbkRvU29Ydm5SeWx3UEFXYmhmR2pOTnZSQ1JPYThDK1NKUlExS2VKb2JtNHBSVUhlTjVDK202Q09DOFJmQlFkZEhqUHRBR2JsUURjTHJJdGJEVDNJSUtISFZqUXVaWnU0Z0IvSXdRaXIvZnpTOGdDemdKcFdzOUVpS0NOa1R5MzFDR3pIa1psUXhuUWxGQmtpZDVwWEkwSFR4TFp4emtneHZ4YzJzMytjM1VESHc2dFZSNy9LMmttS1AxSFMwOWxQOUI4Q3k1eXdVNThjYXlrSXZFWm9UMEpQSHg1QTdGZzlJQnJDMTEzc1JNaEhnbjlNZHB4TWpmNXZqZmZreXpRWERVMXhjZWFEbkpCa3B4MzQwS1loS1VrQ3BweXlpeGF5VzZNdW4iLCJtYWMiOiIwNmZjN2I0M2FhOThiYTZhMTBjZGMwN2I1NjlmNmMyYTViYmJiNGY3Y2Q1N2I5ZWE1ZTBjZDZmNjgzN2E4NTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163869890\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-991916043 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991916043\", {\"maxDepth\":0})</script>\n"}}