{"__meta": {"id": "X2fc68c2fc6052318a93c047a9357de29", "datetime": "2025-07-31 16:58:57", "utime": **********.517527, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981135.197244, "end": **********.517598, "duration": 2.3203539848327637, "duration_str": "2.32s", "measures": [{"label": "Booting", "start": 1753981135.197244, "relative_start": 0, "end": **********.259251, "relative_end": **********.259251, "duration": 2.062007188796997, "duration_str": "2.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.25929, "relative_start": 2.****************, "end": **********.517602, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00694, "accumulated_duration_str": "6.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4470582, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-197358631 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-197358631\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1858746975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1858746975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-784264072 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784264072\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1950519855 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRiKzZDQjgxTFpwTHNMT013VkdoNVE9PSIsInZhbHVlIjoiUEtaa2djeW1YOEVTbXY5b0VrejVmdFAzb0drbCtLSlRxdTNpYlpjaFFHSjRRYkEvaEFWSjhwazkvTlFYd2MzWjNGaWtsc1p2TWZ4bkEvc1QrcDI0dWIwZVBKZ3pieUxOYTZIYUFiQS8va3UrZUkya2Y5UnVORXZVZlRVVWVWdWJ6Uzl5ekpVbDMxOEtnclF1bXNyLzc4YnNvWEFyTUVXRVpXTTh6aE84VkkraHZOc2cxNmkzcytIYVlEOVhUTytJMEpKT2QwdUpOUUlyNy9ralFCVFhOSlZCV2lhMnIyQ2g0RGQ2YmZrcHdueENuZ3hMU2poSTFJbm9JMU43ZG1xSUtIdW5wZWxJNmZQbkExekpaR1ZJczVEeHpQQU5zMzl3eUUrR2hWU21ObmZOTkV6azF5emlWdmVGUUkvMFRBSU1kS3RmNTdrcks5enhWcmljUk1IMnRYTi9BeVdweW5FY3ZXNUxveDBJZ1UwZ0lRMmp1ekxVK2NzYmR5MHdKaXMzUTd1WVpmcVR5MGpZOFlCTjdBYStzMWttM1J4VlJESTc3K0pZWE5MVC9qYlhJc2U1b1h5VURhbFZKcU53VnhRWVd3YTF6MlM3WFNNK09XNjZib2tSN2RyOEtJZzJMNWczQTdiNkdnYlpralh5cnNSQi9BZ3pwMlV2eEV6cStLeHEiLCJtYWMiOiIyMTQ1MzAyYjE4ZWYxMzkwNTAzOGI5MDRiNzdmOTU1NjJlYTFmNjU1NTQyMTZhZTRkYTNlOWI4YWZlYzdhNDE5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkE0K1JaUXBMUnB5NzN0dWN4alpWQmc9PSIsInZhbHVlIjoicitqNThZQmJJOG1qMWh1WDdPdjcxRWNLUFVJNElmVVBhSXh3RXA1T1V5YkJVTm0rdVhUSmR5azdJMkNkUk1ERjYyQnNDenQxQXJiN1E2M3VwUHAxZDljbU5mdU5OZGNtNE53c2NIdVNGY2JBdkFoYjJCSTcweTJLeHlqSVdMRC9sbWdtOExpMlJIWjZHVHJReUZXb2VTQnFiSEtna0ZydXpYZkVFNHlqd3FCTXRQMXBxNlFWSkIwdnFUWFA4cUFHMzlmN3dsaGt4T1c5dGlZdkxHRVhOM250RlkxMml3WDB0R25EQTM5VTU1dFkwMGNsS3liZVVZZUJKeVJIbkJkazQvaHN1L05hSWowQWNLTXh4TVZOcWtqUmMrUVA4NGFKR3NRakE2d1ZaMkV3MWZKeXRMbzlicU02bDFaNGJhcGR3MFQyaS9xb0p3ZTA2ZzFOYzhlMlp5d1JZcG1HQ2tUUjdCeEEwU0F0WVVNSmd1SnRJcWJWaGoyTHNsalMvdzc2TitkOFdEbDM1eWYwaWlnZGM4WU9nWGMvaWVBSjR5bGNuVkpzQStWMHFVWGtmaWhRSk00WWRORW5yQUR3OHpuUi9yUDVnOE8wYjIwdkxicU9NVU1oYmRCUUtZT0NnaXJwWGlpWURDWndsVWZ2eWpjeDZoekx5U0ZhNnRoZ0hrcEkiLCJtYWMiOiI4M2M5ZDNhYTFjM2Y0NTFmODlhYTA2NjliYTFhNzUxZWNjMTFmYjBkYjkwYjUyYmM0NTA3YmI3ODFiNzhlZjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950519855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-732308789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732308789\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-708540769 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:58:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtPekNCSzQyNmgyYjR1bEVpL1JEYUE9PSIsInZhbHVlIjoiSnBmUG1oMzhPNVplN2NBdFp0SHlXQVVZZ2xZOThhSjl1UmJoQ0x0MkJNUmtzWmRBTVM0WlFvSWFDR0V1STBuYVVhUFY3NWRSUGkyWXVRVXBxSjNjVmIvVENBWFpiNkVyNE9vZmVPeFB4eG52dTNYeWthRjA4U1RuMXY4TGV4UWpBNlNiVVNIVytQN3FyZ29hSDVUMEpXdmFmUGlKT0s4NC9OcmZHTHNHUncvbEhWUlpMb1hVYzNJNVAyZCtpZURpMGhtbm41aHBTeEpRWVk1MTR6ak1OaXA3UGhoRXM0MGJKYWk4WVNpcHlHYWpsVk05dWoxMEVsMmtuMnRENExiTnEvS2NCMks0c2FxQzUrNWlpRHk5WDhtN2Y4UXg0ZTFyTVkwRFFnVXd2OVNvOVNpa1RLVHJVemZ3Yjd0NnYxM1oreG9iWTd6WjMwRk4wN2tHRHN6Y3FiV2Rqck8yNmZVanZlNDN0KzFGUlBqTEs5emZRSW40eFdVMy9XT3g1S2lPSDVMMVoyWC9waG9IZmVDdERRd0ZaQTBONHFueW1rRVAvUG83SHM2d3dWQWp4N1VTdHdIOHpDVm9lcVVpa1M4SzAvSnQxQ2xPYTYzL3BMSENCcUlTNzBsdDhGK3QvQmN5RVJXRHdqcElOcndBZ0VlbGFlRm43dFF3eDhCMnJUNHUiLCJtYWMiOiI5ZThkZTg1MGZjMDA5YTAyZmI2MzkyNWNhMjU2MTllN2Q5NDFlZGRmYThiOGNlN2MzODg5MTkxZjk0NGI1YmJhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktlanR6U3l2TzQ5eTZHME1zaFh2VUE9PSIsInZhbHVlIjoieWlteVRKT2IyZEl2T3MrTVpMQ0p1VmRJa01EaXZiNTdlN3g4UXIvLzJIRUc1akw0Uit2MGxOWmVHUnRwWFY1empiWE9vMFpHUk4zNkQ5ZHVyZ3R0T3JmNDVlM3o1aU1ndzFrZnJhVHl1cUtINzJHaEtnS1lHRFErK3NrTnVsWmt4N0U1SWFFZDFxbU5oVStzYzNaQ1MzaC9QYXZick1XbUlJdi8rZHVTdUZEaTZGRXJCUXcxWHlVV1k1NzcvMTFjeWVXQnluOGsyaXdxcFlKRjdlSThwVzlVcWRmc2J5WXovelZHTjBJYkVOK01XMzYwVzdVdjRzbDdWOHROVUNCU3BQOTNLMWpJSlV5cGl4cTZXS3RLZkVWNGlneWdkS2lnelFlQm5PTXBsT0VKY0FrZmhkZzV0aFBWU1U2RUlEMFBvNEp2Z3JoV2FDTVZkWFc5bGVyN0NVMmZRZWNqM3BLSHBJNFhyeXZIMXg1NzFYT3JUYzZDNWpsRVNSa0YvWDVzUWxldkFwbDJEelh1YklYM1JoM3JoTlpFYlEwYTVtc3VDL2xjS0xpalJ2bUJjZTZjUmltL014MldUSUE1dU04SG5ibjZhUmlRL3IybXhyZFBuNzlySWgzRERhaFZ4bVRML01ITG5FdkJETGNXNU15dk5LWDFzQnA5Z1cyaEJuckgiLCJtYWMiOiIyNTEyNzM4MGZlNjY5NzU5Njc1YWE4OGYyYjc5MTk2ODI1ZDNhZmEyZTAwMzViM2IzNTYzMmRjMGE2Y2RiMTk3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtPekNCSzQyNmgyYjR1bEVpL1JEYUE9PSIsInZhbHVlIjoiSnBmUG1oMzhPNVplN2NBdFp0SHlXQVVZZ2xZOThhSjl1UmJoQ0x0MkJNUmtzWmRBTVM0WlFvSWFDR0V1STBuYVVhUFY3NWRSUGkyWXVRVXBxSjNjVmIvVENBWFpiNkVyNE9vZmVPeFB4eG52dTNYeWthRjA4U1RuMXY4TGV4UWpBNlNiVVNIVytQN3FyZ29hSDVUMEpXdmFmUGlKT0s4NC9OcmZHTHNHUncvbEhWUlpMb1hVYzNJNVAyZCtpZURpMGhtbm41aHBTeEpRWVk1MTR6ak1OaXA3UGhoRXM0MGJKYWk4WVNpcHlHYWpsVk05dWoxMEVsMmtuMnRENExiTnEvS2NCMks0c2FxQzUrNWlpRHk5WDhtN2Y4UXg0ZTFyTVkwRFFnVXd2OVNvOVNpa1RLVHJVemZ3Yjd0NnYxM1oreG9iWTd6WjMwRk4wN2tHRHN6Y3FiV2Rqck8yNmZVanZlNDN0KzFGUlBqTEs5emZRSW40eFdVMy9XT3g1S2lPSDVMMVoyWC9waG9IZmVDdERRd0ZaQTBONHFueW1rRVAvUG83SHM2d3dWQWp4N1VTdHdIOHpDVm9lcVVpa1M4SzAvSnQxQ2xPYTYzL3BMSENCcUlTNzBsdDhGK3QvQmN5RVJXRHdqcElOcndBZ0VlbGFlRm43dFF3eDhCMnJUNHUiLCJtYWMiOiI5ZThkZTg1MGZjMDA5YTAyZmI2MzkyNWNhMjU2MTllN2Q5NDFlZGRmYThiOGNlN2MzODg5MTkxZjk0NGI1YmJhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktlanR6U3l2TzQ5eTZHME1zaFh2VUE9PSIsInZhbHVlIjoieWlteVRKT2IyZEl2T3MrTVpMQ0p1VmRJa01EaXZiNTdlN3g4UXIvLzJIRUc1akw0Uit2MGxOWmVHUnRwWFY1empiWE9vMFpHUk4zNkQ5ZHVyZ3R0T3JmNDVlM3o1aU1ndzFrZnJhVHl1cUtINzJHaEtnS1lHRFErK3NrTnVsWmt4N0U1SWFFZDFxbU5oVStzYzNaQ1MzaC9QYXZick1XbUlJdi8rZHVTdUZEaTZGRXJCUXcxWHlVV1k1NzcvMTFjeWVXQnluOGsyaXdxcFlKRjdlSThwVzlVcWRmc2J5WXovelZHTjBJYkVOK01XMzYwVzdVdjRzbDdWOHROVUNCU3BQOTNLMWpJSlV5cGl4cTZXS3RLZkVWNGlneWdkS2lnelFlQm5PTXBsT0VKY0FrZmhkZzV0aFBWU1U2RUlEMFBvNEp2Z3JoV2FDTVZkWFc5bGVyN0NVMmZRZWNqM3BLSHBJNFhyeXZIMXg1NzFYT3JUYzZDNWpsRVNSa0YvWDVzUWxldkFwbDJEelh1YklYM1JoM3JoTlpFYlEwYTVtc3VDL2xjS0xpalJ2bUJjZTZjUmltL014MldUSUE1dU04SG5ibjZhUmlRL3IybXhyZFBuNzlySWgzRERhaFZ4bVRML01ITG5FdkJETGNXNU15dk5LWDFzQnA5Z1cyaEJuckgiLCJtYWMiOiIyNTEyNzM4MGZlNjY5NzU5Njc1YWE4OGYyYjc5MTk2ODI1ZDNhZmEyZTAwMzViM2IzNTYzMmRjMGE2Y2RiMTk3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708540769\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-287144838 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287144838\", {\"maxDepth\":0})</script>\n"}}