{"__meta": {"id": "X92db731eeb34510d2898a03fe9c896ae", "datetime": "2025-07-31 16:24:52", "utime": **********.168524, "method": "GET", "uri": "/finance/sales/contacts/customer/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979089.100752, "end": **********.168581, "duration": 3.067828893661499, "duration_str": "3.07s", "measures": [{"label": "Booting", "start": 1753979089.100752, "relative_start": 0, "end": **********.59409, "relative_end": **********.59409, "duration": 2.493337869644165, "duration_str": "2.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.594151, "relative_start": 2.493398904800415, "end": **********.168586, "relative_end": 5.0067901611328125e-06, "duration": 0.5744349956512451, "duration_str": "574ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01897, "accumulated_duration_str": "18.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.955091, "duration": 0.01396, "duration_str": "13.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 73.59}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.089191, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 73.59, "width_percent": 18.292}, {"sql": "select * from `customers` where `id` = '2' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.123637, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 91.882, "width_percent": 8.118}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/2", "status_code": "<pre class=sf-dump id=sf-dump-314542526 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-314542526\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1427944306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1427944306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1670250500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1670250500\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBmSUs2Tzdxd2hxQkNJZDF1TFZxcmc9PSIsInZhbHVlIjoiSUs3ZnZua2lRTjJ2MEE4a3A0T3VtU0JpbTQ1UFY4azU2bXVJTlJweGNROGVCMzFsZnRqcGd1VTYrV1JSN1Rpb0FDWnVDNVcyQUpUcXZBdFJDUE52UmNVNTFvU0J4ZmZra0VjekFJakNYK2VIdlB2OVFMQ2YxL0xmQmJoT3B4ZW8vNzhCaFJoaXFZRE4yL210TzJVcE95azYvYmxrNVU3VWlkYlhobHNZQWx2eTcxUEtkMElaQU4ydVRva2RSMXFkMGJsUW1Mc2ZIYzZkQWwwanRNd3R4UE1rMit2NGt0Q2ZSbENxYnVTTHY1MjEyYTdtUVBpNUxHY0tXSW5TdWtTanV0T0tlS0VlWkk4SkpWdVhJUmYraDJ6U2NyV2VoZU41RDJMK0dpbXROZWlndS94cDFNVy9VK0RLaUxWdWlFbk5pMnJLSG1GblFyV2t1WUZGWk9xTWxPeU0xOUVmbHk2M1FwaVJNWXJ6VVJzMjl5V3VCZ2tnOXhTOWhEd0FEczQxakhrMXdKT3VNQitJM2xNbGtGSVkyS1hkMng1b2FBZDNzbUdGb3cwMTVNa0VBbi9TV0lQb3lzUDY1SFYxYjI5SUVVVGIyQW9DUW5Eak81SDJyMnhSUHR5ZExWVlYwWXhhS3JwT1doMS92dWNaTlVYVThrSm9kQVN0WTJTMksvbHUiLCJtYWMiOiI3NDkwOTJhMzM1YTEyY2NlYWZkYmZmMDIyYzM3Yzg1NWYwM2QwOGYxYmFmOTJjOWY2ZDI3ZDBmYzk1M2E2OTBkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjVhMGVlQmxPbE1BTmx0eCtDZjZqMFE9PSIsInZhbHVlIjoiQktEMDZhSG1hUGRkNFhqdkQrQlVoQVRsVzNBTkdZMTNNUXc4QVRNZjdoU01BVXI4TGhNYituc0VmOHVtMHRCOE8xYjRaU2JxTUY4Sm9ZR0NOVVhXOUo2cmx4TWhUMm9yUEVxTFNiK1pFODZLMkRSNmtVM2RkczdKSHMvNmV3UkZRbHRvbi9aaXEwSHlZQVF0Y05oREVsYkFFSXdqangreU10Y2xvMHIvMmlpOG1zK0tIRHZ6bGVjdFBsOEp6VXpvOTI2TkxYUTE1Y2gvSUdSSFBrK24rbXlOMGxXY3hva1UzZkxQRm0rR3VEUnROczJNZUwveEE0anRReGVOS0QzTzc5UmU2dkhYN202blVqSnBDdG96Y3lVRWJIZTVHYlBndGFGazh2R1N2VVNPK01yTlNSY2sxblgrQU80WjBubHh1bkkrR0QyQXJoeFY1dGxrNWk0cWNZZXNXMUpTNWl5SDhwZm5YSi9RTC9ORWY4cGJ4N0l2WjR5S3daN2FFQ0ZNWmJJaWJrZEkyanpiZFRlR3ltanA2OXJodEJvdlhBSU94ZS9icU91d1pCVlRzZE5heHpJeFpyZ2lRREo4eEpha2NtbElsYjBVZUEyWitxRFNjcHVzNm0rWXB0Ui9TK1ZURVJlRTBqc1ZVRTRPUHBlcnNPQ3ZITzJGTStCaHlWUnAiLCJtYWMiOiJkNDBjMzQ2NzJiYjAwOTE2MmY4NzhmMTZiNmRkYjhlZjAyYWQyMTczMzJiOTUzMTQ5OTU2MTI5NTgwYjNkM2ZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1940905568 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:24:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlwaDAycFd3bVNLc3NaWU9hTUw0NVE9PSIsInZhbHVlIjoiU05rUFZIaW9xaFNEVlB2bW4wb2xGek4yZTBaUE9zNTd2UmxJVjRtYkJHZFdYT29wWVVDbmlUb3Y4c3RnYm8va09DSis5REdDSjlDdjJVdmJyS1EwRWtGekVMN2FGWm5ZMnJmVHFyS0JBbldIbDNWckhqcUpNLy9sdHJVeVd0QVF2aTJtOWdCUmhwT1hpMWNyRXZDVUpPczZrVlVyL2JZcWNCd2NqZXQvMHVyUUtqdGd3Y290QnhpVFdDU3hvTDRwVHQwNHlHQkhKajgxWUdBV1IraWJpRW1xdUJYc2x1VHJON0ZZN1E2YWU0OUppUEloeVRpUklJVFlFUTYxN1ZYd1VMV05UTTlTN2ZmRVFXejJiL2UvSWRFZzlHT1pmWU83MGlQZk5vaU5uTzJ1MlZLcVBWUFk4QU5TSHVQZHVsUmhmdWJYQzgwTmwvUHJmQ2JiMjZUdURBenFPaGlkWUdnaEorZ3I4M045eTV4Smw5MUZDOFdQaGRKVWNIYWJORENVbk5sRGk2YkpxcmJ1M2llZDhrN0xid3UwQTZ4K01nRitRbjA2aWF0M0hsbDU3dnJsQTdPRGFoRnpvR0hDSGlkRDVweW8zemc1dC9YQUVFbVlmQk5ZaHo3bytmcjhCRkdTdjd1SG9kd2g0OHA5by83VDJzSVdUUXllRVNmRVo0WUYiLCJtYWMiOiJhZDVhY2JmN2Q1OGUyZGE4YmQ1NThiYmE1ZDBkYzJmZGU5NTQ2NTdkMDMwZWRhYTdhZmU4ZTEyY2ZmMTNhMDg0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:24:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIyME1mcUxBZ1Rmcnd6cXNTSHBUZXc9PSIsInZhbHVlIjoicGgrWkQ1UGQzdlgxQjVhaWl5R1lkS1V2YlBUR2lqU1lUS1ZPUDE3cHI1ZUVYZi9vZHc0d0F2SXBkazdZSG8wTHZXZzVTd0lPTHl4K3dKdFRYakI0Rjhob3VjVjNrZFBRMnAveTB3cW1yN2xVeC96Z1ZUVFZEdHcxR3lnZW9SdkhVVXR4bEY4eTVCc0ZPdmF0NnZMc1lGelBwY3p1czhOQ2tiajl0RXNUbTMxVGNBQmtMTVhKdzh3c2c1dHRJZ2I5K3l6Y2lGK0dWTTFTbFJFU3YvWnJ2YkZFbzlNZzN0eWd1ZWxHNHpYM05kTjVwODQvN2VGenNhbmRIZm90S1R4TmJwRkdUOWhvN1o2ZWNDUkM4S00xN0t1STFTTTgwTzFlb0V1SU0vUTdaUDYwQVVRZFA3Y1VtaUNHK3lLb1hVMEFvSy91OGNUZzNNUjQ1UUJ1bDlwZ0Qzc3YyOTlhUVd2dHJjd2I4NGlEb1lGaGY0WDlGcVMxTWVBSU9pbHE5Q1krQnpuTDBEanJsdmcwZnNmY0hqbDJJL0JoUE44RENQcm1sTzNLUjM5UHBHR1U1UkhIU1I4YmFndzFNS0ZTL1VjVXNWaE1udk40UHFJYjNuemkraW4xQk9jbHF4YzZYa3FCS1d1Uk94aXl1em5lNzhrcE8yMXJRTDNZVWt0OVdKd1kiLCJtYWMiOiI5ZmFiZWQ5YmYzZmJiMzA1MjZkNzk4NmNkNmJhMDBhYzAzZGYzYzE0OGVkMDQ0NWE0ZTQwMGRiMDA4ZTRlNGZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:24:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlwaDAycFd3bVNLc3NaWU9hTUw0NVE9PSIsInZhbHVlIjoiU05rUFZIaW9xaFNEVlB2bW4wb2xGek4yZTBaUE9zNTd2UmxJVjRtYkJHZFdYT29wWVVDbmlUb3Y4c3RnYm8va09DSis5REdDSjlDdjJVdmJyS1EwRWtGekVMN2FGWm5ZMnJmVHFyS0JBbldIbDNWckhqcUpNLy9sdHJVeVd0QVF2aTJtOWdCUmhwT1hpMWNyRXZDVUpPczZrVlVyL2JZcWNCd2NqZXQvMHVyUUtqdGd3Y290QnhpVFdDU3hvTDRwVHQwNHlHQkhKajgxWUdBV1IraWJpRW1xdUJYc2x1VHJON0ZZN1E2YWU0OUppUEloeVRpUklJVFlFUTYxN1ZYd1VMV05UTTlTN2ZmRVFXejJiL2UvSWRFZzlHT1pmWU83MGlQZk5vaU5uTzJ1MlZLcVBWUFk4QU5TSHVQZHVsUmhmdWJYQzgwTmwvUHJmQ2JiMjZUdURBenFPaGlkWUdnaEorZ3I4M045eTV4Smw5MUZDOFdQaGRKVWNIYWJORENVbk5sRGk2YkpxcmJ1M2llZDhrN0xid3UwQTZ4K01nRitRbjA2aWF0M0hsbDU3dnJsQTdPRGFoRnpvR0hDSGlkRDVweW8zemc1dC9YQUVFbVlmQk5ZaHo3bytmcjhCRkdTdjd1SG9kd2g0OHA5by83VDJzSVdUUXllRVNmRVo0WUYiLCJtYWMiOiJhZDVhY2JmN2Q1OGUyZGE4YmQ1NThiYmE1ZDBkYzJmZGU5NTQ2NTdkMDMwZWRhYTdhZmU4ZTEyY2ZmMTNhMDg0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:24:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIyME1mcUxBZ1Rmcnd6cXNTSHBUZXc9PSIsInZhbHVlIjoicGgrWkQ1UGQzdlgxQjVhaWl5R1lkS1V2YlBUR2lqU1lUS1ZPUDE3cHI1ZUVYZi9vZHc0d0F2SXBkazdZSG8wTHZXZzVTd0lPTHl4K3dKdFRYakI0Rjhob3VjVjNrZFBRMnAveTB3cW1yN2xVeC96Z1ZUVFZEdHcxR3lnZW9SdkhVVXR4bEY4eTVCc0ZPdmF0NnZMc1lGelBwY3p1czhOQ2tiajl0RXNUbTMxVGNBQmtMTVhKdzh3c2c1dHRJZ2I5K3l6Y2lGK0dWTTFTbFJFU3YvWnJ2YkZFbzlNZzN0eWd1ZWxHNHpYM05kTjVwODQvN2VGenNhbmRIZm90S1R4TmJwRkdUOWhvN1o2ZWNDUkM4S00xN0t1STFTTTgwTzFlb0V1SU0vUTdaUDYwQVVRZFA3Y1VtaUNHK3lLb1hVMEFvSy91OGNUZzNNUjQ1UUJ1bDlwZ0Qzc3YyOTlhUVd2dHJjd2I4NGlEb1lGaGY0WDlGcVMxTWVBSU9pbHE5Q1krQnpuTDBEanJsdmcwZnNmY0hqbDJJL0JoUE44RENQcm1sTzNLUjM5UHBHR1U1UkhIU1I4YmFndzFNS0ZTL1VjVXNWaE1udk40UHFJYjNuemkraW4xQk9jbHF4YzZYa3FCS1d1Uk94aXl1em5lNzhrcE8yMXJRTDNZVWt0OVdKd1kiLCJtYWMiOiI5ZmFiZWQ5YmYzZmJiMzA1MjZkNzk4NmNkNmJhMDBhYzAzZGYzYzE0OGVkMDQ0NWE0ZTQwMGRiMDA4ZTRlNGZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:24:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940905568\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400659304 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400659304\", {\"maxDepth\":0})</script>\n"}}