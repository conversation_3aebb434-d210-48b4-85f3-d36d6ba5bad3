{"__meta": {"id": "X337b4586796cbe1398a4789d869e3ceb", "datetime": "2025-07-31 16:26:57", "utime": **********.959621, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979214.250871, "end": **********.959689, "duration": 3.708817958831787, "duration_str": "3.71s", "measures": [{"label": "Booting", "start": 1753979214.250871, "relative_start": 0, "end": **********.45118, "relative_end": **********.45118, "duration": 3.2003090381622314, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.451233, "relative_start": 3.200361967086792, "end": **********.959696, "relative_end": 7.152557373046875e-06, "duration": 0.5084631443023682, "duration_str": "508ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421568, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.010310000000000001, "accumulated_duration_str": "10.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7662, "duration": 0.0061600000000000005, "duration_str": "6.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 59.748}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.814289, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 59.748, "width_percent": 18.526}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8423371, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 78.274, "width_percent": 21.726}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-115381202 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-115381202\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1156558517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1156558517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-36290783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-36290783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-462478826 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ink3aGZ3WVplNk9KR2dEbXZFTUg1NkE9PSIsInZhbHVlIjoiMDhaZVZ6QVVPRTF3QmFZaXI5VXZLQmZmUGpDVVBHZ1FzdktmQ3pQeVNMa095SVdtQ1ozaG05S0NQV1FTbkJvVXJUak1Xejd1aDJUSFlCWFI4Wm1SMUR1dWhuSnBMVXduNGFaVkFQWjJSdXBFVm9Pd1pHNmdrQk5OKzBGbVgwb3R1REdyRUtNdW5yVC9XY3ZiU1FkNnJ0dWZORmJ1M1drNDNudk9OK1lCS093L2JudEtzUHA1dFliWWY3V1VIcnJUa05JdmttbzVTa1lBbXA1L1JvTW9ZbGF3MUxQVHBZTjRYeWlvNzFyYXBrV2x1QnNyVGhBU05Bcm1VR2J3Qk1nYUpJMHdIdEFIYy81ajZrUEdJTEc5dnNpV1ozZ0xwQno1ZGJJRWZTVTA5QjRwS0FzNXd0QzNFdWJLb25uaWgxSm81cld3cHRtNVcrTDcyUmtrTjQxNXpiOTA2cFdBVTFrOUMxWE16d2hQMEczcVh2RlF5SWxyc1Y4RkhBVEVmUGZRTEJTb285SVIxZGNCQ2laWEJaaFRDb0pHNFRoV3VYTjlmNEJweTVaQk9keUhtNXZ6ZzlBRTdKbXBQN1llVUpKVjFSYUNDdHFMSGZENS9SZS9WV2J6d2dLcnJFbi96cEN6OTlsb09WcFJjZjFDdTh3UnZkdHVoU3prWmFMamE0b00iLCJtYWMiOiIzOTdiZTZlNjRiZDA4YjI0MDE3MmM0OGY5MTU5YmI1YWMyOGY5NWVkOWNhODU3ZDJhM2RkZjU4YzkwZjBhZjNhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImpGMk05cUY5VXk4WmY0UjNrYVhwblE9PSIsInZhbHVlIjoiUmM2NVdMTkovRWJWRnJmbllqdHF5UEFuMk8rZHNseXExNitSMFhOdGJJOFYwY2VhMWFWNCt4WnJBN1FvUWZRdDFGekwrOEFWdzQrWEU2aDU1UVhxNTBoWmRSL0x3RHZieXBZUU5jV0xXTk1YZWorV2pKVkpOVEZ5NHZHd0JlUDdJUlhNS1QvWnd6dEJ4eWk2cHFlSTgzZm1tbDJ0Yms0ZFBXY2FKUWYvUk1ybHhUdllDOWpMWFB3d2JYVW16eHNJWWZCUjZEZ0RDVERhWXg3L1hnMzBRQXlYSStMYUdqcHRERzQ2Q1lkSWRoc3ZnRkVtV2MxbFRMUHd3WTczdVlaOG4rd0ErVmFLbkJUZGVQZTZjeGU4UGNJcVpiNXFaQWtXWDJacEwydHFGVzdHTEdxMUg4SWkzZzJMMWlmTThpR0RtZ0RCQlRGRytGdnNNUDEyb1VkWjVEWnBkSGkyMHNYa1o2ZG41SWxSUzJtMHlhemlxL2VSQmhJZXhXYVZYRndNZGlWeTJOaTBFSzQrdnQ5ZmhmT1lMZi9RSDNlRFJxQmxiOG1od1B4MlZReXUxbkNCYVZLNXpSSGhpTElMVDdUVHJSYXFNbXgwekVSTnQ0S1NIK1MwUjEzeHFwYkFaYkhEZE8yQ1duTEU0aXFVdUdxYVZ6NkY0V0NDaDRCM1NXUXQiLCJtYWMiOiIyNDJhYjlhZjVmNzYwZjI0ODg2OTcyOTc4NTY2NjY2MzI1ZTEwMzIwMWYzYzA0YjVhN2UxODgzNGRiYmMwODdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462478826\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1740372129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740372129\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-108858476 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:26:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllNZDkzblBrSmlER2wrUzFIUzlPcVE9PSIsInZhbHVlIjoienVSVHdqU2hWejh0cXJ0S0svUHBrdGJDVUgwKzJLTGlZTmRzWnFlVDFHVG92UnZlSFNJTUhtRTNxTFNtclo2WndWRWtiMEFjVlNNRThIN0lrUzJEK29RTXJvOGlpSm1GZG9EZDFWSE5tQlJJcmJiRlgrQW1vTUxUS3ZMRUR2SEwzcWVjYnhYWmhFcGtKaVdRRHRzWmlEUUlnYUROd0Fxb05id1VrZTZHK0VZaGV5VFQ3Ly9qVXJlZVM2eE01VklZdDd3M05zRUtTc3lGTlZUR1RXTDN3cG5BaXQwcmdNWVhXMW1PT1k3NUo2R2dycXZVM20vaWEvbndSeVhWUEZ1MkRpU2xYNHdnZVpYeVN3NTE2OUxEWWszQ2NDUG9GUVB3TjhUUWlpLzJOdmxRTjBVZkFnVlJaRDV6U1JvR0UvaWJMcDZwdXcwQTlFYUZ3SFFKaGNCenowM2IzN0JaRHJ3QnNyL1JpNytwT09ydGtxd09MMzExY2cwdWpjcWFJc1FVa1BLWGV5V3FtR2NpM1hTajBkMWo3QmhjR2dlWE9uWE5ZRVVYd1lnUXdabWUzR0xhMDFrVHdKWmZvak96QWthU3lWMFVSbFRwQzlibzhQZ05XSndKZkdEcHVRb1pRQW91SHpMUEEyZXJGZ0RibDhKbzBaSk5hbVRrc29iUkl6cDYiLCJtYWMiOiIyYTY4Yzk1YjY4MjllZjg1MzhhYTEwYjBiMDdjZjRmNmY0ZmMxNmM2MjY0OTAxMmFkOGVkYmZmYjJmNTFkYjY5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikt0YTRuSzF4RHlzV3RTTFF3dzVvYnc9PSIsInZhbHVlIjoiVytJbm9TOTVrQzdYWjE1NFU4RkRaTTUyVzZ1N3N0c1FTZlNaN0gvN08xMnd3eGRwNHVkeXpxSDN4Zk9MTHFKOUJydjZjelMzV1RyK3E3emluem11UFFFaFkwSC9BZ05ObzZ5bjJhbFpJRTZEd3phYWJUT0VNQjFCUHdkRFZrV2hmMUNBbkk3UWZUL0VKN1RPV1lJM0xITEtpVHU3NExYQkQrZEZCNUFYZUtkclVNN2hQMHF2MldKcDc2UlFreDhKaUJCbDJkbzZnNHZWcEgvWFczOGUwYXhEMkFiZnJkTjR0L3c0VUZ6WUcvS2hsb0tpZjgzc29YRlpYMjIzd3Y1OGFTZkxHMzJrS0NBSzgxNzJsbjFwQVQ2UW1PeWR0dVhQaVRjejR1NTFlT1oxcldmSSs0OWlJb1c5T2ZFRFBVOHVyZjJXd1c1UlFtZGZNQzZYdVJNYUJtMjduRXFXb3I3TCtyTGpwSzgyUzRDd216Ym5JSzdDYmRRbFBGdS83ODBPY2dJTmdGY2laa1ZCcFA2V3dkYXRrWk1JSkZzMnluNUxaMUQ4RlJMaHpoZFRZVjFvM29XbzVyck5hWElRNXJOY0FPcHd0M2Vtc0VBSjVPQVV6aTNkMkNyRktyWTFGUGtRSkJ6WTQvSCt1c3NiY25Oa2xKbXU3UjBnbXUzWFhOMzQiLCJtYWMiOiI2YjI1MzI3NmM2ZTA4YWE3YmI0MDUyZmQ4MDkxYzA5N2I5YzA5YjU1ZjRjNmUwYjQ1MDdmZmIwMTljMzQ5MGM3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllNZDkzblBrSmlER2wrUzFIUzlPcVE9PSIsInZhbHVlIjoienVSVHdqU2hWejh0cXJ0S0svUHBrdGJDVUgwKzJLTGlZTmRzWnFlVDFHVG92UnZlSFNJTUhtRTNxTFNtclo2WndWRWtiMEFjVlNNRThIN0lrUzJEK29RTXJvOGlpSm1GZG9EZDFWSE5tQlJJcmJiRlgrQW1vTUxUS3ZMRUR2SEwzcWVjYnhYWmhFcGtKaVdRRHRzWmlEUUlnYUROd0Fxb05id1VrZTZHK0VZaGV5VFQ3Ly9qVXJlZVM2eE01VklZdDd3M05zRUtTc3lGTlZUR1RXTDN3cG5BaXQwcmdNWVhXMW1PT1k3NUo2R2dycXZVM20vaWEvbndSeVhWUEZ1MkRpU2xYNHdnZVpYeVN3NTE2OUxEWWszQ2NDUG9GUVB3TjhUUWlpLzJOdmxRTjBVZkFnVlJaRDV6U1JvR0UvaWJMcDZwdXcwQTlFYUZ3SFFKaGNCenowM2IzN0JaRHJ3QnNyL1JpNytwT09ydGtxd09MMzExY2cwdWpjcWFJc1FVa1BLWGV5V3FtR2NpM1hTajBkMWo3QmhjR2dlWE9uWE5ZRVVYd1lnUXdabWUzR0xhMDFrVHdKWmZvak96QWthU3lWMFVSbFRwQzlibzhQZ05XSndKZkdEcHVRb1pRQW91SHpMUEEyZXJGZ0RibDhKbzBaSk5hbVRrc29iUkl6cDYiLCJtYWMiOiIyYTY4Yzk1YjY4MjllZjg1MzhhYTEwYjBiMDdjZjRmNmY0ZmMxNmM2MjY0OTAxMmFkOGVkYmZmYjJmNTFkYjY5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikt0YTRuSzF4RHlzV3RTTFF3dzVvYnc9PSIsInZhbHVlIjoiVytJbm9TOTVrQzdYWjE1NFU4RkRaTTUyVzZ1N3N0c1FTZlNaN0gvN08xMnd3eGRwNHVkeXpxSDN4Zk9MTHFKOUJydjZjelMzV1RyK3E3emluem11UFFFaFkwSC9BZ05ObzZ5bjJhbFpJRTZEd3phYWJUT0VNQjFCUHdkRFZrV2hmMUNBbkk3UWZUL0VKN1RPV1lJM0xITEtpVHU3NExYQkQrZEZCNUFYZUtkclVNN2hQMHF2MldKcDc2UlFreDhKaUJCbDJkbzZnNHZWcEgvWFczOGUwYXhEMkFiZnJkTjR0L3c0VUZ6WUcvS2hsb0tpZjgzc29YRlpYMjIzd3Y1OGFTZkxHMzJrS0NBSzgxNzJsbjFwQVQ2UW1PeWR0dVhQaVRjejR1NTFlT1oxcldmSSs0OWlJb1c5T2ZFRFBVOHVyZjJXd1c1UlFtZGZNQzZYdVJNYUJtMjduRXFXb3I3TCtyTGpwSzgyUzRDd216Ym5JSzdDYmRRbFBGdS83ODBPY2dJTmdGY2laa1ZCcFA2V3dkYXRrWk1JSkZzMnluNUxaMUQ4RlJMaHpoZFRZVjFvM29XbzVyck5hWElRNXJOY0FPcHd0M2Vtc0VBSjVPQVV6aTNkMkNyRktyWTFGUGtRSkJ6WTQvSCt1c3NiY25Oa2xKbXU3UjBnbXUzWFhOMzQiLCJtYWMiOiI2YjI1MzI3NmM2ZTA4YWE3YmI0MDUyZmQ4MDkxYzA5N2I5YzA5YjU1ZjRjNmUwYjQ1MDdmZmIwMTljMzQ5MGM3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108858476\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2082038193 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082038193\", {\"maxDepth\":0})</script>\n"}}