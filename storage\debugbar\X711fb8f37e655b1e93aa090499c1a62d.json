{"__meta": {"id": "X711fb8f37e655b1e93aa090499c1a62d", "datetime": "2025-07-31 15:53:22", "utime": **********.351781, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977199.689524, "end": **********.351816, "duration": 2.662292003631592, "duration_str": "2.66s", "measures": [{"label": "Booting", "start": 1753977199.689524, "relative_start": 0, "end": **********.420012, "relative_end": **********.420012, "duration": 1.7304880619049072, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.420039, "relative_start": 1.****************, "end": **********.351819, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "932ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02465, "accumulated_duration_str": "24.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.727274, "duration": 0.02465, "duration_str": "24.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-657076862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-657076862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-316886573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-316886573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-819094633 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNTNThucnFVNm5nL2ZMQlBISkQ4eUE9PSIsInZhbHVlIjoibzJBZ3cvVGdwcDI3RHdKWXFMd0hzVzloZnlUZnFqVWxVa2c4eEIwNW9qN0t0cEw1WS9NN3liaFFnazhiK3pMQVBGLys3dHJ1RWMvcDNsNlJmb0ZhL25kRGxDWURuSzBudTNXQUhZOEIyNVIrcWtqT3c4UUdvN0pXTHliRnZSNUN2WWpSakpLcmZaaFVWZGk5SnoxQldrMkgyOFV6TmtiRGRhOVZrRGhTL1B3R2hpd0NJT3dsRS9zUE1XUko1UzVJYlI3S0dZQlgrTWlTTzNwSGdiWkJBc09RQkM2Q05RZ2FWMDg3cmRtcXZsQXpXYVVKWDluT0xUUk1NZzBMOWE5WFBYNkxBRGJYQTNoRldMdWdLaVNYcVB6cUxOaldkZm1DZTJHT0E4RWR1NUMvOTB5NElVV2dJTU1maEl2dkxBUS9oYWhTY28zK3RMcGQzSWJ1TjFaZGJzaUUrMU5odU9NTXh2bTlCb2RyM252b3ZkQ0d1K1FyellwaVBtME0xRFV2RVdKVzY4aTFCb0pIMmtJWEhKblgweVJYZGZIM2Y3NHk4aTBXTEMvbEVSN1NVVzdqN1lybVgzNjlSdTVTdGxlcDdFaUNya0ZlYk0yV2k0cnI4R2s4bU9BaGhudVBnenVMdEQ0MlJ0elBKcXQwSktLbXluTGkyMWpxaHhTaUY2V1IiLCJtYWMiOiIxODQ1NGVhOWJjYmNkYjA4MDI2MDYyOWFlNTc0NDIxMDlmYmQzMzdkN2UzMDA1NzUxZTU5YmNiZTliMjM2MTFiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjllUDI5dm9kc3dHaGJYTVUyVUptcWc9PSIsInZhbHVlIjoiSi9VTEdPeUJGVWl2cmFkWWYxbGx4Y2V6TmxYZHphS2hGS3hBU1FLblB1WEFPS0ZpNDJqTE1QcE9lR2pEV3FBVDZJdUtjZVNJVno4TzB0U29nR1ZvRnd1dDZVYTZ2RXlERFhISXFScFk1UlJMYTZyTW84THBtTTkvMkkzL0lnSVJwem51ZjJiWU0zTjZkTFpWbCt2a1JaNUVXQVMvaFdsNWZCckVrSWY5Wk9MdUV5V1dFVyt1M01QUGlhek1Lb1l4VGkvVUVNOWhublFITFA4TVVMc21nTlh0Zi8rUmwyMlVveWR5b1JlSkJlc2lEVDVnVzV1TVdiY0VzbHplL2h1Ti9oWE5qUE9OSWJmdVdjUUx2M0RCR1FqQ1pNNnUwMDNCVTdvRGZScHpiTE5pZnBjcVovNkJrK3BzZlBHMGlqb1Z4b1h2em5xOHRQKyt2dkwzTENPeGFNSERjdk1TOVpFR1NqSmk1c01DeEtKcjRVV0UwQ2piR0FMWVFTYUxoR1RpL1ZySHNVNkM5R0N5VnNTeURXNmtzalBSTmdNMkN2VWJJYzJraXBCU0dFSDYrdy9zT3lsenRrYU5NRWhneTJUTTYxVnFVMDBLR3BNaE9CSHdqSFR3aVo3WU1GUWpYcW5yb29adUE1NXAzUWhtVStkRWQ2bmxwdmdYWHg2WkZpbTciLCJtYWMiOiI1MTY0NzQ0NDFlYjhiZmMyYTM3NjFiYjlkZjQ0MTk2NzU3NTRkODc5MTNlODVjNWQwYTk1ZDA5MzdiNGM2NzI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-modified-since</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819094633\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-410260969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410260969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1538517004 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:53:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitTSVNLd2tHOVhqem5FUUNiakZvRnc9PSIsInZhbHVlIjoiNzVYaEFLR0xlSjgrM3lJdXc5SXgvaElOQ2FHYXphVXJWV2w1bDMxa0N0b3BQZjE2NWdvSTZoOCtmM2xvTEpVOVl6bjBvQVpneU40djZ0Zks1NTROYk41bkdLdGtoZmd2UnJ2cHVHYno5RE5YRnJ6b2RGRDYvVllpZWQyZ3NHcE5ramNaTW56ZFZNR21TV285OUtqeGQvckhBTlNLbDRSQ0xPV1l1OWlrRm5FOVFJamFhenRocFFBVWxmVEdjTjBLSGJTWDdDN05QU2E2NGszU21oQVh0eE1nTEFkUjdwd3R0VDUwb1orbnB5cnNzRzVYNXBuUWpqY1ArejROWFVzaDNPTWF6ajFoWG4zcEpUdFU1OEx1cjJxVE41eXdWcXAxZENhUE4zWThtSW5maU85NldpeW1UZEZXSjYySVcwZCtWT2hxdDVWWGQyWHJJNGpmKzJMTlJqRHZ2eUJQaDFIL2dNZkhRNWQwKytMTEZjYzV3Zld0MnA0dkpJblRaUGxDczdyUzEvUnJ0ckFOTk1qemRaMEZhSlBMRktyK09QQVo0VnViSEt0WW1WRlhqbjBRWVlHV0JRVGY1VnVtZnJtVnd3Z0pDcklCTmtoY1BnL0FyUk4zSWZnanpwSm5yZ09LeDNIaVNRazJwMmNXbkd5SVl5N1RIVDl5N2h2MXFoSUciLCJtYWMiOiJlZGViZTMzOTEwZmQ4YjkzYTJmNDMzYmRlY2EwMmQ4NzkwYWQwNjlmMThjZTk5MGIzMDQ3Mzk4MGY3NmY0MTExIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1KTG0xc1pQOTBBZ0QrdHdxNHIzanc9PSIsInZhbHVlIjoia1NGU0lYaXk4NzUzVVM1K1BMcEJOc0I3QWVqK1UzWlJXMzU3djIzTDZzczRhd2tXNVZHa1VKVTRvd3FvQmFBVjVISXhrb2hVakdySUZGaVFrQVlSTzZCbGt2TnBlZ0lpc0w3aUJVTG51aUdzK1g0dUt2L1RkZk5MbmhEa1oyVEN6MDR2dEFqSnpJcUZ0dkpwNTZYZVdZakpyTldlY1ZjVVpxWlJMbXhxNCtvMzhZb1dQWnErZVhRdFpPOEc2SUtlL3BHMWxYYlErWnp0WjlWL0VTK21MK29SMnJLZHY0TWl3bmo5SjJrUlp0cGdjMXZJOEt0Z1hJMHNKK0lKbzBKMzlsUXJaQTRjK1NzRjRhWlFHWGJsTEFhejFrK1VySVNyMVlyS0lxczZRV3o0djRFcEdjSG5HUzBYR09IOUVLendtRDRyaDM5OXVKWmNjQitvSnVUekhNWlVrNkUzUzNrYUQxR3c1Y0k5SXY4N2Z4OGRyVHJhamFzWkJEcXFDTmhkWXJVT0tvMGRxQXhGdE45Y216S0pheC9MUm04WUY2aGh1VzcwWDlocXhxcWZLUWJmMmxHci9jYjR6REdzT2h5NzVJM0M4RkpBcENNYTltQlVTS2Z1NnZLSGc0Z3BFWmllOHEzZlRlUTNPN2tnSEVseExsaGhEUHJLZ1pMbERuNzEiLCJtYWMiOiI0ZTFhZGY5ODdiMTNhZGU1NGZlMzI2ZjdkMTE0ZjZmNjYwZDdhODdkOTY2YWQ5NGY1ODM3ZjA0ZDk5MzEzZWQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitTSVNLd2tHOVhqem5FUUNiakZvRnc9PSIsInZhbHVlIjoiNzVYaEFLR0xlSjgrM3lJdXc5SXgvaElOQ2FHYXphVXJWV2w1bDMxa0N0b3BQZjE2NWdvSTZoOCtmM2xvTEpVOVl6bjBvQVpneU40djZ0Zks1NTROYk41bkdLdGtoZmd2UnJ2cHVHYno5RE5YRnJ6b2RGRDYvVllpZWQyZ3NHcE5ramNaTW56ZFZNR21TV285OUtqeGQvckhBTlNLbDRSQ0xPV1l1OWlrRm5FOVFJamFhenRocFFBVWxmVEdjTjBLSGJTWDdDN05QU2E2NGszU21oQVh0eE1nTEFkUjdwd3R0VDUwb1orbnB5cnNzRzVYNXBuUWpqY1ArejROWFVzaDNPTWF6ajFoWG4zcEpUdFU1OEx1cjJxVE41eXdWcXAxZENhUE4zWThtSW5maU85NldpeW1UZEZXSjYySVcwZCtWT2hxdDVWWGQyWHJJNGpmKzJMTlJqRHZ2eUJQaDFIL2dNZkhRNWQwKytMTEZjYzV3Zld0MnA0dkpJblRaUGxDczdyUzEvUnJ0ckFOTk1qemRaMEZhSlBMRktyK09QQVo0VnViSEt0WW1WRlhqbjBRWVlHV0JRVGY1VnVtZnJtVnd3Z0pDcklCTmtoY1BnL0FyUk4zSWZnanpwSm5yZ09LeDNIaVNRazJwMmNXbkd5SVl5N1RIVDl5N2h2MXFoSUciLCJtYWMiOiJlZGViZTMzOTEwZmQ4YjkzYTJmNDMzYmRlY2EwMmQ4NzkwYWQwNjlmMThjZTk5MGIzMDQ3Mzk4MGY3NmY0MTExIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1KTG0xc1pQOTBBZ0QrdHdxNHIzanc9PSIsInZhbHVlIjoia1NGU0lYaXk4NzUzVVM1K1BMcEJOc0I3QWVqK1UzWlJXMzU3djIzTDZzczRhd2tXNVZHa1VKVTRvd3FvQmFBVjVISXhrb2hVakdySUZGaVFrQVlSTzZCbGt2TnBlZ0lpc0w3aUJVTG51aUdzK1g0dUt2L1RkZk5MbmhEa1oyVEN6MDR2dEFqSnpJcUZ0dkpwNTZYZVdZakpyTldlY1ZjVVpxWlJMbXhxNCtvMzhZb1dQWnErZVhRdFpPOEc2SUtlL3BHMWxYYlErWnp0WjlWL0VTK21MK29SMnJLZHY0TWl3bmo5SjJrUlp0cGdjMXZJOEt0Z1hJMHNKK0lKbzBKMzlsUXJaQTRjK1NzRjRhWlFHWGJsTEFhejFrK1VySVNyMVlyS0lxczZRV3o0djRFcEdjSG5HUzBYR09IOUVLendtRDRyaDM5OXVKWmNjQitvSnVUekhNWlVrNkUzUzNrYUQxR3c1Y0k5SXY4N2Z4OGRyVHJhamFzWkJEcXFDTmhkWXJVT0tvMGRxQXhGdE45Y216S0pheC9MUm04WUY2aGh1VzcwWDlocXhxcWZLUWJmMmxHci9jYjR6REdzT2h5NzVJM0M4RkpBcENNYTltQlVTS2Z1NnZLSGc0Z3BFWmllOHEzZlRlUTNPN2tnSEVseExsaGhEUHJLZ1pMbERuNzEiLCJtYWMiOiI0ZTFhZGY5ODdiMTNhZGU1NGZlMzI2ZjdkMTE0ZjZmNjYwZDdhODdkOTY2YWQ5NGY1ODM3ZjA0ZDk5MzEzZWQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538517004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1778447021 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778447021\", {\"maxDepth\":0})</script>\n"}}