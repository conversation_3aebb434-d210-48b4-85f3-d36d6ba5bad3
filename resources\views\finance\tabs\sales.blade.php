<style>
    .sales-view {
        display: none;
    }

    .sales-view.active {
        display: block;
    }

    /* Customer search dropdown styling */
    #customerDropdown {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1050;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #fff;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        display: none;
    }

    #customerDropdown.show {
        display: block !important;
    }

    .customer-option {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f8f9fa;
        text-decoration: none;
        color: #212529;
        display: block;
        cursor: pointer;
    }

    .customer-option:hover {
        background-color: #f8f9fa;
        color: #212529;
        text-decoration: none;
    }

    .customer-option:last-child {
        border-bottom: none;
    }

    #customerNameSearch {
        position: relative;
    }

    .position-relative {
        position: relative;
    }

    .dropdown-item-text {
        padding: 0.75rem 1rem;
        color: #6c757d;
    }
</style>

<!-- Sales Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Sales Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success active" data-sales-view="subscription">
                    <i class="ti ti-repeat me-1"></i>{{ __('Subscription') }}
                </button>
                <button type="button" class="btn btn-outline-success" data-sales-view="installment">
                    <i class="ti ti-calendar-time me-1"></i>{{ __('Installment') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Subscription View -->
<div id="subscription-view" class="sales-view active">
    <div class="row mb-4">
        <!-- Subscription Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-users" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small>{{ __('Active Subscribers') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                    <small>{{ __('Monthly Recurring Revenue') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small>{{ __('Pending Subscriptions') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small>{{ __('Churn Rate') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Subscription Plans') }}</h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                        <i class="ti ti-plus me-1"></i>{{ __('Create Plan') }}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="subscriptionsTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Status / Created At') }}</th>
                                    <th>{{ __('Next EMI Date') }}</th>
                                    <th>{{ __('Customer Name / Email / Phone') }}</th>
                                    <th>{{ __('Product') }}</th>
                                    <th>{{ __('Product Price / Downpayment') }}</th>
                                    <th>{{ __('Paid Amount / Pending Amount') }}</th>
                                    <th>{{ __('Discount Amount') }}</th>
                                    <th>{{ __('Receipt') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    try {
                                        $subscriptions = \App\Models\Subscription::where('created_by', \Auth::user()->creatorId())->orderBy('created_at', 'desc')->get();
                                    } catch (\Exception $e) {
                                        $subscriptions = collect([]);
                                    }
                                @endphp
                                @forelse($subscriptions as $subscription)
                                <tr>
                                    <td>
                                        <strong>{{ $subscription->subscription_id }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge {{ $subscription->getStatusBadgeClass() }} mb-1">
                                            {{ $subscription->getFormattedStatus() }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ $subscription->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        @if($subscription->next_emi_date)
                                            <span class="text-{{ $subscription->isOverdue() ? 'danger' : 'primary' }}">
                                                {{ $subscription->next_emi_date->format('M d, Y') }}
                                            </span>
                                            @if($subscription->isOverdue())
                                                <br><small class="text-danger">{{ __('Overdue') }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">{{ __('N/A') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $subscription->customer_name }}</strong><br>
                                            <small class="text-muted">{{ $subscription->customer_email }}</small><br>
                                            @if($subscription->customer_phone)
                                                <small class="text-muted">{{ $subscription->customer_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $subscription->product_name }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ \Auth::user()->priceFormat($subscription->product_price) }}</strong><br>
                                            <small class="text-muted">{{ __('Down:') }} {{ \Auth::user()->priceFormat($subscription->down_payment) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="text-success">{{ \Auth::user()->priceFormat($subscription->paid_amount) }}</span><br>
                                            <span class="text-warning">{{ \Auth::user()->priceFormat($subscription->pending_amount) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-info">{{ \Auth::user()->priceFormat($subscription->discount_amount) }}</span>
                                    </td>
                                    <td>
                                        @if($subscription->receipt_url)
                                            <a href="{{ $subscription->receipt_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-file-text"></i>
                                            </a>
                                        @else
                                            <span class="text-muted">{{ __('N/A') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ti ti-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="cancelSubscription({{ $subscription->id }})">
                                                        <i class="ti ti-x me-2"></i>{{ __('Cancel Subscription') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="showSubscriptionNotes({{ $subscription->id }})">
                                                        <i class="ti ti-notes me-2"></i>{{ __('Subscription Notes') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="openWhatsAppChat('{{ $subscription->customer_phone }}')">
                                                        <i class="ti ti-brand-whatsapp me-2"></i>{{ __('WhatsApp Chat') }}
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editSubscription({{ $subscription->id }})">
                                                        <i class="ti ti-edit me-2"></i>{{ __('Edit') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteSubscription({{ $subscription->id }})">
                                                        <i class="ti ti-trash me-2"></i>{{ __('Delete') }}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-repeat" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Subscription Plans') }}</h5>
                                            <p>{{ __('Create your first subscription plan to start recurring billing') }}</p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                                                <i class="ti ti-plus me-1"></i>{{ __('Create Plan') }}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Installment View -->
<div id="installment-view" class="sales-view" style="display: none;">
    <div class="row mb-4">
        <!-- Installment Stats -->
        @php
            try {
                $activePlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'active')->count();
                $totalValue = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->sum('total_amount');
                $overduePlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'overdue')->count();
                $completedPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->where('status', 'completed')->count();
                $totalPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->count();
                $collectionRate = $totalPlans > 0 ? round(($completedPlans / $totalPlans) * 100, 1) : 0;
            } catch (\Exception $e) {
                $activePlans = 0;
                $totalValue = 0;
                $overduePlans = 0;
                $collectionRate = 0;
            }
        @endphp
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-calendar-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ $activePlans }}</h4>
                    <small>{{ __('Active Plans') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-currency-dollar" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat($totalValue) }}</h4>
                    <small>{{ __('Total Value') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock-exclamation" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ $overduePlans }}</h4>
                    <small>{{ __('Overdue Payments') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-percentage" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ $collectionRate }}%</h4>
                    <small>{{ __('Collection Rate') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Installment Plans') }}</h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                        <i class="ti ti-plus me-1"></i>{{ __('Create Plan') }}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="installmentPlansTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Status / Created At') }}</th>
                                    <th>{{ __('Next Installment Date') }}</th>
                                    <th>{{ __('Customer Name / Email / Phone') }}</th>
                                    <th>{{ __('Product') }}</th>
                                    <th>{{ __('Product Price / Downpayment') }}</th>
                                    <th>{{ __('Paid Amount / Installment Amount') }}</th>
                                    <th>{{ __('Discount Amount') }}</th>
                                    <th>{{ __('Receipt') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    try {
                                        $installmentPlans = \App\Models\InstallmentPlan::where('created_by', \Auth::user()->creatorId())->orderBy('created_at', 'desc')->get();
                                    } catch (\Exception $e) {
                                        $installmentPlans = collect([]);
                                    }
                                @endphp
                                @forelse($installmentPlans as $plan)
                                <tr>
                                    <td>
                                        <strong>{{ $plan->plan_id }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge {{ $plan->getStatusBadgeClass() }} mb-1">
                                            {{ $plan->getFormattedStatus() }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ $plan->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        @if($plan->next_installment_date)
                                            <span class="text-{{ $plan->isOverdue() ? 'danger' : 'primary' }}">
                                                {{ $plan->next_installment_date->format('M d, Y') }}
                                            </span>
                                            @if($plan->isOverdue())
                                                <br><small class="text-danger">{{ __('Overdue') }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">{{ __('N/A') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $plan->customer_name }}</strong><br>
                                            <small class="text-muted">{{ $plan->customer_email }}</small><br>
                                            @if($plan->customer_phone)
                                                <small class="text-muted">{{ $plan->customer_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $plan->product_name }}</span>
                                        @if($plan->quantity > 1)
                                            <br><small class="text-muted">{{ __('Qty:') }} {{ $plan->quantity }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ \Auth::user()->priceFormat($plan->product_price) }}</strong><br>
                                            <small class="text-muted">{{ __('Down:') }} {{ \Auth::user()->priceFormat($plan->down_payment) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="text-success">{{ \Auth::user()->priceFormat($plan->paid_amount) }}</span><br>
                                            <span class="text-info">{{ \Auth::user()->priceFormat($plan->installment_amount) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-info">{{ \Auth::user()->priceFormat($plan->discount_amount) }}</span>
                                    </td>
                                    <td>
                                        @if($plan->receipt_url)
                                            <a href="{{ $plan->receipt_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-file-text"></i>
                                            </a>
                                        @else
                                            <span class="text-muted">{{ __('N/A') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="ti ti-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="cancelInstallmentPlan({{ $plan->id }})">
                                                        <i class="ti ti-x me-2"></i>{{ __('Cancel Subscription') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="showInstallmentPlanNotes({{ $plan->id }})">
                                                        <i class="ti ti-notes me-2"></i>{{ __('Subscription Notes') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="openWhatsAppChat('{{ $plan->customer_phone }}')">
                                                        <i class="ti ti-brand-whatsapp me-2"></i>{{ __('WhatsApp Chat') }}
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editInstallmentPlan({{ $plan->id }})">
                                                        <i class="ti ti-edit me-2"></i>{{ __('Edit') }}
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteInstallmentPlan({{ $plan->id }})">
                                                        <i class="ti ti-trash me-2"></i>{{ __('Delete') }}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-calendar-time" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Installment Plans') }}</h5>
                                            <p>{{ __('Create installment plans for large purchases') }}</p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                                                <i class="ti ti-plus me-1"></i>{{ __('Create Plan') }}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Quick Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('customer.index') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Customers') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('invoice.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Invoices') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('productservice.index') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-package mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Products') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('report.income.summary') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Reports') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                            <i class="ti ti-repeat mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('New Subscription') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                            <i class="ti ti-calendar-time mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('New Installment') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Subscription Modal -->
<div class="modal fade" id="createSubscriptionModal" tabindex="-1" aria-labelledby="createSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createSubscriptionModalLabel">{{ __('Create Subscription Plan') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createSubscriptionForm">
                @csrf
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary">{{ __('Customer Details') }}</h6>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerName" class="form-label">{{ __('Customer Name') }}</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="customerNameSearch" placeholder="{{ __('Search customer name...') }}" autocomplete="off">
                                <input type="hidden" id="customerName" name="customer_id" required>
                                <input type="hidden" id="customerType" name="customer_type">
                                <div class="dropdown-menu w-100" id="customerDropdown" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Dynamic customer options will be loaded here -->
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">{{ __('Search from Customers and Leads') }}</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerEmail" class="form-label">{{ __('Customer Email') }}</label>
                            <select class="form-select" id="customerEmail" name="customer_email" required>
                                <option value="">{{ __('Select Customer Email') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted" id="emailHelpText">{{ __('Auto-populated from selected customer') }}</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="customerContact" class="form-label">{{ __('Customer Contact') }}</label>
                            <select class="form-select" id="customerContact" name="customer_phone">
                                <option value="">{{ __('Select Customer Contact') }}</option>
                            </select>
                            <small class="text-muted" id="phoneHelpText">{{ __('Auto-populated from selected customer') }}</small>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary">{{ __('Product Details') }}</h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-success btn-sm">
                                        <i class="ti ti-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm">
                                        {{ __('View') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="product" class="form-label">{{ __('Product') }}</label>
                            <select class="form-select subscription-product-select" id="product" name="product_id" required>
                                <option value="">{{ __('Loading products...') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">{{ __('Select a product for this subscription plan') }}</small>
                        </div>
                        <div class="col-md-6 mb-3" id="subscriptionProductDetailsSection" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <h6 class="card-title mb-2">{{ __('Product Details') }}</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">{{ __('SKU') }}:</small>
                                            <div id="subscriptionProductSku" class="fw-medium">-</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">{{ __('Category') }}:</small>
                                            <div id="subscriptionProductCategory" class="fw-medium">-</div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <small class="text-muted">{{ __('Description') }}:</small>
                                            <div id="subscriptionProductDescription" class="text-sm">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productQty" class="form-label">{{ __('Product Qty') }}</label>
                            <input type="number" class="form-control" id="productQty" name="quantity" value="1" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="activeDate" class="form-label">{{ __('Active Date') }}</label>
                            <input type="date" class="form-control" id="activeDate" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productPrice" class="form-label">{{ __('Product Price') }}</label>
                            <input type="number" step="0.01" class="form-control" id="productPrice" name="product_price" placeholder="{{ __('Product Price') }}" readonly>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="invoiceDescription" class="form-label">{{ __('Invoice Description') }}</label>
                            <textarea class="form-control" id="invoiceDescription" name="description" rows="3" placeholder="{{ __('Invoice Description') }}"></textarea>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="mb-3 text-primary">{{ __('Payment Method') }}</h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="offline" value="offline" checked>
                                <label class="form-check-label" for="offline">
                                    {{ __('Offline') }}
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="online" value="online">
                                <label class="form-check-label" for="online">
                                    {{ __('Online') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="downPayment" class="form-label">{{ __('Down Payment') }}</label>
                            <input type="number" step="0.01" class="form-control" id="downPayment" name="down_payment" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label">{{ __('Discount Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount_amount" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="totalEmis" class="form-label">{{ __('Total EMIs') }}</label>
                            <input type="number" class="form-control" id="totalEmis" name="total_emis" placeholder="12" min="1" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="billingCycle" class="form-label">{{ __('Billing Cycle') }}</label>
                            <select class="form-select" id="billingCycle" name="billing_cycle" required>
                                <option value="monthly">{{ __('Monthly') }}</option>
                                <option value="quarterly">{{ __('Quarterly') }}</option>
                                <option value="yearly">{{ __('Yearly') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Create Subscription') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Installment Modal -->
<div class="modal fade" id="createInstallmentModal" tabindex="-1" aria-labelledby="createInstallmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createInstallmentModalLabel">{{ __('Create Installment Plan') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createInstallmentForm">
                @csrf
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary">{{ __('Customer Details') }}</h6>
                                <button type="button" class="btn btn-success btn-sm" id="addCustomerBtn">
                                    {{ __('Add') }}
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerName" class="form-label">{{ __('Customer Name') }}</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="installmentCustomerNameSearch" placeholder="{{ __('Search customer name...') }}" autocomplete="off">
                                <input type="hidden" id="installmentCustomerName" name="customer_id" required>
                                <input type="hidden" id="installmentCustomerType" name="customer_type">
                                <div class="dropdown-menu w-100" id="installmentCustomerDropdown" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Dynamic customer options will be loaded here -->
                                </div>
                            </div>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">{{ __('Search from Customers and Leads') }}</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerEmail" class="form-label">{{ __('Customer Email') }}</label>
                            <select class="form-select" id="installmentCustomerEmail" name="customer_email" required>
                                <option value="">{{ __('Select Customer Email') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted" id="installmentEmailHelpText">{{ __('Auto-populated from selected customer') }}</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="installmentCustomerContact" class="form-label">{{ __('Customer Contact') }}</label>
                            <select class="form-select" id="installmentCustomerContact" name="customer_phone">
                                <option value="">{{ __('Select Customer Contact') }}</option>
                            </select>
                            <small class="text-muted" id="installmentPhoneHelpText">{{ __('Auto-populated from selected customer') }}</small>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 text-primary">{{ __('Product Details') }}</h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-success btn-sm">
                                        <i class="ti ti-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm">
                                        {{ __('View') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProduct" class="form-label">{{ __('Product') }}</label>
                            <select class="form-select installment-product-select" id="installmentProduct" name="product_id" required>
                                <option value="">{{ __('Loading products...') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                            <small class="text-muted">{{ __('Select a product for this installment plan') }}</small>
                        </div>
                        <div class="col-md-6 mb-3" id="installmentProductDetailsSection" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <h6 class="card-title mb-2">{{ __('Product Details') }}</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">{{ __('SKU') }}:</small>
                                            <div id="installmentProductSku" class="fw-medium">-</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">{{ __('Category') }}:</small>
                                            <div id="installmentProductCategory" class="fw-medium">-</div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <small class="text-muted">{{ __('Description') }}:</small>
                                            <div id="installmentProductDescription" class="text-sm">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProductQty" class="form-label">{{ __('Product Qty') }}</label>
                            <input type="number" class="form-control" id="installmentProductQty" name="quantity" value="1" min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentActiveDate" class="form-label">{{ __('Active Date') }}</label>
                            <input type="date" class="form-control" id="installmentActiveDate" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentProductPrice" class="form-label">{{ __('Product Price') }}</label>
                            <input type="number" step="0.01" class="form-control" id="installmentProductPrice" name="product_price" placeholder="{{ __('Product Price') }}" readonly>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="mb-3 text-primary">{{ __('Payment Method') }}</h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="installmentOffline" value="offline" checked>
                                <label class="form-check-label" for="installmentOffline">
                                    {{ __('Offline') }}
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payment_method" id="installmentOnline" value="online">
                                <label class="form-check-label" for="installmentOnline">
                                    {{ __('Online') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentDownPayment" class="form-label">{{ __('Down Payment') }}</label>
                            <input type="number" step="0.01" class="form-control" id="installmentDownPayment" name="down_payment" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentDiscountAmount" class="form-label">{{ __('Discount Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="installmentDiscountAmount" name="discount_amount" placeholder="0.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="installmentPaymentFrequency" class="form-label">{{ __('Payment Frequency') }}</label>
                            <select class="form-select" id="installmentPaymentFrequency" name="payment_frequency" required>
                                <option value="monthly">{{ __('Monthly') }}</option>
                                <option value="weekly">{{ __('Weekly') }}</option>
                                <option value="quarterly">{{ __('Quarterly') }}</option>
                                <option value="yearly">{{ __('Yearly') }}</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{{ __('Balance') }}</label>
                            <div class="d-flex align-items-center">
                                <span id="installmentBalance" class="fw-bold text-primary">0</span>
                                <button type="button" class="btn btn-success btn-sm ms-auto" id="addInstallmentBtn">
                                    {{ __('Add Installment') }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Installment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3 text-primary">{{ __('Installment Details') }}</h6>
                            <div id="installmentsList">
                                <!-- Dynamic installments will be added here -->
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Description Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <label for="installmentInvoiceDescription" class="form-label">{{ __('Invoice Description') }}</label>
                            <textarea class="form-control" id="installmentInvoiceDescription" name="description" rows="4" placeholder="{{ __('Enter your content...') }}"></textarea>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="installmentDeclaration" checked>
                                <label class="form-check-label" for="installmentDeclaration">
                                    {{ __('I hereby declare that I\'m creating this invoice to collect online payment on the behalf of the organisation.') }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Create Installment Plan') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Subscription Notes Modal -->
<div class="modal fade" id="subscriptionNotesModal" tabindex="-1" aria-labelledby="subscriptionNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="subscriptionNotesModalLabel">{{ __('Subscription Notes') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="subscriptionNotesForm">
                    <input type="hidden" id="notesSubscriptionId" name="subscription_id">
                    <div class="mb-3">
                        <label for="subscriptionNotes" class="form-label">{{ __('Notes') }}</label>
                        <textarea class="form-control" id="subscriptionNotes" name="notes" rows="5" placeholder="{{ __('Enter subscription notes...') }}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="saveSubscriptionNotes()">{{ __('Save Notes') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Subscription Modal -->
<div class="modal fade" id="editSubscriptionModal" tabindex="-1" aria-labelledby="editSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSubscriptionModalLabel">{{ __('Edit Subscription') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSubscriptionForm">
                @csrf
                <input type="hidden" id="editSubscriptionId" name="subscription_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editStatus" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="active">{{ __('Active') }}</option>
                                <option value="paused">{{ __('Paused') }}</option>
                                <option value="cancelled">{{ __('Cancelled') }}</option>
                                <option value="expired">{{ __('Expired') }}</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editNextEmiDate" class="form-label">{{ __('Next EMI Date') }}</label>
                            <input type="date" class="form-control" id="editNextEmiDate" name="next_emi_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPaidAmount" class="form-label">{{ __('Paid Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="editPaidAmount" name="paid_amount">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editPendingAmount" class="form-label">{{ __('Pending Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="editPendingAmount" name="pending_amount">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="editReceiptUrl" class="form-label">{{ __('Receipt URL') }}</label>
                            <input type="url" class="form-control" id="editReceiptUrl" name="receipt_url" placeholder="https://...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Update Subscription') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Installment Plan Notes Modal -->
<div class="modal fade" id="installmentPlanNotesModal" tabindex="-1" aria-labelledby="installmentPlanNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="installmentPlanNotesModalLabel">{{ __('Installment Plan Notes') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="installmentPlanNotesForm">
                    <input type="hidden" id="notesInstallmentPlanId" name="installment_plan_id">
                    <div class="mb-3">
                        <label for="installmentPlanNotes" class="form-label">{{ __('Notes') }}</label>
                        <textarea class="form-control" id="installmentPlanNotes" name="notes" rows="5" placeholder="{{ __('Enter installment plan notes...') }}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="saveInstallmentPlanNotes()">{{ __('Save Notes') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Installment Plan Modal -->
<div class="modal fade" id="editInstallmentPlanModal" tabindex="-1" aria-labelledby="editInstallmentPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editInstallmentPlanModalLabel">{{ __('Edit Installment Plan') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editInstallmentPlanForm">
                @csrf
                <input type="hidden" id="editInstallmentPlanId" name="installment_plan_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentStatus" class="form-label">{{ __('Status') }}</label>
                            <select class="form-select" id="editInstallmentStatus" name="status">
                                <option value="active">{{ __('Active') }}</option>
                                <option value="paused">{{ __('Paused') }}</option>
                                <option value="cancelled">{{ __('Cancelled') }}</option>
                                <option value="completed">{{ __('Completed') }}</option>
                                <option value="overdue">{{ __('Overdue') }}</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentNextDate" class="form-label">{{ __('Next Installment Date') }}</label>
                            <input type="date" class="form-control" id="editInstallmentNextDate" name="next_installment_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentPaidAmount" class="form-label">{{ __('Paid Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="editInstallmentPaidAmount" name="paid_amount">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="editInstallmentPendingAmount" class="form-label">{{ __('Pending Amount') }}</label>
                            <input type="number" step="0.01" class="form-control" id="editInstallmentPendingAmount" name="pending_amount">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="editInstallmentReceiptUrl" class="form-label">{{ __('Receipt URL') }}</label>
                            <input type="url" class="form-control" id="editInstallmentReceiptUrl" name="receipt_url" placeholder="https://...">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Update Installment Plan') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales view switching
    const salesViewButtons = document.querySelectorAll('[data-sales-view]');
    const salesViews = document.querySelectorAll('.sales-view');

    salesViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-sales-view');

            // Remove active class from all buttons
            salesViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Hide all views
            salesViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // Customer search functionality
    let searchTimeout;
    let selectedCustomer = null;

    $('#customerNameSearch').on('input', function() {
        const searchTerm = $(this).val();

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            $('#customerDropdown').removeClass('show').empty();
            return;
        }

        searchTimeout = setTimeout(function() {
            searchCustomers(searchTerm);
        }, 300);
    });
    
    $('#installmentCustomerNameSearch').on('input', function() {
        const searchTerm = $(this).val();

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            $('#installmentCustomerDropdown').removeClass('show').empty();
            return;
        }

        searchTimeout = setTimeout(function() {
            searchCustomers(searchTerm);
        }, 300);
    });

    // Handle customer search
    function searchCustomers(searchTerm) {
        $.ajax({
            url: '{{ route("finance.sales.search-contacts") }}',
            type: 'GET',
            data: { search: searchTerm },
            success: function(response) {
                if (response.success) {
                    displayCustomerOptions(response.contacts);
                } else {
                    $('#customerDropdown').removeClass('show').empty();
                }
            },
            error: function() {
                $('#customerDropdown').removeClass('show').empty();
            }
        });
    }

    // Display customer search results
    function displayCustomerOptions(contacts) {
        const dropdown = $('#customerDropdown');
        dropdown.empty();

        if (contacts.length === 0) {
            dropdown.append('<div class="dropdown-item-text text-muted">{{ __("No contacts found") }}</div>');
        } else {
            contacts.forEach(function(contact) {
                const item = $(`
                    <a class="dropdown-item customer-option" href="#"
                       data-id="${contact.id}"
                       data-type="${contact.type}"
                       data-name="${contact.name}"
                       data-email="${contact.email}"
                       data-phone="${contact.phone}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${contact.display_name}</strong>
                                <br>
                                <small class="text-muted">${contact.display_info}</small>
                            </div>
                            <span class="badge bg-${contact.type === 'customer' ? 'primary' : 'info'} ms-2">
                                ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}
                            </span>
                        </div>
                    </a>
                `);
                dropdown.append(item);
            });
        }

        dropdown.addClass('show');
    }

    // Handle customer selection
    $(document).on('click', '.customer-option', function(e) {
        e.preventDefault();

        const customerId = $(this).data('id');
        const customerType = $(this).data('type');
        const customerName = $(this).data('name');

        // Set selected customer
        selectedCustomer = {
            id: customerId,
            type: customerType,
            name: customerName
        };

        // Update form fields
        $('#customerNameSearch').val(customerName);
        $('#customerName').val(customerId);
        $('#customerType').val(customerType);
        $('#customerDropdown').removeClass('show').empty();

        // Fetch detailed contact information
        fetchContactDetails(customerType, customerId);
    });

    // Fetch detailed contact information
    function fetchContactDetails(type, id) {
        $.ajax({
            url: `{{ route("finance.sales.get-contact-details", ["type" => ":type", "id" => ":id"]) }}`
                .replace(':type', type)
                .replace(':id', id),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    populateContactFields(response.contact);
                } else {
                    show_toastr('error', response.message || '{{ __("Failed to fetch contact details") }}');
                }
            },
            error: function() {
                show_toastr('error', '{{ __("Failed to fetch contact details") }}');
            }
        });
    }

    // Populate email and phone fields
    function populateContactFields(contact) {
        // Clear and populate email dropdown
        const emailSelect = $('#customerEmail');
        emailSelect.empty().append('<option value="">{{ __("Select Customer Email") }}</option>');

        if (contact.emails && contact.emails.length > 0) {
            contact.emails.forEach(function(email) {
                if (email) {
                    emailSelect.append(`<option value="${email}">${email}</option>`);
                }
            });
            // Auto-select first email
            emailSelect.val(contact.emails[0]);
        }

        // Clear and populate phone dropdown
        const phoneSelect = $('#customerContact');
        phoneSelect.empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');

        if (contact.phones && contact.phones.length > 0) {
            contact.phones.forEach(function(phone) {
                if (phone) {
                    phoneSelect.append(`<option value="${phone}">${phone}</option>`);
                }
            });
            // Auto-select first phone
            phoneSelect.val(contact.phones[0]);
        }

        // Update help text
        $('#emailHelpText').text(`{{ __("Loaded from") }} ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}: ${contact.name}`);
        $('#phoneHelpText').text(`{{ __("Loaded from") }} ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}: ${contact.name}`);
    }

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#customerNameSearch, #customerDropdown').length) {
            $('#customerDropdown').removeClass('show');
        }
    });

    // Clear customer selection when search field is cleared
    $('#customerNameSearch').on('keyup', function() {
        if ($(this).val() === '') {
            selectedCustomer = null;
            $('#customerName').val('');
            $('#customerType').val('');
            $('#customerEmail').empty().append('<option value="">{{ __("Select Customer Email") }}</option>');
            $('#customerContact').empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');
            $('#emailHelpText').text('{{ __("Auto-populated from selected customer") }}');
            $('#phoneHelpText').text('{{ __("Auto-populated from selected customer") }}');
        }
    });

    // Load products when subscription modal is opened
    $('#createSubscriptionModal').on('show.bs.modal', function() {
        loadSubscriptionProducts();
    });

    // Load products for subscription dropdown
    function loadSubscriptionProducts() {
        const select = $('.subscription-product-select');
        select.html('<option value="">{{ __("Loading products...") }}</option>');
        select.prop('disabled', true);

        $.ajax({
            url: '{{ route("invoice.products.dropdown") }}',
            type: 'GET',
            success: function(response) {
                select.prop('disabled', false);
                if (response.status) {
                    populateSubscriptionProductDropdown(response.data);
                    if (response.data.length === 0) {
                        select.html('<option value="">{{ __("No products available") }}</option>');
                        show_toastr('warning', '{{ __("No products found. Please add products first.") }}');
                    }
                } else {
                    select.html('<option value="">{{ __("Error loading products") }}</option>');
                    show_toastr('error', response.message || '{{ __("Failed to load products") }}');
                }
            },
            error: function() {
                select.prop('disabled', false);
                select.html('<option value="">{{ __("Error loading products") }}</option>');
                show_toastr('error', '{{ __("Error loading products") }}');
            }
        });
    }

    // Populate subscription product dropdown
    function populateSubscriptionProductDropdown(products) {
        const select = $('.subscription-product-select');
        select.html('<option value="">{{ __("Select Product") }}</option>');

        products.forEach(product => {
            const displayName = product.name + (product.sku ? ` (${product.sku})` : '');
            const priceDisplay = product.sale_price ? ` - $${parseFloat(product.sale_price).toFixed(2)}` : '';
            select.append(`<option value="${product.id}" data-price="${product.sale_price}" data-name="${product.name}" data-sku="${product.sku}" data-description="${product.description || ''}">${displayName}${priceDisplay}</option>`);
        });
    }

    // Enhanced subscription product selection handling
    $('#product').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const productId = $(this).val();
        const price = selectedOption.data('price');
        const description = selectedOption.data('description');

        if (productId && price) {
            $('#productPrice').val(price);
            // Auto-populate description immediately from dropdown data
            if (description) {
                $('#invoiceDescription').val(description);
            }
            calculateEmiAmount();
            loadProductDetails(productId, 'subscription');
        } else {
            hideProductDetails('subscription');
        }
    });

    // Load detailed product information
    function loadProductDetails(productId, type) {
        // Show loading state
        if (type === 'subscription') {
            $('#subscriptionProductSku').text('{{ __("Loading...") }}');
            $('#subscriptionProductCategory').text('{{ __("Loading...") }}');
            $('#subscriptionProductDescription').text('{{ __("Loading...") }}');
            $('#subscriptionProductDetailsSection').show();
        } else if (type === 'installment') {
            $('#installmentProductSku').text('{{ __("Loading...") }}');
            $('#installmentProductCategory').text('{{ __("Loading...") }}');
            $('#installmentProductDescription').text('{{ __("Loading...") }}');
            $('#installmentProductDetailsSection').show();
        }

        $.ajax({
            url: '{{ route("invoice.product.details", ":id") }}'.replace(':id', productId),
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    displayProductDetails(response.data, type);
                } else {
                    show_toastr('error', '{{ __("Error loading product details") }}');
                    hideProductDetails(type);
                }
            },
            error: function() {
                show_toastr('error', '{{ __("Error loading product details") }}');
                hideProductDetails(type);
            }
        });
    }

    // Display product details
    function displayProductDetails(product, type) {
        if (type === 'subscription') {
            $('#subscriptionProductSku').text(product.sku || '-');
            $('#subscriptionProductCategory').text(product.category ? product.category.name : '-');
            $('#subscriptionProductDescription').text(product.description || '-');
            $('#subscriptionProductDetailsSection').show();

            // Auto-populate description field
            if (product.description) {
                $('#invoiceDescription').val(product.description);
            }
        } else if (type === 'installment') {
            $('#installmentProductSku').text(product.sku || '-');
            $('#installmentProductCategory').text(product.category ? product.category.name : '-');
            $('#installmentProductDescription').text(product.description || '-');
            $('#installmentProductDetailsSection').show();

            // Auto-populate description field for installment
            if (product.description) {
                $('#installmentInvoiceDescription').val(product.description);
                // Update Summernote if it's initialized
                if (typeof $.fn.summernote !== 'undefined' && $('#installmentInvoiceDescription').hasClass('note-editable')) {
                    $('#installmentInvoiceDescription').summernote('code', product.description);
                }
            }
        }
    }

    // Hide product details
    function hideProductDetails(type) {
        if (type === 'subscription') {
            $('#subscriptionProductDetailsSection').hide();
            $('#productPrice').val('');
            $('#invoiceDescription').val('');
        } else if (type === 'installment') {
            $('#installmentProductDetailsSection').hide();
            $('#installmentProductPrice').val('');
            $('#installmentInvoiceDescription').val('');
            // Clear Summernote if it's initialized
            if (typeof $.fn.summernote !== 'undefined' && $('#installmentInvoiceDescription').hasClass('note-editable')) {
                $('#installmentInvoiceDescription').summernote('code', '');
            }
        }
    }

    // Calculate EMI amount when values change
    $('#productPrice, #downPayment, #discountAmount, #totalEmis').on('input', calculateEmiAmount);

    function calculateEmiAmount() {
        const productPrice = parseFloat($('#productPrice').val()) || 0;
        const downPayment = parseFloat($('#downPayment').val()) || 0;
        const discountAmount = parseFloat($('#discountAmount').val()) || 0;
        const totalEmis = parseInt($('#totalEmis').val()) || 1;

        const remainingAmount = productPrice - downPayment - discountAmount;
        const emiAmount = remainingAmount / totalEmis;

        // You can display this EMI amount somewhere if needed
        console.log('EMI Amount:', emiAmount);
    }

    // Handle subscription form submission
    $('#createSubscriptionForm').on('submit', function(e) {
        e.preventDefault();

        // Validate customer selection
        if (!selectedCustomer || !$('#customerName').val()) {
            show_toastr('error', '{{ __("Please select a customer") }}');
            $('#customerNameSearch').addClass('is-invalid');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('{{ __("Creating...") }}');

        $.ajax({
            url: '{{ route("finance.sales.store-subscription") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    $('#createSubscriptionModal').modal('hide');
                    resetSubscriptionForm();
                    location.reload(); // Refresh to show new subscription
                } else {
                    show_toastr('error', response.message || '{{ __("Something went wrong") }}');
                }
            },
            error: function(xhr) {
                let errorMessage = '{{ __("Something went wrong") }}';

                // Clear previous validation errors
                $('.form-control, #customerNameSearch').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        let input = $(`[name="${field}"]`);

                        // Handle customer_id field specially
                        if (field === 'customer_id') {
                            input = $('#customerNameSearch');
                        }

                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '{{ __("Please check the form for errors") }}';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Reset subscription form
    function resetSubscriptionForm() {
        $('#createSubscriptionForm')[0].reset();
        selectedCustomer = null;
        $('#customerNameSearch').val('');
        $('#customerName').val('');
        $('#customerType').val('');
        $('#customerEmail').empty().append('<option value="">{{ __("Select Customer Email") }}</option>');
        $('#customerContact').empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');
        $('#emailHelpText').text('{{ __("Auto-populated from selected customer") }}');
        $('#phoneHelpText').text('{{ __("Auto-populated from selected customer") }}');
        $('.form-control, #customerNameSearch').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    }

    // Reset modal when closed
    $('#createSubscriptionModal').on('hidden.bs.modal', function() {
        resetSubscriptionForm();
    });

    // Clear validation errors when user starts typing in customer search
    $('#customerNameSearch').on('input', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });
});

// Subscription action functions
function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        $.ajax({
            url: `{{ route("finance.sales.cancel-subscription", ":id") }}`.replace(':id', subscriptionId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to cancel subscription');
            }
        });
    }
}

function showSubscriptionNotes(subscriptionId) {
    // Load existing notes
    $.ajax({
        url: `{{ route("finance.sales.get-subscription", ":id") }}`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#notesSubscriptionId').val(subscriptionId);
                $('#subscriptionNotes').val(response.subscription.notes || '');
                $('#subscriptionNotesModal').modal('show');
            }
        }
    });
}

function saveSubscriptionNotes() {
    const subscriptionId = $('#notesSubscriptionId').val();
    const notes = $('#subscriptionNotes').val();

    $.ajax({
        url: `{{ route("finance.sales.update-subscription-notes", ":id") }}`.replace(':id', subscriptionId),
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            notes: notes
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#subscriptionNotesModal').modal('hide');
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to save notes');
        }
    });
}

function openWhatsAppChat(phone) {
    if (!phone) {
        show_toastr('error', 'No phone number available');
        return;
    }

    const cleanPhone = phone.replace(/[^\d]/g, '');
    const whatsappUrl = `https://wa.me/${cleanPhone}`;
    window.open(whatsappUrl, '_blank');
}

function editSubscription(subscriptionId) {
    // Load subscription data
    $.ajax({
        url: `{{ route("finance.sales.get-subscription", ":id") }}`.replace(':id', subscriptionId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const subscription = response.subscription;
                $('#editSubscriptionId').val(subscriptionId);
                $('#editStatus').val(subscription.status);
                $('#editNextEmiDate').val(subscription.next_emi_date);
                $('#editPaidAmount').val(subscription.paid_amount);
                $('#editPendingAmount').val(subscription.pending_amount);
                $('#editReceiptUrl').val(subscription.receipt_url);
                $('#editSubscriptionModal').modal('show');
            }
        }
    });
}

function deleteSubscription(subscriptionId) {
    if (confirm('Are you sure you want to delete this subscription? This action cannot be undone.')) {
        $.ajax({
            url: `{{ route("finance.sales.delete-subscription", ":id") }}`.replace(':id', subscriptionId),
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', 'Failed to delete subscription');
            }
        });
    }
}

// Handle edit subscription form submission
$(document).on('submit', '#editSubscriptionForm', function(e) {
    e.preventDefault();

    const subscriptionId = $('#editSubscriptionId').val();
    const formData = new FormData(this);

    $.ajax({
        url: `{{ route("finance.sales.update-subscription", ":id") }}`.replace(':id', subscriptionId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#editSubscriptionModal').modal('hide');
                location.reload();
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to update subscription');
        }
    });
});

// ==================== INSTALLMENT PLAN FUNCTIONALITY ====================

// Installment plan customer search functionality
let installmentSearchTimeout;
let selectedInstallmentCustomer = null;

$('#installmentCustomerNameSearch').on('input', function() {
    const searchTerm = $(this).val();

    clearTimeout(installmentSearchTimeout);

    if (searchTerm.length < 2) {
        $('#installmentCustomerDropdown').removeClass('show').empty();
        return;
    }

    installmentSearchTimeout = setTimeout(function() {
        searchInstallmentCustomers(searchTerm);
    }, 300);
});

// Handle installment customer search
function searchInstallmentCustomers(searchTerm) {
    $.ajax({
        url: '{{ route("finance.sales.search-contacts") }}',
        type: 'GET',
        data: { search: searchTerm },
        success: function(response) {
            if (response.success) {
                displayInstallmentCustomerOptions(response.contacts);
            } else {
                $('#installmentCustomerDropdown').removeClass('show').empty();
            }
        },
        error: function() {
            $('#installmentCustomerDropdown').removeClass('show').empty();
        }
    });
}

// Display installment customer search results
function displayInstallmentCustomerOptions(contacts) {
    const dropdown = $('#installmentCustomerDropdown');
    dropdown.empty();

    if (contacts.length === 0) {
        dropdown.append('<div class="dropdown-item-text text-muted">{{ __("No contacts found") }}</div>');
    } else {
        contacts.forEach(function(contact) {
            const item = $(`
                <a class="dropdown-item installment-customer-option" href="#"
                   data-id="${contact.id}"
                   data-type="${contact.type}"
                   data-name="${contact.name}"
                   data-email="${contact.email}"
                   data-phone="${contact.phone}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${contact.display_name}</strong>
                            <br>
                            <small class="text-muted">${contact.display_info}</small>
                        </div>
                        <span class="badge bg-${contact.type === 'customer' ? 'primary' : 'info'} ms-2">
                            ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}
                        </span>
                    </div>
                </a>
            `);
            dropdown.append(item);
        });
    }

    dropdown.addClass('show');
}

// Handle installment customer selection
$(document).on('click', '.installment-customer-option', function(e) {
    e.preventDefault();

    const customerId = $(this).data('id');
    const customerType = $(this).data('type');
    const customerName = $(this).data('name');

    // Set selected customer
    selectedInstallmentCustomer = {
        id: customerId,
        type: customerType,
        name: customerName
    };

    // Update form fields
    $('#installmentCustomerNameSearch').val(customerName);
    $('#installmentCustomerName').val(customerId);
    $('#installmentCustomerType').val(customerType);
    $('#installmentCustomerDropdown').removeClass('show').empty();

    // Fetch detailed contact information
    fetchInstallmentContactDetails(customerType, customerId);
});

// Fetch detailed installment contact information
function fetchInstallmentContactDetails(type, id) {
    $.ajax({
        url: `{{ route("finance.sales.get-contact-details", ["type" => ":type", "id" => ":id"]) }}`
            .replace(':type', type)
            .replace(':id', id),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                populateInstallmentContactFields(response.contact);
            } else {
                show_toastr('error', response.message || '{{ __("Failed to fetch contact details") }}');
            }
        },
        error: function() {
            show_toastr('error', '{{ __("Failed to fetch contact details") }}');
        }
    });
}

// Populate installment email and phone fields
function populateInstallmentContactFields(contact) {
    // Clear and populate email dropdown
    const emailSelect = $('#installmentCustomerEmail');
    emailSelect.empty().append('<option value="">{{ __("Select Customer Email") }}</option>');

    if (contact.emails && contact.emails.length > 0) {
        contact.emails.forEach(function(email) {
            if (email) {
                emailSelect.append(`<option value="${email}">${email}</option>`);
            }
        });
        // Auto-select first email
        emailSelect.val(contact.emails[0]);
    }

    // Clear and populate phone dropdown
    const phoneSelect = $('#installmentCustomerContact');
    phoneSelect.empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');

    if (contact.phones && contact.phones.length > 0) {
        contact.phones.forEach(function(phone) {
            if (phone) {
                phoneSelect.append(`<option value="${phone}">${phone}</option>`);
            }
        });
        // Auto-select first phone
        phoneSelect.val(contact.phones[0]);
    }

    // Update help text
    $('#installmentEmailHelpText').text(`{{ __("Loaded from") }} ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}: ${contact.name}`);
    $('#installmentPhoneHelpText').text(`{{ __("Loaded from") }} ${contact.type === 'customer' ? '{{ __("Customer") }}' : '{{ __("Lead") }}'}: ${contact.name}`);
}

// Hide dropdown when clicking outside
$(document).on('click', function(e) {
    if (!$(e.target).closest('#installmentCustomerNameSearch, #installmentCustomerDropdown').length) {
        $('#installmentCustomerDropdown').removeClass('show');
    }
});

// Clear installment customer selection when search field is cleared
$('#installmentCustomerNameSearch').on('keyup', function() {
    if ($(this).val() === '') {
        selectedInstallmentCustomer = null;
        $('#installmentCustomerName').val('');
        $('#installmentCustomerType').val('');
        $('#installmentCustomerEmail').empty().append('<option value="">{{ __("Select Customer Email") }}</option>');
        $('#installmentCustomerContact').empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');
        $('#installmentEmailHelpText').text('{{ __("Auto-populated from selected customer") }}');
        $('#installmentPhoneHelpText').text('{{ __("Auto-populated from selected customer") }}');
    }
});

// Load products when installment modal is opened
$('#createInstallmentModal').on('show.bs.modal', function() {
    loadInstallmentProducts();
});

// Load products for installment dropdown
function loadInstallmentProducts() {
    const select = $('.installment-product-select');
    select.html('<option value="">{{ __("Loading products...") }}</option>');
    select.prop('disabled', true);

    $.ajax({
        url: '{{ route("invoice.products.dropdown") }}',
        type: 'GET',
        success: function(response) {
            select.prop('disabled', false);
            if (response.status) {
                populateInstallmentProductDropdown(response.data);
                if (response.data.length === 0) {
                    select.html('<option value="">{{ __("No products available") }}</option>');
                    show_toastr('warning', '{{ __("No products found. Please add products first.") }}');
                }
            } else {
                select.html('<option value="">{{ __("Error loading products") }}</option>');
                show_toastr('error', response.message || '{{ __("Failed to load products") }}');
            }
        },
        error: function() {
            select.prop('disabled', false);
            select.html('<option value="">{{ __("Error loading products") }}</option>');
            show_toastr('error', '{{ __("Error loading products") }}');
        }
    });
}

// Populate installment product dropdown
function populateInstallmentProductDropdown(products) {
    const select = $('.installment-product-select');
    select.html('<option value="">{{ __("Select Product") }}</option>');

    products.forEach(product => {
        const displayName = product.name + (product.sku ? ` (${product.sku})` : '');
        const priceDisplay = product.sale_price ? ` - $${parseFloat(product.sale_price).toFixed(2)}` : '';
        select.append(`<option value="${product.id}" data-price="${product.sale_price}" data-name="${product.name}" data-sku="${product.sku}" data-description="${product.description || ''}">${displayName}${priceDisplay}</option>`);
    });
}

// Enhanced installment product selection handling
$('#installmentProduct').on('change', function() {
    const selectedOption = $(this).find('option:selected');
    const productId = $(this).val();
    const price = selectedOption.data('price');
    const description = selectedOption.data('description');

    if (productId && price) {
        $('#installmentProductPrice').val(price);
        // Auto-populate description immediately from dropdown data
        if (description) {
            $('#installmentInvoiceDescription').val(description);
            // Update Summernote if it's initialized
            if (typeof $.fn.summernote !== 'undefined' && $('#installmentInvoiceDescription').hasClass('note-editable')) {
                $('#installmentInvoiceDescription').summernote('code', description);
            }
        }
        calculateInstallmentBalance();
        loadProductDetails(productId, 'installment');
    } else {
        hideProductDetails('installment');
    }
});

// Calculate installment balance when values change
$('#installmentProductPrice, #installmentProductQty, #installmentDownPayment, #installmentDiscountAmount').on('input', calculateInstallmentBalance);

function calculateInstallmentBalance() {
    const productPrice = parseFloat($('#installmentProductPrice').val()) || 0;
    const quantity = parseInt($('#installmentProductQty').val()) || 1;
    const downPayment = parseFloat($('#installmentDownPayment').val()) || 0;
    const discountAmount = parseFloat($('#installmentDiscountAmount').val()) || 0;

    const totalAmount = (productPrice * quantity) - discountAmount;
    const balance = totalAmount - downPayment;

    $('#installmentBalance').text(balance.toFixed(2));
}

// Dynamic installment addition
let installmentCounter = 0;

$('#addInstallmentBtn').on('click', function() {
    installmentCounter++;
    const ordinalSuffix = getOrdinalSuffix(installmentCounter);
    const installmentHtml = `
        <div class="installment-item border rounded p-3 mb-3" data-installment="${installmentCounter}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">${installmentCounter}${ordinalSuffix} {{ __('Installment') }}</h6>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-success btn-sm" onclick="addAnotherInstallment()">
                        {{ __('Add Installment') }}
                    </button>
                    ${installmentCounter > 1 ? '<button type="button" class="btn btn-danger btn-sm" onclick="removeInstallment(this)"><i class="ti ti-trash"></i></button>' : ''}
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ __('Payment Date') }}</label>
                    <input type="date" class="form-control installment-date" name="installments[${installmentCounter-1}][payment_date]" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ __('Amount') }}</label>
                    <input type="number" step="0.01" class="form-control installment-amount" name="installments[${installmentCounter-1}][amount]" placeholder="0" required>
                </div>
            </div>
        </div>
    `;

    $('#installmentsList').append(installmentHtml);
    validateInstallmentAmounts();
});

function getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j == 1 && k != 11) {
        return "st";
    }
    if (j == 2 && k != 12) {
        return "nd";
    }
    if (j == 3 && k != 13) {
        return "rd";
    }
    return "th";
}

function removeInstallment(button) {
    $(button).closest('.installment-item').remove();
    reindexInstallments();
    validateInstallmentAmounts();
}

function reindexInstallments() {
    $('.installment-item').each(function(index) {
        const newIndex = index + 1;
        const ordinalSuffix = getOrdinalSuffix(newIndex);

        $(this).attr('data-installment', newIndex);
        $(this).find('h6').text(`${newIndex}${ordinalSuffix} {{ __('Installment') }}`);
        $(this).find('.installment-date').attr('name', `installments[${index}][payment_date]`);
        $(this).find('.installment-amount').attr('name', `installments[${index}][amount]`);
    });

    installmentCounter = $('.installment-item').length;
}

function addAnotherInstallment() {
    $('#addInstallmentBtn').click();
}

// Handle installment form submission
$('#createInstallmentForm').on('submit', function(e) {
    e.preventDefault();

    // Validate customer selection
    if (!selectedInstallmentCustomer || !$('#installmentCustomerName').val()) {
        show_toastr('error', '{{ __("Please select a customer") }}');
        $('#installmentCustomerNameSearch').addClass('is-invalid');
        return;
    }

    // Validate installments
    const installments = $('.installment-item');
    if (installments.length === 0) {
        show_toastr('error', '{{ __("Please add at least one installment") }}');
        return;
    }

    // Get Summernote content
    if (typeof $.fn.summernote !== 'undefined') {
        const summernoteContent = $('#installmentInvoiceDescription').summernote('code');
        $('#installmentInvoiceDescription').val(summernoteContent);
    }

    const formData = new FormData(this);
    const submitBtn = $(this).find('button[type="submit"]');
    const originalText = submitBtn.text();

    submitBtn.prop('disabled', true).text('{{ __("Creating...") }}');

    $.ajax({
        url: '{{ route("finance.sales.store-installment-plan") }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#createInstallmentModal').modal('hide');
                resetInstallmentForm();
                location.reload(); // Refresh to show new installment plan
            } else {
                show_toastr('error', response.message || '{{ __("Something went wrong") }}');
            }
        },
        error: function(xhr) {
            let errorMessage = '{{ __("Something went wrong") }}';

            // Clear previous validation errors
            $('.form-control, #installmentCustomerNameSearch').removeClass('is-invalid');
            $('.invalid-feedback').text('');

            if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = xhr.responseJSON.errors;
                Object.keys(errors).forEach(function(field) {
                    let input = $(`[name="${field}"]`);

                    // Handle customer_id field specially
                    if (field === 'customer_id') {
                        input = $('#installmentCustomerNameSearch');
                    }

                    if (input.length > 0) {
                        input.addClass('is-invalid');
                        const feedback = input.siblings('.invalid-feedback');
                        if (feedback.length > 0) {
                            feedback.text(errors[field][0]);
                        }
                    }
                });
                errorMessage = '{{ __("Please check the form for errors") }}';
            }

            show_toastr('error', errorMessage);
        },
        complete: function() {
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
});

function resetInstallmentForm() {
    $('#createInstallmentForm')[0].reset();
    selectedInstallmentCustomer = null;
    installmentCounter = 0;
    $('#installmentsList').empty();
    $('#installmentBalance').text('0');
    $('#installmentCustomerName').val('');
    $('#installmentCustomerType').val('');
    $('#installmentCustomerEmail').empty().append('<option value="">{{ __("Select Customer Email") }}</option>');
    $('#installmentCustomerContact').empty().append('<option value="">{{ __("Select Customer Contact") }}</option>');
    $('#installmentEmailHelpText').text('{{ __("Auto-populated from selected customer") }}');
    $('#installmentPhoneHelpText').text('{{ __("Auto-populated from selected customer") }}');
    $('.form-control, #installmentCustomerNameSearch').removeClass('is-invalid');
    $('.invalid-feedback').text('');

    // Reset Summernote
    if (typeof $.fn.summernote !== 'undefined') {
        $('#installmentInvoiceDescription').summernote('reset');
    }
}

// ==================== INSTALLMENT PLAN ACTION FUNCTIONS ====================

function cancelInstallmentPlan(planId) {
    if (confirm('{{ __("Are you sure you want to cancel this installment plan?") }}')) {
        $.ajax({
            url: `{{ route("finance.sales.cancel-installment-plan", ":id") }}`.replace(':id', planId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', '{{ __("Failed to cancel installment plan") }}');
            }
        });
    }
}

function showInstallmentPlanNotes(planId) {
    // Load existing notes
    $.ajax({
        url: `{{ route("finance.sales.get-installment-plan", ":id") }}`.replace(':id', planId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                $('#notesInstallmentPlanId').val(planId);
                $('#installmentPlanNotes').val(response.installment_plan.notes || '');
                $('#installmentPlanNotesModal').modal('show');
            }
        }
    });
}

function saveInstallmentPlanNotes() {
    const planId = $('#notesInstallmentPlanId').val();
    const notes = $('#installmentPlanNotes').val();

    $.ajax({
        url: `{{ route("finance.sales.update-installment-plan-notes", ":id") }}`.replace(':id', planId),
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            notes: notes
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#installmentPlanNotesModal').modal('hide');
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', '{{ __("Failed to save notes") }}');
        }
    });
}

function editInstallmentPlan(planId) {
    // Load installment plan data
    $.ajax({
        url: `{{ route("finance.sales.get-installment-plan", ":id") }}`.replace(':id', planId),
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const plan = response.installment_plan;
                $('#editInstallmentPlanId').val(planId);
                $('#editInstallmentStatus').val(plan.status);
                $('#editInstallmentNextDate').val(plan.next_installment_date);
                $('#editInstallmentPaidAmount').val(plan.paid_amount);
                $('#editInstallmentPendingAmount').val(plan.pending_amount);
                $('#editInstallmentReceiptUrl').val(plan.receipt_url);
                $('#editInstallmentPlanModal').modal('show');
            }
        }
    });
}

function deleteInstallmentPlan(planId) {
    if (confirm('{{ __("Are you sure you want to delete this installment plan? This action cannot be undone.") }}')) {
        $.ajax({
            url: `{{ route("finance.sales.delete-installment-plan", ":id") }}`.replace(':id', planId),
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    location.reload();
                } else {
                    show_toastr('error', response.message);
                }
            },
            error: function() {
                show_toastr('error', '{{ __("Failed to delete installment plan") }}');
            }
        });
    }
}

// Handle edit installment plan form submission
$(document).on('submit', '#editInstallmentPlanForm', function(e) {
    e.preventDefault();

    const planId = $('#editInstallmentPlanId').val();
    const formData = new FormData(this);

    $.ajax({
        url: `{{ route("finance.sales.update-installment-plan", ":id") }}`.replace(':id', planId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                $('#editInstallmentPlanModal').modal('hide');
                location.reload();
            } else {
                show_toastr('error', response.message);
            }
        },
        error: function() {
            show_toastr('error', 'Failed to update installment plan');
        }
    });
});

// Initialize Summernote for invoice description
$(document).ready(function() {
    // Initialize Summernote if available
    if (typeof $.fn.summernote !== 'undefined') {
        $('#installmentInvoiceDescription').summernote({
            placeholder: "Enter your content...",
            tabsize: 2,
            height: 150,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'strikethrough']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link']],
                ['view', ['fullscreen', 'codeview']]
            ]
        });
    }
});

// Reset installment modal when it's closed
$('#createInstallmentModal').on('hidden.bs.modal', function() {
    resetInstallmentForm();
});

// Add validation for installment amounts
$(document).on('input', '.installment-amount', function() {
    validateInstallmentAmounts();
});

function validateInstallmentAmounts() {
    const balance = parseFloat($('#installmentBalance').text()) || 0;
    let totalInstallmentAmount = 0;

    $('.installment-amount').each(function() {
        const amount = parseFloat($(this).val()) || 0;
        totalInstallmentAmount += amount;
    });

    const difference = Math.abs(balance - totalInstallmentAmount);

    if (difference > 0.01 && $('.installment-amount').length > 0) {
        $('.installment-amount').addClass('is-invalid');
        if ($('#installment-validation-message').length === 0) {
            $('#installmentsList').append('<div id="installment-validation-message" class="alert alert-warning mt-2">{{ __("Total installment amounts must equal the balance amount") }}</div>');
        }
    } else {
        $('.installment-amount').removeClass('is-invalid');
        $('#installment-validation-message').remove();
    }
}

// Auto-calculate installment amounts when frequency changes
$('#installmentPaymentFrequency').on('change', function() {
    const frequency = $(this).val();
    const balance = parseFloat($('#installmentBalance').text()) || 0;
    const installmentCount = $('.installment-item').length;

    if (installmentCount > 0 && balance > 0) {
        const amountPerInstallment = (balance / installmentCount).toFixed(2);
        $('.installment-amount').val(amountPerInstallment);
        validateInstallmentAmounts();
    }
});

// Auto-set dates based on frequency
$(document).on('change', '.installment-date', function() {
    const currentDate = new Date($(this).val());
    const frequency = $('#installmentPaymentFrequency').val();
    const currentItem = $(this).closest('.installment-item');
    const nextItem = currentItem.next('.installment-item');

    if (nextItem.length > 0) {
        let nextDate = new Date(currentDate);

        switch (frequency) {
            case 'weekly':
                nextDate.setDate(nextDate.getDate() + 7);
                break;
            case 'monthly':
                nextDate.setMonth(nextDate.getMonth() + 1);
                break;
            case 'quarterly':
                nextDate.setMonth(nextDate.getMonth() + 3);
                break;
            case 'yearly':
                nextDate.setFullYear(nextDate.getFullYear() + 1);
                break;
        }

        const nextDateString = nextDate.toISOString().split('T')[0];
        nextItem.find('.installment-date').val(nextDateString);
    }
});
</script>
