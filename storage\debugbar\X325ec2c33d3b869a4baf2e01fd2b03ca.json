{"__meta": {"id": "X325ec2c33d3b869a4baf2e01fd2b03ca", "datetime": "2025-07-31 16:53:43", "utime": **********.740397, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980821.414901, "end": **********.740473, "duration": 2.3255720138549805, "duration_str": "2.33s", "measures": [{"label": "Booting", "start": 1753980821.414901, "relative_start": 0, "end": **********.468189, "relative_end": **********.468189, "duration": 2.053287982940674, "duration_str": "2.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.468214, "relative_start": 2.0533130168914795, "end": **********.740481, "relative_end": 7.867813110351562e-06, "duration": 0.27226686477661133, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01073, "accumulated_duration_str": "10.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.598139, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 41.752}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.634009, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 41.752, "width_percent": 13.141}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.653445, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 54.893, "width_percent": 24.418}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.677504, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 79.31, "width_percent": 20.69}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1595264185 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1595264185\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-137281165 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137281165\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-584774966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-584774966\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1800371162 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijk5QzFTREdFcVZwZU15VGVHSHZNZ3c9PSIsInZhbHVlIjoiWkFCbEhVdjlYc01BS0p5dDdaeFZyaUdsK2JodnlxVzV3RDFVeVhIWGQyZUZFMG5iVDdvM0IvYkRRMzFva3ZFTFpzM3k2V09BYW93dlFleGFPRXRXcWptQmlQWGpPMGozWVNvUUJzUkpMR1lrUUhyK3pISFE3RGJZcWtaT2huUzQ2YTY0aGhsQTk2VnNGeGduQjhoQkdJQ1JscnJ2eVM2dUxnS0s1NHRJZFVWRENqTE1UQXJSZmNUYlR2TVdsUHdRUHJQZ0FRMlJNQmZRb08zakFjWW5ENURDaVhIMkZOenZlclZLUlkrTHlvbm9KSC9NVVFOdVhDQnhlSmxDUHJJdURlcHFBSlNUdWY1b3ZDN0dqRlRHazFDWGlTMHB5WEpOczIxSEM2QzRnU0J3bGt2RUdXYk5NZHhMTXFSeGZmS2VvN2lBYzNtSnNBTzZpZlo2dGtQaEcrMHZORmRXeWIzZmdERVRWMFUrVGxnR1R1Q3VhdS80NUc1eVdmWGlwTVUrM0hWUkhRYzRkRU5OeFAwKzJVMkYyYmlHdFVBYkFsMnFFai9UWHZncWh3NmRjVXN4SlI5VVJBeDZiSGNuTGEySkJBRWRnNGtrYUVJQ3pnL0J3dWZ4eFNmYVZTT01UVkpjUW5wVHJmU01JR0RrRXoyZEhuL0Z5S0hiK1MzSUxtR0UiLCJtYWMiOiI2OWI4MmE1YmY3ZjNjNDYxZjlkYWQwM2VlOGVkMGM5YTg5YTk5MTZjMmMyNDVjNWYxNmQwMjI1MDc3ODU1YmQ3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlFac05VeEhqdHpVU05JbGhVNWpjV2c9PSIsInZhbHVlIjoiL1FSMW9WTW5ZSVQ1SFVrMTJwQjNIcktTK0JQNjkvRmdMdGVaWmFYK1FYKzE2UUhqODRaMlg5Si9xbHp4S1FvNkhYdXNPQ1ZEaVVjVTBMZ2FlR09leWozUHF0SnNFNEZBQU4rempBZ0R5ejc2akV5RnVOYlVpQTZWNTV2TUZMdWlIeFFqSWZNTHMzWUhEcEViMUFGVCtUMUl3RjlsVS9RQVhZSGZyUEsyeW1JQnRiT1JjMjJSS0lzdVlxcGs0Z0ZGdEU0S2lMdWt2cWw5RitIS2NaTFZEZDB5SkJ4VnhhWFR6VHljYURuRUFyZUlnUk82Rlloem9FaFFGbWEvMmNIVS9lZmd0UkNENVNlc0d2akdBOTR1dFRnZVEyRVI5ckRlTk9CYzdiVllCNGpSc05kdFluaDBwNVRWV3I0M1lEL05NWXZwc2FzU2pOblo2eVdoSmdQWkFTTENGRlpZeTBrR1M1ei9NN1ZsVm5IZ3hEUnJvMjVuUmZxNFpHU0ppcmg2M3U1eHhNVURVTUk5MmNNc083c1J5MVIzMHVNTFg1aWRBcUdDMVA0ZEVoS2dFajgvWEVveFh6TDRPdytlcWJyTVBweWRBTDZSSE1yRnAxQk1JVG1ZZXFiQnpvcU56eVVWQnBQeFhQRFBKdllSRG9Mc1J2b3RwTGo2VGJMRXNtM0giLCJtYWMiOiIwNmUwZThhNDU5MDI1OWI5Y2M0MjI2ZmE3YTkwNTUwNTdhYTA4MGRmYzg3N2RlZjY3NWY1NTJmNGRhYTE0ZjYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800371162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1766918318 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766918318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-776278391 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:53:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdpR2s5b0FSWFNTUGZyUUIxSDk5UUE9PSIsInZhbHVlIjoiQ0ZLM2JqT1I4M1FKYmR1VnNkZ251bjFsa09BbGZ0Y3ZjNkpuTzNvMTNKb2JWaFhoRU5TRncxMFRiTFp0NjFUSTdXMm5TTE1RUlptM3Y4MzFOaFZGZ3pJTk5wWHAvUTdCRWlsSUVwNlp1UXlKM0gzZ3JVOVZlT3ArNmhsOGVtU3ZsQ3JvajJJNU9rYzZMekJMN3JCTzZEcS9WcW1ObUNqWm1ldFFtODRLbm9CYWV1dVEzYnBZWG1zSXNjbXplR3p2cnpOcitqdzFtNmtuTmcydXVqUGpxalZybXNJRUxRL2Q5Q0x4WTR2L0ozL2crSFZPWjd3RzR6TEZ4bWtLVDV4Yk9TT2pqNEtZa2cvS0JWYm4zWHBScGlGZFV4aFIxK2lNdTBVRHBObDlWZWtJUGdsdkdYYnd2M0dMcXVQZnQ1c3VyYnlRUmxXb0hMZVVDaGI3Sm9ScnFXdngrOGh4aFFYdGgwL0xRR0FxU2pMb294MVBGNjE1YTJmdnVRR1JjbzRnN0ZuMDd5KzJQN1hvb2k0SjJucENLTzg0TThvWGFiSCtPdDUreUs1dU9vQ2IyYVJxY3haT2Rmc2hoNEhpbkZVOWR2ZER2RC9NZW1oQVNZQllETVU4VW1rcmtvTWhhUUx4MS91elNqK1U4ZkgrbXFtSi94S3JNdnI4UFRxWVlDOE4iLCJtYWMiOiI2YzgwMjU2MjA0YTZiYzZkYjExYjRlZWZmNzlmYmJlZDgyZWQ2YTMwNjkwMWM4Y2NmMTdkNzI4YjU4ZDkwMDM4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:53:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZQRXhBMS9qOEdUOThZT3V2ejA5Vnc9PSIsInZhbHVlIjoiWjM0TUsycXg4emhxZmtOZENZSUVQajRDdytjdlVpd0RyV0tXQTBhTjNTMEJqUTJJUTFXWU1mb01LZjlCM0hIQXpWeTF3cUpZUUxIVGxQaEVYbHJ4QmJ6UDhmODVaZkVJUHF0YXplTlVNdlA2TzFuRWJnYmJjc2ZtYVJmR2pnT0xqSmJUdkF3QjBwOFJvQnhrNmRBb2ZHeVQ0eVF4ZnE2ZjEyL0g2dDZEczVjdWtNVVliOElwVk83cjVPMEdCZnFac1ROS1FNVFRSK25rdTZzNXpKd2xNdlBLSitBNTJrT2FRVk5TTlBpeWJCdlZKbVRwaElyQk1nVnJ6aURDajlvU1AxTFdYSlkyY2JPY1Y1VGx5NURmMVhiRGErMzFrL0FPQ2tGdHFXcHNFdzhXVXV4d3llc1hEaXFSZjZOMmNnUHJGNVZvQ1Z1ckxPWm9uSFpWa3dtYldLRjJXdi9VbndrZjB0czZlcVFxT1lMbGg4RzdsMXJ0T2RoUFpzcFZ6eHJ6S2hTTDhyWnlCbkYvRXIzRmNUaXpteEtyazdBaHJtQ0haZU9icmJvdzVsRnNVM1dPNTBSS1h5bzBqak0rWlpvVEZkMTRYNSt3TEZQR3AvVVduV3A1RVhIODVSZ1cyR2N6ajliQURQWGVkMkEwK3dka21rQkY3dTl3aWNxZUE4NlciLCJtYWMiOiI3NmM1YWFmYjFmM2NhZWM3MjIzOWZlNmIxZGViZGJmODU5OTE3NTljZWE1ZjhlYzRiNmI4ZWRhN2VmZDRmNDlhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:53:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdpR2s5b0FSWFNTUGZyUUIxSDk5UUE9PSIsInZhbHVlIjoiQ0ZLM2JqT1I4M1FKYmR1VnNkZ251bjFsa09BbGZ0Y3ZjNkpuTzNvMTNKb2JWaFhoRU5TRncxMFRiTFp0NjFUSTdXMm5TTE1RUlptM3Y4MzFOaFZGZ3pJTk5wWHAvUTdCRWlsSUVwNlp1UXlKM0gzZ3JVOVZlT3ArNmhsOGVtU3ZsQ3JvajJJNU9rYzZMekJMN3JCTzZEcS9WcW1ObUNqWm1ldFFtODRLbm9CYWV1dVEzYnBZWG1zSXNjbXplR3p2cnpOcitqdzFtNmtuTmcydXVqUGpxalZybXNJRUxRL2Q5Q0x4WTR2L0ozL2crSFZPWjd3RzR6TEZ4bWtLVDV4Yk9TT2pqNEtZa2cvS0JWYm4zWHBScGlGZFV4aFIxK2lNdTBVRHBObDlWZWtJUGdsdkdYYnd2M0dMcXVQZnQ1c3VyYnlRUmxXb0hMZVVDaGI3Sm9ScnFXdngrOGh4aFFYdGgwL0xRR0FxU2pMb294MVBGNjE1YTJmdnVRR1JjbzRnN0ZuMDd5KzJQN1hvb2k0SjJucENLTzg0TThvWGFiSCtPdDUreUs1dU9vQ2IyYVJxY3haT2Rmc2hoNEhpbkZVOWR2ZER2RC9NZW1oQVNZQllETVU4VW1rcmtvTWhhUUx4MS91elNqK1U4ZkgrbXFtSi94S3JNdnI4UFRxWVlDOE4iLCJtYWMiOiI2YzgwMjU2MjA0YTZiYzZkYjExYjRlZWZmNzlmYmJlZDgyZWQ2YTMwNjkwMWM4Y2NmMTdkNzI4YjU4ZDkwMDM4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:53:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZQRXhBMS9qOEdUOThZT3V2ejA5Vnc9PSIsInZhbHVlIjoiWjM0TUsycXg4emhxZmtOZENZSUVQajRDdytjdlVpd0RyV0tXQTBhTjNTMEJqUTJJUTFXWU1mb01LZjlCM0hIQXpWeTF3cUpZUUxIVGxQaEVYbHJ4QmJ6UDhmODVaZkVJUHF0YXplTlVNdlA2TzFuRWJnYmJjc2ZtYVJmR2pnT0xqSmJUdkF3QjBwOFJvQnhrNmRBb2ZHeVQ0eVF4ZnE2ZjEyL0g2dDZEczVjdWtNVVliOElwVk83cjVPMEdCZnFac1ROS1FNVFRSK25rdTZzNXpKd2xNdlBLSitBNTJrT2FRVk5TTlBpeWJCdlZKbVRwaElyQk1nVnJ6aURDajlvU1AxTFdYSlkyY2JPY1Y1VGx5NURmMVhiRGErMzFrL0FPQ2tGdHFXcHNFdzhXVXV4d3llc1hEaXFSZjZOMmNnUHJGNVZvQ1Z1ckxPWm9uSFpWa3dtYldLRjJXdi9VbndrZjB0czZlcVFxT1lMbGg4RzdsMXJ0T2RoUFpzcFZ6eHJ6S2hTTDhyWnlCbkYvRXIzRmNUaXpteEtyazdBaHJtQ0haZU9icmJvdzVsRnNVM1dPNTBSS1h5bzBqak0rWlpvVEZkMTRYNSt3TEZQR3AvVVduV3A1RVhIODVSZ1cyR2N6ajliQURQWGVkMkEwK3dka21rQkY3dTl3aWNxZUE4NlciLCJtYWMiOiI3NmM1YWFmYjFmM2NhZWM3MjIzOWZlNmIxZGViZGJmODU5OTE3NTljZWE1ZjhlYzRiNmI4ZWRhN2VmZDRmNDlhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:53:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776278391\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}