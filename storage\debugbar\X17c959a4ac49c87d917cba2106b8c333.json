{"__meta": {"id": "X17c959a4ac49c87d917cba2106b8c333", "datetime": "2025-07-31 16:37:54", "utime": **********.322261, "method": "GET", "uri": "/finance/sales/contacts/customer/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979871.366223, "end": **********.322359, "duration": 2.9561359882354736, "duration_str": "2.96s", "measures": [{"label": "Booting", "start": 1753979871.366223, "relative_start": 0, "end": 1753979873.858325, "relative_end": 1753979873.858325, "duration": 2.4921019077301025, "duration_str": "2.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979873.858392, "relative_start": 2.49216890335083, "end": **********.322367, "relative_end": 7.867813110351562e-06, "duration": 0.4639749526977539, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03447, "accumulated_duration_str": "34.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1369529, "duration": 0.02921, "duration_str": "29.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.74}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.248193, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.74, "width_percent": 7.833}, {"sql": "select * from `customers` where `id` = '2' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.272249, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 92.573, "width_percent": 7.427}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/2", "status_code": "<pre class=sf-dump id=sf-dump-966500986 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-966500986\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1950505514 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1950505514\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1052473843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1052473843\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2083787296 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpYVk8zL3cvbVpZZDJhdml3SytYUXc9PSIsInZhbHVlIjoiUURCV2VYRDBza21OV3Q4RXRNTDhnSzFYUnJMbnRScVUvTDltQllaek1vMWg2UGJjSGtVbTM3R29yeXB1ODlSTU15NFVjd3NQUW44RVBMZlpZaWNMUVFPUDFKTjM0VEZrd3BROWFxWlZzRHROdzlCaTFac2dsSTJ6VGc5bkw3b21MRVJJcnI3VjBjSGYrVXlKcytsSFNGVW1KQ1pXcisvNW1leVFicWg0QTBOWDlEcTBLN1RIZmFleWtjUTI4eGNFNUp4aU5rOEJ2VFJwM1pwUGV5RVlWVXJsVVB6UjA4TFMvMzRLTUw0TWtOUnZ1UEhWNTJIdjRLTzBRQ2s2SFZaV01Ib3ZLZ2lpdFY0elpnQ0k5RmVheSswbDRFdjc2aUFIT1BNTFcyVmkwN2k3RTA2ckZ6QjdBakJPdUMwQmFaM0JZL1JIcDE3aktPUi9nS0M2TlhidGlrRWZsajBhR3pDTFA5L3Rjd3A0Y01QTEMxdzdwV3Rram5kaS9EOWdtc01qaDZENjlXa0phb2p2c3FLWm9DT2hveTNPNTA5cWJuZTl0R0s0SzFDcisrbE16K2ZHdStjaDVzME1mY0ZmaG45ZXlOcm4rVzgrS0IrMjNpN1QyOVgvMXI3VGxNWS9yK3YweVVnWDN3NEhMNCs4aDhjMzh0b3RRbnh1aE1EMFVnMzUiLCJtYWMiOiIzN2YzMDcyZmU3MGFmZWJkMTRhY2Y3YTQ0YzhkMDE0MTEyOTAwMWU3YmQwYzU4NzZlMjQ1MjZkNzM1NWYyZWUyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IllpU1hnWWVscC9Uc3dRZUx5TUhlY3c9PSIsInZhbHVlIjoiaGgxblFVVU5jT1p2QXpzUjVQOFlaTlNzNzlwOEJQR1E1VWJJWmdsMkVmc2liMVprbXdXWEpHaCtpc2l3UTNrMU5sMk9jcjZKQ09UMnNUb2pISTJDdlFkNGowTlA5MnN1WGxMRmpYUktxTnV6NkpBdEYyYm4vVzNtcDYvQm5HMElBRnFscWY2Z0ZldnVCVVBsMEJYZ25odnBBWWszQXp1Q3RHNksyYkh1ZEtIVmFmam9Kd3JwemN2aEowMldIMGhwZU44a2xWTmR1M3Bhd3JyV2UvM3NaTFNOeUdvWmxtMzRRdk1LOHFQSDhoeXQxQ2FCeWsvbTg5MWJuUXgzSkN1SWk5OVorWFVob24zVHphSXV6Sk9pTmIvS2dTV25nT0wwcitIT1JPY3RVcE9wN29KSG04UnBXSThtSThWcVE5TTQ3RUNjaHlsRWNuQ2hVS3hteTNzL3o0b0dkY2wvMDZ5SWltTEVLSEdLbkx5cHhzSFU3Qkh3M2lOWmRRRXdmajNYc3hQelFBUVpleTlaMG92QURQR3BrZ3FJZ25TVStDbmFQdWN6UTZpM2FPM2Qycjk0YXlpbUZCamJFcU9IeW1SWk56QzRBM3l1K25CRms2VnJYQmtONUNXdm5hTkh3ZWszQzVXVW5IZlRrS3NmYXlmajI1dVpzL2N5eUIxcTFTSlAiLCJtYWMiOiI4NjgzODZhODRkODhkYzQ2YTcyYWJiZjMwMWUxODAxZjliNDljM2ZjYWYxZjZhNmU0MDBjMGNlODU5ODc4NjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083787296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1594757515 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594757515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1041993042 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:37:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldOZCtiMWVxSjByaloydU00Qng2cGc9PSIsInZhbHVlIjoiOXB3MmxPdHZHNTBWcmRBWHFQSFhSQkRraW9qUTBYVXE2RXRRejd1bFlUajRzT3JDQmg0SkZmejRqM2RaUy9BbVdMK0U3dUlFTytLTzdIVkxYa0ZUVkhoOGpqTE9NamR5eWVCVXpDNmQ2d0xadFlaLzNNS1Z6a2hjR1g5ZlY3d2lxek93QkZ2ZHRWSDcwNTA3NG1aWnduUHBYSEhYUVNxKytxUzNNNStOdUI4RGI5TWg2UTQ0MWdkMGZwNkRSRWZzZENNczNrcDl5ZS9Ld3lhVWZoYnU1RHBZT3hhV1B0Qk9NU01pakd1c2FHZDA1Vmh2aVJRcFlGU2tJNTFDMXVtdUdEQVkwcllseFlCakZtcWVzN0xuUU96bzBJKzZTVThHRWVEdEo5ckRNRWdjejl1OURrRjllclhnR0x3Uy9nQ0lNbmRmOG81Ty9YaHF1c1pMOTNDaFQ2UU5wTUJ3WFVwQ1hKUGVwRjVZNjgzbVhwMVBua2hiQmoxeWdISDZKWU1Jekp5Q0NzVXFET0xieUp6VWVsbUVjVTQxa2RFcFNjeFBDaXdIUEEyNXYrTmpKd2JXR3lMblIzVkhHUHUwQjZrVmwzRzREd2srcUNtd3FiSVI4YXFxN29ac2pqWmwzNDdpN3BzK2FlYWNGeUdLR3pBS1BMVUFNUjNRclpUc0tsSGoiLCJtYWMiOiIzNmRhMzEyYTA5NDllNDZmZjg0MDZkZGMyMTUxMDgwNTcwZWMxNTM4ZTk0MTU5ZTU4OWU0NDhhMDQ0MmM2NDQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:37:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVGbE40Y2RodzE2MzFxcE9tLzJ3MlE9PSIsInZhbHVlIjoiU2pubzUrMUduYlV2akVrNTd5bjZjT2dwREoyRnYwSE9LeHRxTVRrVTZhN3grWWtkd2dJa3o4d2tJZ2M3enF0NzZVdFRVOFJKa0U1alJLZ1ZVMWJGS0lKSnAxaldMMG5JMWI2ZXc1eURnaDVLV2llQXIrbHZyK3FSTlpYVGg5QkRoanNzQkZZQlJvdEkwM3BIaHp4bXNOM3krM2tKVlJHbjAxRW5adXVUN1BTazFsckNVMEo3RFAvbktQcFlCL3R3cHRkaDBJbm5KU0k3aCtURHNIWDE3ZEVjMUFRa09SZlE1cDBGall6SWt0RnNlMVVjQlNkRDVmQzVxdnFWZGhVQm5seitUOU5FaFBTeHE4NW1MZnBKT1Y4MGVMY2gzWUhrQ0RDZXJMaGEwT3hBVW0vV0YvL2pOWVNvV1B0ekp0YkxMRUE1eGxmSGhxV250WURZamhqdU94UUEybE1NbU8wZlhsZkRTR0JDcXVaTDZzRkx4Q2lmdURzRnRxQmVTU0t2NTZDT3Y3bjVXbFZacWhneENSMmY5T1VQU2xHUXNFc0VaUVo1VDRvYWwwa0ZSWThuUGJHd05KY0RjSk1WMjhtRSt3LzgzTm9CdUx3TVZRN25LeXdqSE15MGNFd3Zpa0tYTThjbmZqNEdjSmpmc1BjN0lJRXRZMHMzcXhNRFRBOGsiLCJtYWMiOiJmOTZkYmU2YTVhNjYzNGJkOGQ0YjEyZjBiY2Q0MmUzYjAwZjI0MzdkMGIyN2ZmOTdmMWU3NDM2MWQwMDk1MTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:37:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldOZCtiMWVxSjByaloydU00Qng2cGc9PSIsInZhbHVlIjoiOXB3MmxPdHZHNTBWcmRBWHFQSFhSQkRraW9qUTBYVXE2RXRRejd1bFlUajRzT3JDQmg0SkZmejRqM2RaUy9BbVdMK0U3dUlFTytLTzdIVkxYa0ZUVkhoOGpqTE9NamR5eWVCVXpDNmQ2d0xadFlaLzNNS1Z6a2hjR1g5ZlY3d2lxek93QkZ2ZHRWSDcwNTA3NG1aWnduUHBYSEhYUVNxKytxUzNNNStOdUI4RGI5TWg2UTQ0MWdkMGZwNkRSRWZzZENNczNrcDl5ZS9Ld3lhVWZoYnU1RHBZT3hhV1B0Qk9NU01pakd1c2FHZDA1Vmh2aVJRcFlGU2tJNTFDMXVtdUdEQVkwcllseFlCakZtcWVzN0xuUU96bzBJKzZTVThHRWVEdEo5ckRNRWdjejl1OURrRjllclhnR0x3Uy9nQ0lNbmRmOG81Ty9YaHF1c1pMOTNDaFQ2UU5wTUJ3WFVwQ1hKUGVwRjVZNjgzbVhwMVBua2hiQmoxeWdISDZKWU1Jekp5Q0NzVXFET0xieUp6VWVsbUVjVTQxa2RFcFNjeFBDaXdIUEEyNXYrTmpKd2JXR3lMblIzVkhHUHUwQjZrVmwzRzREd2srcUNtd3FiSVI4YXFxN29ac2pqWmwzNDdpN3BzK2FlYWNGeUdLR3pBS1BMVUFNUjNRclpUc0tsSGoiLCJtYWMiOiIzNmRhMzEyYTA5NDllNDZmZjg0MDZkZGMyMTUxMDgwNTcwZWMxNTM4ZTk0MTU5ZTU4OWU0NDhhMDQ0MmM2NDQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:37:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVGbE40Y2RodzE2MzFxcE9tLzJ3MlE9PSIsInZhbHVlIjoiU2pubzUrMUduYlV2akVrNTd5bjZjT2dwREoyRnYwSE9LeHRxTVRrVTZhN3grWWtkd2dJa3o4d2tJZ2M3enF0NzZVdFRVOFJKa0U1alJLZ1ZVMWJGS0lKSnAxaldMMG5JMWI2ZXc1eURnaDVLV2llQXIrbHZyK3FSTlpYVGg5QkRoanNzQkZZQlJvdEkwM3BIaHp4bXNOM3krM2tKVlJHbjAxRW5adXVUN1BTazFsckNVMEo3RFAvbktQcFlCL3R3cHRkaDBJbm5KU0k3aCtURHNIWDE3ZEVjMUFRa09SZlE1cDBGall6SWt0RnNlMVVjQlNkRDVmQzVxdnFWZGhVQm5seitUOU5FaFBTeHE4NW1MZnBKT1Y4MGVMY2gzWUhrQ0RDZXJMaGEwT3hBVW0vV0YvL2pOWVNvV1B0ekp0YkxMRUE1eGxmSGhxV250WURZamhqdU94UUEybE1NbU8wZlhsZkRTR0JDcXVaTDZzRkx4Q2lmdURzRnRxQmVTU0t2NTZDT3Y3bjVXbFZacWhneENSMmY5T1VQU2xHUXNFc0VaUVo1VDRvYWwwa0ZSWThuUGJHd05KY0RjSk1WMjhtRSt3LzgzTm9CdUx3TVZRN25LeXdqSE15MGNFd3Zpa0tYTThjbmZqNEdjSmpmc1BjN0lJRXRZMHMzcXhNRFRBOGsiLCJtYWMiOiJmOTZkYmU2YTVhNjYzNGJkOGQ0YjEyZjBiY2Q0MmUzYjAwZjI0MzdkMGIyN2ZmOTdmMWU3NDM2MWQwMDk1MTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:37:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041993042\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1566294672 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566294672\", {\"maxDepth\":0})</script>\n"}}