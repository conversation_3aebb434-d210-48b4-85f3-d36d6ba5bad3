{"__meta": {"id": "Xf5897b434428865b740f9a13ec154298", "datetime": "2025-07-31 15:54:07", "utime": **********.330934, "method": "GET", "uri": "/finance/plan/get-product/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977245.035457, "end": **********.330988, "duration": 2.2955310344696045, "duration_str": "2.3s", "measures": [{"label": "Booting", "start": 1753977245.035457, "relative_start": 0, "end": **********.047897, "relative_end": **********.047897, "duration": 2.0124402046203613, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.047936, "relative_start": 2.012479066848755, "end": **********.330994, "relative_end": 5.9604644775390625e-06, "duration": 0.28305792808532715, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51530888, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/plan/get-product/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getProduct", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.plan.get-product", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=233\" onclick=\"\">app/Http/Controllers/FinanceController.php:233-257</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0117, "accumulated_duration_str": "11.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.202472, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 46.923}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.241225, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 46.923, "width_percent": 10.855}, {"sql": "select * from `products` where `products`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.252271, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 57.778, "width_percent": 9.573}, {"sql": "select * from `tax_slabs` where `tax_slabs`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.265047, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 67.35, "width_percent": 7.35}, {"sql": "select * from `product_emi_options` where `product_emi_options`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.276411, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 74.701, "width_percent": 8.718}, {"sql": "select * from `product_shipping_fields` where `product_shipping_fields`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.284498, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 83.419, "width_percent": 7.607}, {"sql": "select * from `product_bump_offers` where `product_bump_offers`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.292268, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 91.026, "width_percent": 8.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\TaxSlab": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTaxSlab.php&line=1", "ajax": false, "filename": "TaxSlab.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/plan/get-product/2", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1515625922 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1515625922\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1380744914 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNlTU5rUkRPNkFKbGl0WFJub3dtYnc9PSIsInZhbHVlIjoiUnhkSzhOOUhuWUhQY0FqbHhoM1B0a3hoYXNoQmdQVXRwNkcrbGpuMUlCZmFXSFFJeGU2Qm10NS8rY1BYMUxXMDNwbVRmazJTZXRqRnBlR0dhcWpnSWszS1diM3lOaE1Uek5EQ1JpdlhzUm5mc1pxMExYcFcxaHVsK1ZhNnVVRWI2c3dNTzBMeWwweUwzdWIyVkhQWmthMmRlZlhCb3EzdHdFaHF0Z2s3WUFDVU9rNUZxbHhxLzZSMDI2am1TclRFakg0NFNvK0ZibjUwK0FpUCtvNTN3Y2JwQWNBWXRia2hlL1ZBNjRZcFNDNFFyajVscjkvQ2hleUc3eEt1aVBiQVg5ZkhxUC9FczNFeTFGY2VscnhwUmlQOFpOeXh4RUlDQTg5c2Q0ZjBwaW5lL3Vtam1lcmpMZUhLZVVNN3dmc0VZdDlQM2xPVzZaWDBLN0RJN2hoaUxKTUQ0WXdjZVUrUlhURTMzRGFmdWFVWE10aFU0UlN2cncxRlFvbFptMkpHNGM5SktURmVLM3dFTWQ1QkJ3MFM1Unp3MkZiS1JQY0gvanQ2R1ZpUzhzaXJhMTU0dVVISUtjRmxsdTdoVDlLdjhnOFVUVzQ0MEQvMFovL1hXTm9NQ3ZiNGpOUEFjTDFkbUpkYmRXU1BPYTdhSjcySWF6YWJkVUpXQ2NqdjlGSXYiLCJtYWMiOiIxMTQ0NDM2ZDI3NzkwMWFkYzUwMmNlMzQ3ZDBmZWYzZDc5ZDAyOWI3ZDgwM2M4MmRlMDk1Mzg4NDQ1NWZmM2FlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkkrMXZmK2lBMUxOV3hTV0RQZ0NJanc9PSIsInZhbHVlIjoiRjQ4L1FwbG40a09oQ0J6OG54RDFucXVLaVhDYUtoUkQyNWh5Sjc3bHBNYnBGS3pPcEVVUmY2NytIeW82SjIrWURUL2pJL1U1YS81WTVIdG9IbnlUdTJnUE1RWEhmVXV3YXZjbnF4WUpLQmxUem9Rb05HelY5eVpzaGdCbHVKRTdBRjJSckx0SitseGw5aG5zQVFBRlVhYkdRaHV4S0ZGaFBUTmpXYmpPcytOdkhxUVVyMmU1KzBWejNOcXpaK0s1eHZSRFZmRXB6VDB3elQrNGM3UzhEYWlQZElkWURzVXpvc3pGamxncGY5Z25XQ2RMZGNnR1FpR0ZvMjY2L0w4N05hSC9JR1NFVk5WTkVNQjhaNzBuVUVNdWpXZ2tFaDBMUVZ0dW9mQ1JTQlFNR05haHcvTXJXWWlqMVE5d2JmUXlMYnBnUU5ERFBmRmJ5VU1MOGZFb3lmT1I5VlFSL2hFR00zRWU4ZWM3bmZra1ZuNGVyUm1SbHIvc2dMR2YyaXE1Y1pKeVdvZXV1ZWtjcHMxWm1sZGs4SnlYMGZPUVhCbS9FOVRvdXBMc1R5a3VpYTF2RUxuUWlsUktEZWhtcnQvSkZ0U0YyRUtmaXpBNGNaT0RaZ29laktVTnNmUlpoK2crSU84bTVCaEVCZUxMS05PbUtJNmt2cTdlbmp6dy82YmsiLCJtYWMiOiI1YTFlZjViZGI0ZGNhOWIzMjUzYWM1MzQ3YmEyMzVmYzgyZWYyZDU5MzMxZDc2MzI0MjFmN2VmNzNmYjY4ZWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380744914\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-165221945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165221945\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-569963185 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:54:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkzMS8xT3RIVFY0NU82SzB5dEZjNGc9PSIsInZhbHVlIjoiL3JmWlRscnAwazBkQkJoa0RaZlhQZktQb2Ztby83eEl2ZXVJVWUzNnRMSlVJeE5oa1lkbW1WczNVQllibUFtNm5ocjdGb1VXdlh3R1F2UHpnejZDRDJnaWVvTXRVU0dPVUFTQ0VDMHYrcFVDUUNrcW5HSitxaU9VNnVlTitUZkhzOXA0S0ZwTFludkJ1RWdibm4wQUxlaVE3aFFkcWlPeXpxNVV2MExrWnJIZGs5enQ5amRLUkNDV0NjVXhaaTFVamNPYWNVUFZnQ2pmaTNieXFZZmt6Z2g4R1pZNGVVL2hQbk9SUWlMVDduazdZRXV5OWpOYkFkMHE1N0pHaUovREtNdXlaalhJK2JSZU1tQzNuZHlLRkFRcElWSUZVRnJVWmFTMkVIV21Sb3hHankrRnlRMnFjTkdGOXFIUm16SGdtLzgveXJWSEFZRmdaWmNRcy9lWmRsMm1rR0EwSVU2NkZPbTBTSDEvLzVtSDl5SWRKMDExQ1FLbzRpbWVpU3hKeTA3Q3p3MkQ2dVlFNTFRMWhHb0YxQzNjUjdFTGM0RTBHMEJqYlpubVJKVzZlYnNKU0JPVEpCRG5zMmc4czNBT05RS3hwQXptNDV3cEtSQ3IrOUlYZkhUNTBvSXFFbWZVZVVOUlBseEx0TkM5NjFMNDlwOGpkYUR6ZytOZDVYNkQiLCJtYWMiOiJiZTZkN2FhMTI1YmUwZjE4NGNiNWJhZDQyZDNlYmFkYjNiNTliYWU5ZTg5NjRmMWQwYWM0YmMwMjk5N2QyYjNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:54:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRqNW8xWDN1MDhNR1BtSkR4SkRreHc9PSIsInZhbHVlIjoiWm94VGxad0VXcWl2TU5wcXdHTHgxUzh3czRNWnpCOFJqY3pjNUwzSm1xVjY0QjJBZERlTHBwV1dlV2luUXRjMnpmT25hRHlzRFdlWnhHVnRXN0dDZUNDRVRBVDduSDNSMHFvREtaaUkxNHFCR050ekczRnhxOUVxUjBndGdVVDBUdEI2NEZRN1pscFpwTHJiNDk4czZBRmlaMyt2eUdlMXUwaFhRcnZUTkFRTVZ6RHIxZG5lMklIc1d3a1RsQTJZbms1OHQ2WlJieGRhNGx1bi9BWUtjV3NOWkdnai9ONFMzTEljOTNMTXR4bmlZdUd1ejkyZ2REV2tKUVBkN253b2c3bU1jbUZtU0hwTm9VTVZNalRGWlVYYk1BMERLTnZxekMxd3kvc2F6cWNuaFpKRWxyL28wR2ZrbUVZRVJ3WXNnSGVWRVAzbW02TEE5OE1xa2VMWGJncC8zZzl4bDd3VGVoaVJ2Qm1rTGlMRlpvNzJCN2c0WWF6ZFhJM1AzNjRWMHVBbGg2Q2NRNGVtenRlcHdKWUpBcjdxRjlodmhHczBpZnFJNUJzajFnZEdJcmhPRWhzNm5sSTJxQm9UaVpJMUtEVjEwOGhGTzZab0VGUW5NQzBqNkJzcHRvRjJxZ29LNEhvNU5sV256d3Mxem5qN2x5Vmx6Vi94blBHWjNSRzEiLCJtYWMiOiI4YmRkMzZiYzM1OGFhMDAzYjcxNjM0YmRkMWI1YmQxZTllNmFmN2E4MGEwNzYwOTg3NTRjNTRkYTc2ZjYwZDNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:54:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkzMS8xT3RIVFY0NU82SzB5dEZjNGc9PSIsInZhbHVlIjoiL3JmWlRscnAwazBkQkJoa0RaZlhQZktQb2Ztby83eEl2ZXVJVWUzNnRMSlVJeE5oa1lkbW1WczNVQllibUFtNm5ocjdGb1VXdlh3R1F2UHpnejZDRDJnaWVvTXRVU0dPVUFTQ0VDMHYrcFVDUUNrcW5HSitxaU9VNnVlTitUZkhzOXA0S0ZwTFludkJ1RWdibm4wQUxlaVE3aFFkcWlPeXpxNVV2MExrWnJIZGs5enQ5amRLUkNDV0NjVXhaaTFVamNPYWNVUFZnQ2pmaTNieXFZZmt6Z2g4R1pZNGVVL2hQbk9SUWlMVDduazdZRXV5OWpOYkFkMHE1N0pHaUovREtNdXlaalhJK2JSZU1tQzNuZHlLRkFRcElWSUZVRnJVWmFTMkVIV21Sb3hHankrRnlRMnFjTkdGOXFIUm16SGdtLzgveXJWSEFZRmdaWmNRcy9lWmRsMm1rR0EwSVU2NkZPbTBTSDEvLzVtSDl5SWRKMDExQ1FLbzRpbWVpU3hKeTA3Q3p3MkQ2dVlFNTFRMWhHb0YxQzNjUjdFTGM0RTBHMEJqYlpubVJKVzZlYnNKU0JPVEpCRG5zMmc4czNBT05RS3hwQXptNDV3cEtSQ3IrOUlYZkhUNTBvSXFFbWZVZVVOUlBseEx0TkM5NjFMNDlwOGpkYUR6ZytOZDVYNkQiLCJtYWMiOiJiZTZkN2FhMTI1YmUwZjE4NGNiNWJhZDQyZDNlYmFkYjNiNTliYWU5ZTg5NjRmMWQwYWM0YmMwMjk5N2QyYjNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:54:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRqNW8xWDN1MDhNR1BtSkR4SkRreHc9PSIsInZhbHVlIjoiWm94VGxad0VXcWl2TU5wcXdHTHgxUzh3czRNWnpCOFJqY3pjNUwzSm1xVjY0QjJBZERlTHBwV1dlV2luUXRjMnpmT25hRHlzRFdlWnhHVnRXN0dDZUNDRVRBVDduSDNSMHFvREtaaUkxNHFCR050ekczRnhxOUVxUjBndGdVVDBUdEI2NEZRN1pscFpwTHJiNDk4czZBRmlaMyt2eUdlMXUwaFhRcnZUTkFRTVZ6RHIxZG5lMklIc1d3a1RsQTJZbms1OHQ2WlJieGRhNGx1bi9BWUtjV3NOWkdnai9ONFMzTEljOTNMTXR4bmlZdUd1ejkyZ2REV2tKUVBkN253b2c3bU1jbUZtU0hwTm9VTVZNalRGWlVYYk1BMERLTnZxekMxd3kvc2F6cWNuaFpKRWxyL28wR2ZrbUVZRVJ3WXNnSGVWRVAzbW02TEE5OE1xa2VMWGJncC8zZzl4bDd3VGVoaVJ2Qm1rTGlMRlpvNzJCN2c0WWF6ZFhJM1AzNjRWMHVBbGg2Q2NRNGVtenRlcHdKWUpBcjdxRjlodmhHczBpZnFJNUJzajFnZEdJcmhPRWhzNm5sSTJxQm9UaVpJMUtEVjEwOGhGTzZab0VGUW5NQzBqNkJzcHRvRjJxZ29LNEhvNU5sV256d3Mxem5qN2x5Vmx6Vi94blBHWjNSRzEiLCJtYWMiOiI4YmRkMzZiYzM1OGFhMDAzYjcxNjM0YmRkMWI1YmQxZTllNmFmN2E4MGEwNzYwOTg3NTRjNTRkYTc2ZjYwZDNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:54:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569963185\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1130085686 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130085686\", {\"maxDepth\":0})</script>\n"}}