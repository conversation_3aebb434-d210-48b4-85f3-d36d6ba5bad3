{"__meta": {"id": "X42c589976131f59649a4f046d4c7e145", "datetime": "2025-07-31 16:55:02", "utime": **********.963997, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980900.541393, "end": **********.964047, "duration": 2.422653913497925, "duration_str": "2.42s", "measures": [{"label": "Booting", "start": 1753980900.541393, "relative_start": 0, "end": **********.813093, "relative_end": **********.813093, "duration": 2.271699905395508, "duration_str": "2.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813161, "relative_start": 2.****************, "end": **********.964051, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "FbBTqyvoGSBIYzJOWfdKYTN82eH9wTZv1kzJnNWM", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1277316943 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1277316943\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-586587580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586587580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2076680921 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2076680921\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-803858099 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803858099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-103943434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-103943434\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2146653536 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:55:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBES2pUajBOQ3ZZdHJTMzJhYWQ3b0E9PSIsInZhbHVlIjoicitIeVh1SHp2MGU2NUwzREkrMGNOUHdGcjFaN083dTBQQ0w0ekc5bzJTNG1PSkQ5L29Udnd5ZVRhUlk2bXFIcDRqZ3FENGg1SG9DSWk4Ykx1ZlR5SDJKVWJScnRtQjFzZXJPOVdpNDIwN2o2ajdmTDk5amVTRnN1Rkh2b1RkUVJteE5iTG14cHRjNGJuYS9adUpESWJzM1JGWEhoRkpTcUVPT0pLV2NDd2ZaeHlCVUY2OFhQaTkrVHplc3lHQzlSZUw3d2tKYTdGNlJ1VWpXK3RIdHdBMEVVRzBFSWFuMDZBbUJFTld0bTNjK01XWTVoekFDZHBwWXJpUEtTZlllSHJCMUxQTlhldkNRT1dUanEvUmhtTzQ4a0Q4L0dtWU12NVJKV2JxelJRM2JqaWxXT1Ewb3V5S0Q3dnduUFRxVm5UYjk3WlRnRHFsYzJ4UWJhbnpXT2lEeVN0Z2g5VmQ3ZnlhUTlmRGJUK2srZzBRa0hXemxTUXJYaFM5VGpJb3FaS0dZWEFHeStkRVppUWZZTGNqc3VlMVFoMENYM25PMUhTMUVRUkpabU9uNU1mYUJYVXJkd2ZCTkdWOStNb0pZVlB2cXRpeW41QnNuc1ZBMjZhNjVGa3lFdjJoSFhadklzVk5wM1NvNFRTUDUrUkhOMGRIKy9zNCsxRFp6am54aUoiLCJtYWMiOiIyZDA4NGRhOWQyOGZhOWFkMDk2MjZiNzE2NDAwMTg0NjJlMmRjZTdlNjZkMmFjYjdkMDdiZDU4MWIxNDUyMDdkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxmUDhDRWVWdWJFbTJNTVgrOW1BTXc9PSIsInZhbHVlIjoic2xxR3pQN3Vwa2lqUlhVQno1MmtjcHI3R0JUcmxkbGNIb1YzV0I2MWlUZ09LOVVrVTRiUFF2RWc5T2djOEg2Z2Q1bUhWNDZ3dkVpUFFqVDVSQUpoaUpDZFcxdlFOUkxUU3pHREVXWjkvYVJlU3V3VTlQYzMwc1VTSkU4b3RvakIrdkVLbkEzNGZHQWpoRGl5dmJ5Q0MzU2QzRHpMbjcwY3ZYZnFaMlJpcjhzalVldUdLeXhwaUtpYWcwMXprWmEzTmg5Vm9hM0VXeDRlVG1nUVZydGRSR1dudW5nZkRZU3VWM1VIU0hVQTNRaUk4UVo5dUpLZ1dQcVV1dEZKWHkwRjRETzhMYzJvSkZBV25XZUFpYzhuNjROak5mYnlGRnhyYTluclZMelZQN0JJYkZjSXV6WWpsYUVlYUFBMVdHOG44WGhtbEY3MnBDZVBYQmtXZVVrU0x0Wmo4SzBWcUdybW4zWWlJOWY2akVob3hSSWtHcU5ZdW1PVzVEbC91SnBzZlRtSjBFb1p2eklVZHlKUm9lYU5DcmlReW9kQnZQNDd4bE5PNFNUU1ExMDRLdlkzblpMZDM1blJwYitDUDE3N3BmQTNuU3llWVFqNWtVUnpMTGl0ZkZLME94WWF2ZTlEQndJUVVyRndiN244bzAvY1l4bE1sOUFrWllxek9LaEsiLCJtYWMiOiJmODk0MDhmM2E3NWYyNTE3ZWIyMmEwZjQxYzUxMjExNjAwNWFmZGI3YjIzODU1Mjg0YzBjMGM5NWM3ZWFmNTA2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBES2pUajBOQ3ZZdHJTMzJhYWQ3b0E9PSIsInZhbHVlIjoicitIeVh1SHp2MGU2NUwzREkrMGNOUHdGcjFaN083dTBQQ0w0ekc5bzJTNG1PSkQ5L29Udnd5ZVRhUlk2bXFIcDRqZ3FENGg1SG9DSWk4Ykx1ZlR5SDJKVWJScnRtQjFzZXJPOVdpNDIwN2o2ajdmTDk5amVTRnN1Rkh2b1RkUVJteE5iTG14cHRjNGJuYS9adUpESWJzM1JGWEhoRkpTcUVPT0pLV2NDd2ZaeHlCVUY2OFhQaTkrVHplc3lHQzlSZUw3d2tKYTdGNlJ1VWpXK3RIdHdBMEVVRzBFSWFuMDZBbUJFTld0bTNjK01XWTVoekFDZHBwWXJpUEtTZlllSHJCMUxQTlhldkNRT1dUanEvUmhtTzQ4a0Q4L0dtWU12NVJKV2JxelJRM2JqaWxXT1Ewb3V5S0Q3dnduUFRxVm5UYjk3WlRnRHFsYzJ4UWJhbnpXT2lEeVN0Z2g5VmQ3ZnlhUTlmRGJUK2srZzBRa0hXemxTUXJYaFM5VGpJb3FaS0dZWEFHeStkRVppUWZZTGNqc3VlMVFoMENYM25PMUhTMUVRUkpabU9uNU1mYUJYVXJkd2ZCTkdWOStNb0pZVlB2cXRpeW41QnNuc1ZBMjZhNjVGa3lFdjJoSFhadklzVk5wM1NvNFRTUDUrUkhOMGRIKy9zNCsxRFp6am54aUoiLCJtYWMiOiIyZDA4NGRhOWQyOGZhOWFkMDk2MjZiNzE2NDAwMTg0NjJlMmRjZTdlNjZkMmFjYjdkMDdiZDU4MWIxNDUyMDdkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxmUDhDRWVWdWJFbTJNTVgrOW1BTXc9PSIsInZhbHVlIjoic2xxR3pQN3Vwa2lqUlhVQno1MmtjcHI3R0JUcmxkbGNIb1YzV0I2MWlUZ09LOVVrVTRiUFF2RWc5T2djOEg2Z2Q1bUhWNDZ3dkVpUFFqVDVSQUpoaUpDZFcxdlFOUkxUU3pHREVXWjkvYVJlU3V3VTlQYzMwc1VTSkU4b3RvakIrdkVLbkEzNGZHQWpoRGl5dmJ5Q0MzU2QzRHpMbjcwY3ZYZnFaMlJpcjhzalVldUdLeXhwaUtpYWcwMXprWmEzTmg5Vm9hM0VXeDRlVG1nUVZydGRSR1dudW5nZkRZU3VWM1VIU0hVQTNRaUk4UVo5dUpLZ1dQcVV1dEZKWHkwRjRETzhMYzJvSkZBV25XZUFpYzhuNjROak5mYnlGRnhyYTluclZMelZQN0JJYkZjSXV6WWpsYUVlYUFBMVdHOG44WGhtbEY3MnBDZVBYQmtXZVVrU0x0Wmo4SzBWcUdybW4zWWlJOWY2akVob3hSSWtHcU5ZdW1PVzVEbC91SnBzZlRtSjBFb1p2eklVZHlKUm9lYU5DcmlReW9kQnZQNDd4bE5PNFNUU1ExMDRLdlkzblpMZDM1blJwYitDUDE3N3BmQTNuU3llWVFqNWtVUnpMTGl0ZkZLME94WWF2ZTlEQndJUVVyRndiN244bzAvY1l4bE1sOUFrWllxek9LaEsiLCJtYWMiOiJmODk0MDhmM2E3NWYyNTE3ZWIyMmEwZjQxYzUxMjExNjAwNWFmZGI3YjIzODU1Mjg0YzBjMGM5NWM3ZWFmNTA2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146653536\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146499703 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FbBTqyvoGSBIYzJOWfdKYTN82eH9wTZv1kzJnNWM</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146499703\", {\"maxDepth\":0})</script>\n"}}