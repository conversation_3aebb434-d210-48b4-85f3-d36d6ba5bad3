{"__meta": {"id": "X06a6f75b123a6480b31d0a386f0570d8", "datetime": "2025-07-31 16:16:24", "utime": **********.990659, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978583.242564, "end": **********.990784, "duration": 1.7482199668884277, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1753978583.242564, "relative_start": 0, "end": **********.761255, "relative_end": **********.761255, "duration": 1.518691062927246, "duration_str": "1.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761279, "relative_start": 1.5187151432037354, "end": **********.990796, "relative_end": 1.2159347534179688e-05, "duration": 0.22951698303222656, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47012640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02682, "accumulated_duration_str": "26.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.868948, "duration": 0.021920000000000002, "duration_str": "21.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.73}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9220061, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.73, "width_percent": 6.376}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.937205, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 88.106, "width_percent": 5.854}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.949413, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 93.96, "width_percent": 6.04}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1423509953 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1423509953\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1009945306 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009945306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-716959182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-716959182\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InY3VXRJOWNlUEUwTzU1K3VhQ2lUQ1E9PSIsInZhbHVlIjoiWnZMeS9GbTlwR0JGeVVFK1I2THovSW5wWXFJMnU3Z3Z1ejhDMVA5ditiNVdHZlhKb0RWd3hZY0laV1dCcm9CcUwxMFR0YTB5d1VLL1RFdVFxclFBNTMrWWlIektTTlZCV1Z1T0VJdjY4UW9JbG4yVVlWRndMWjNSMnVVN3A2RUJlVWlWSWhNcE8yWG5nMkhKUExIU1lwYi9rM3A2Q05yLzA4cm83cHJjR3dBOFoyNkNmQzhIV0ZFYVFCK0ZzZkpJOXkxT3B3M2hXemZvdkEvQWpybkJtYmVyV3lvK3BHL1hWQkZRR1ZSVUR1Vjd3NDMyNngvTzhCWXVINTlvb0RacFFvVkEzVHJPSm1RTjhHTWhqWFlkaURqUVNBdTRqYWp1Qmt1NGV0azVBYTRXNUVMOVh4Q1kwY1ByOUJuaGhZNmFZZlBYaWpvWFBYL2wrVG5yQ2R6b2Z1b3BNQWxsZzk0b0diTG9jUVQ2Q0NQbjNjL0VVNDlXT202b291bDZYYjRFNzZVRnVHeEVNeEs5M3BvVHgxcDJFYU52MWg3OXlBSSs3Wm5hRW14eGhwSUwwUTh5ZFI2TmQ1SGlwMkMwWEdocnFHYXRRc2FMaFkrR3VBMjdIQUlGWEJOSTBlK0hNNm1MaVQ0eStDc0tlOVVYQjhma0p3UGQvZkQ3aVNpMXlyZE4iLCJtYWMiOiIyZDRmY2M3NmRlODk3MjU2MmM5ZmY3OGViMGMwNGNhZTA3ZmE1NzY1ZTEwZDQzOWM4ZjMyZjdkOWQ1YjljOTMyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVLb3l4aXhtOGw5RFB4c1BtZjh6NVE9PSIsInZhbHVlIjoiTjBsSkdPdStGemhTZzNmV2s0S1IzREhqRE5iQ0dlU3lRU3ZiYnRBeGtVVXJLQXFFUVZVa291QTJsQXZVT3hhMnFBNmZ6YWt2cmJkRW5KYjFJMEVDYjJndThOOU9Oa2hYckRVNGNjR0puV2dsUEJ3d2V3V1FzYzQ0T1UwMUorL05OTGRsRHZZeUsvbXVMdndsTEpEaW5RK1FXY3EyNDJaekQwcFFxNktmT3JpUWZ5VmVSZjBxNnN6ZmhSWXB4ME1rMTNxUm14b3ZnWjRQQU9NTm44NG5oV0RydDdRQ3RtMnlpM0h5LzZYTU9VSjgyaEZqWVNXMlFXdmhsSkVLQjcycFZSV3dTM1lDL3BYaENud3dOL1lQU2cwdDlwUzMwcjd6M0NnWWhTK3dreG5PTVBsSDJTdWxHcUIwMFZXak8rbG1mbFJ5YUpKQlN0azV1N21FYjNwb3huaWp2TTNFZ0VVZGNGbENPRkw3NTRvYiswQlpRdSt2bzhobm5HcTlHOExBRGN2bGxjMlJIVjJwazJPZVpQclhVZzV6bUczaXVnY3FtODgybkxWWlBHMmZZSndWaGx2WU94Q0Radk1KSmtVTmY0bEEwNFBHa1R5UFdSa0RPVW1zNFNoL3p3Wkw1TnJMNUpPMkJwWGowZ0xSc1ptVFhtaEdxREpYclJDQnQ3MWMiLCJtYWMiOiIxNGQ3YjhkMTE4OGIxOWY4YzAxMTU2NzczMmRiM2Y1ZmQ2YjM2YjIwNjNhM2E4MTM2NzE3ODcxZWI0YzhmMzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1825765062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825765062\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:16:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitzU3VPMHhPMDgrSWZuc1lEZnJPRXc9PSIsInZhbHVlIjoiTXBNaVg0cS96ZWFCY2lTSWEwU1JwSlZFeXFiWUM2VCtoU1NheXJncGllRFZHOVJxOVgxZW9WK000ZThIK2ZPVEh1aUltMmxMSW5jcEl6cU4rYXZwVVYzTUd0bTN0dDBFSkxGS1RsZGhJeFRkZWJmUTVTamhoUEdPazRTaVpQSEEvazdNeGJUUWRnT0gxQm1VQnM5VVAreDNadXExQyttd2c4RW5KSGtYbnJNakVCUmhKUnhFRDdkZERma0tUVDhhbFdyMGxPSUlwbnFoeWlkazdKU2prKzdKTU5iMWowaTh4SVd6d1o1VTRVcnhScjBPeTlhYW9KRGE1VWV5WW9JZEl0R1BydHBadHBvMU1tZVdqNFN1SWJOYVV1NnVCOW9vbHB2Z3NYNWZrZlVoK2REZE1YbFVhaDBOZXdQbm9LajBpbW9jL0xNaUJxMTFRSXU4YzVnckw3VnpVQitFNTBWUDlpS3MzanlLaFMxa2J5cmVRMXR0RTlJTzI3VWVBQVZsSW1QbmRLTmdMZzZTV05Ga3g3bm1TYXhLR2toc25tNlJ4eWRJRFBxdEptMjk2S2taUUN3QzlyS3J6dGpVbzhhTHAxZlpHREovSUJTYys2bnp1NXFrcThDOFdHYjNhb3NycllmSjMxQjBuZm1sem1MVzVSYTlldmNtTmQ1MzFuSkEiLCJtYWMiOiIwZDlmOWEzZGFkYWZlYjQ0NDRhZjZhYjNiYTUwYWU3MGZlOTZmNjZhODU1OGRiM2M0ODMwZTQ4MDg3MDAzNGQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:16:24 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImJaWE1iZEV3UkY1bEVicTFsM0FsTHc9PSIsInZhbHVlIjoibkl0cElDd2g2ZmJ2T0p4WFFFZ0I0bkxTR3BrK0ZDekpINjFVOXoxb2luOGo5TW9CTW44aWJZK0MxU3FkRlRTZWlzTE5PMTArUE9RY1p0ejFVeDhFUWVGV3BHM1IwWnNKVWdhYi9HVTQrMFlTMFJEbTVtTUhGUXQrSVBidDNMSE1vRGFkVS9VTVo3enIwamxHMVozdnplZGh2bVNwTTBDa21ScUtYWCs4MUVkdHdUcFJqOEY5MUdhMi81dDRsQlhsVEp0SWg3bWZ5ell5M2pIU1NuZ0RIa1p6K0hOYWZQWDFZMEp1UUdobW5xei9kcHVTcTV2ZjdTR3VuT0ZnL1ZoRVQzWVNCaitTaWtsT2Q4dFdhU0h4M1FBdkN0Zml2YmdnNU1VZ2xOOWFLWWFka0o0emxXZ3BYTHlCR3h5RVRucGVIY1pwTmFwQ3dXUjVOZUc3cXRNMmNHS2xWT0tkWXMvM0xQRytPVmFKNVV6aGpnR0o5bmVyWlZ0TXJkcy9ySnppWTVLcmg4aE9DWVNXcnJlS3VOcitwaWd6ejEvZ1JvdWxDZy9YTEI0cmlIWFZFaWNxNDQ0VEd3OWRacWZFNkpOSGlyUTlNclJwb3RKcXhMcHFnR0p3dkx6MVpzYkNpSWMyUjBRbkVnWGZWeFJhU0p4N2VJcjdpTVNKbHZnbE42Q0kiLCJtYWMiOiI3Mzc2M2U0ZmUxNTkyODYwYWRkZTc0MWRkMzMzYWMxZTE4YTAyYjNlNzI5MGFhMzgyYzBlNzU3MjQ3ZjUzNWFjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:16:24 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitzU3VPMHhPMDgrSWZuc1lEZnJPRXc9PSIsInZhbHVlIjoiTXBNaVg0cS96ZWFCY2lTSWEwU1JwSlZFeXFiWUM2VCtoU1NheXJncGllRFZHOVJxOVgxZW9WK000ZThIK2ZPVEh1aUltMmxMSW5jcEl6cU4rYXZwVVYzTUd0bTN0dDBFSkxGS1RsZGhJeFRkZWJmUTVTamhoUEdPazRTaVpQSEEvazdNeGJUUWRnT0gxQm1VQnM5VVAreDNadXExQyttd2c4RW5KSGtYbnJNakVCUmhKUnhFRDdkZERma0tUVDhhbFdyMGxPSUlwbnFoeWlkazdKU2prKzdKTU5iMWowaTh4SVd6d1o1VTRVcnhScjBPeTlhYW9KRGE1VWV5WW9JZEl0R1BydHBadHBvMU1tZVdqNFN1SWJOYVV1NnVCOW9vbHB2Z3NYNWZrZlVoK2REZE1YbFVhaDBOZXdQbm9LajBpbW9jL0xNaUJxMTFRSXU4YzVnckw3VnpVQitFNTBWUDlpS3MzanlLaFMxa2J5cmVRMXR0RTlJTzI3VWVBQVZsSW1QbmRLTmdMZzZTV05Ga3g3bm1TYXhLR2toc25tNlJ4eWRJRFBxdEptMjk2S2taUUN3QzlyS3J6dGpVbzhhTHAxZlpHREovSUJTYys2bnp1NXFrcThDOFdHYjNhb3NycllmSjMxQjBuZm1sem1MVzVSYTlldmNtTmQ1MzFuSkEiLCJtYWMiOiIwZDlmOWEzZGFkYWZlYjQ0NDRhZjZhYjNiYTUwYWU3MGZlOTZmNjZhODU1OGRiM2M0ODMwZTQ4MDg3MDAzNGQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:16:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImJaWE1iZEV3UkY1bEVicTFsM0FsTHc9PSIsInZhbHVlIjoibkl0cElDd2g2ZmJ2T0p4WFFFZ0I0bkxTR3BrK0ZDekpINjFVOXoxb2luOGo5TW9CTW44aWJZK0MxU3FkRlRTZWlzTE5PMTArUE9RY1p0ejFVeDhFUWVGV3BHM1IwWnNKVWdhYi9HVTQrMFlTMFJEbTVtTUhGUXQrSVBidDNMSE1vRGFkVS9VTVo3enIwamxHMVozdnplZGh2bVNwTTBDa21ScUtYWCs4MUVkdHdUcFJqOEY5MUdhMi81dDRsQlhsVEp0SWg3bWZ5ell5M2pIU1NuZ0RIa1p6K0hOYWZQWDFZMEp1UUdobW5xei9kcHVTcTV2ZjdTR3VuT0ZnL1ZoRVQzWVNCaitTaWtsT2Q4dFdhU0h4M1FBdkN0Zml2YmdnNU1VZ2xOOWFLWWFka0o0emxXZ3BYTHlCR3h5RVRucGVIY1pwTmFwQ3dXUjVOZUc3cXRNMmNHS2xWT0tkWXMvM0xQRytPVmFKNVV6aGpnR0o5bmVyWlZ0TXJkcy9ySnppWTVLcmg4aE9DWVNXcnJlS3VOcitwaWd6ejEvZ1JvdWxDZy9YTEI0cmlIWFZFaWNxNDQ0VEd3OWRacWZFNkpOSGlyUTlNclJwb3RKcXhMcHFnR0p3dkx6MVpzYkNpSWMyUjBRbkVnWGZWeFJhU0p4N2VJcjdpTVNKbHZnbE42Q0kiLCJtYWMiOiI3Mzc2M2U0ZmUxNTkyODYwYWRkZTc0MWRkMzMzYWMxZTE4YTAyYjNlNzI5MGFhMzgyYzBlNzU3MjQ3ZjUzNWFjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:16:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-889243925 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889243925\", {\"maxDepth\":0})</script>\n"}}