{"__meta": {"id": "Xb463a895f48839a361ccc6c38eaa8a04", "datetime": "2025-07-31 16:14:46", "utime": **********.233715, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978484.373359, "end": **********.233793, "duration": 1.8604340553283691, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1753978484.373359, "relative_start": 0, "end": 1753978485.817895, "relative_end": 1753978485.817895, "duration": 1.4445359706878662, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753978485.817944, "relative_start": 1.4445850849151611, "end": **********.233799, "relative_end": 5.9604644775390625e-06, "duration": 0.41585493087768555, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47012640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.039999999999999994, "accumulated_duration_str": "40ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.023739, "duration": 0.030989999999999997, "duration_str": "30.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.475}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.122448, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.475, "width_percent": 5.85}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1474378, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 83.325, "width_percent": 7.175}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.172213, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 90.5, "width_percent": 9.5}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-709803786 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-709803786\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-72111531 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72111531\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1378993361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1378993361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1511563369 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IllmVTFEVy9WTENKTWo0aUdCbDVaVGc9PSIsInZhbHVlIjoiK3hkb0N4bnJYaG5GcDNBQyttejJ3b0pzeE5heThDQzZpQ2ZjcGNCeUlDNTdXcTFib2hHUmY5Y3FlYzBwS095eEFHenFUandQRVEvV0FKMlJleXBtZmRHQWpNNnBlb2RtRXdJOUNUUHg0aXhOQ1dTaW9BYytlM0dzSm02WjR5bDlueTJFbk1RYTVVSUx0cWxpd2xxNHM2NVM2Q1VyS3YwditHY2VQTHVEeW8xeEU2SEdSaVdOenlGeFFtc2xwVjdHOU54cHBNWWd0NjgzbHVFclpkOFhlN2xaM2FrbysrY0Vhdk1RTk8za05GUTZCenNrMkpkZkZSMG1kTGxrd1RqL3grOGtYT0hJY1lwRTdyay9za091ZUdaekxSbDBqTE83ajVZdUNZanhWRFhyeW9KbnBabm9PNTJJa2JxWEU4N2xrTkNaT3BjaFhXakZ6cDllY3A3M05QZUg4VlRRcERObmlsS0piUXdqcTF3by9IcjFCdENKTnFnRnZMcXpPb1NudHpzL2dlYkdna0pvNVUvQms5Z2tXOEdiWGFTakJoNHlJcURKZXZhT1B5eWducklsQ2pmYTBxOXJoMFNmaFd1V2srUWpueEdaaE0zYi9LdXBnZDdBZUJJZGhySXdhWGhna2w4TTJFYTc0RURRTTRRemxwdWJLbTZBdWdSYnBQT1AiLCJtYWMiOiI5N2FlZjM5MGQxM2JjNTRiYTRmNTEyNzE4NTFiOWE2YjU5MDA4ZGJkZTQ0NjZmZTYyYzUwYjNlZmMxMDNlYzE0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImUrWVkxYWc0eHJiSUVxNDN6eFBWNVE9PSIsInZhbHVlIjoiSVRpbTRQNWc1czBlWTdyck5Gc2xFQXJncjJMaE5NTk15VllUd3FSbkZhSXlBT3FocG1ZbVM2cUVrOWtrTnpUeVFWZWhWRkt4TFJkOXBXNGRjSUpCVEpRUVJnT1JHQ2o5SU00cFZ0Q3lhQWhEay91ZUxUbUdiVE55aC9DTXZva2dPNURwam1pVlkwRmNXSU9PQWpvVGNGWXE5Nm1PZXhpK2MwL013bHJaa2lmM3hqY3RMamVCSy9DYjllVkNaVXJxU1Y5VjV2NHpkRWZma291cm4wdHpVR0tkZHRubFExNGcyaDNYd1NqbVZqbmpOeXp1eEdWSGREb0NOeHFtWnI3YXJPYXFiQXJnMHVGdlcwTmJwWGJNLzVkdU5YY3R2c0Q2RnpqTHN6U2hjckdyeFExYVlUMldPdnRZelJwTUpMamwySlFFTS9mMzM2SEk4NnZRSU93UjZ2ck5wTnBSNnIwVE9JaXBJWklpZ1owOUE1TUJnQ3ZmS0FVYWo4OG5WTDR4Zm55cXJaSlZTMEU4S0lEQnBUbDdkNkZmZE9JVXBtaUoyaHVVUC9SVklWNmNSMG4xNS9NRm9FZVF6YitXRFhPOEcvK3VBOVdsT1dEVW9rNXBlUHJWMVEwQVBpM1pVMnNNeFltNmtMamhNSDlaT0lBS2krZDJOdWFVcTE1YWpxRnciLCJtYWMiOiJkODJlZDZjYTA5M2NmZmVhYzE0NmEwYmViOWNjNDFiYjUwOTBmMWUyMThjNTg1OTNhNTYzYTI4MGY1YjgxYjk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511563369\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1320740222 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320740222\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2121237961 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:14:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFqdXJwUkZBRnFUTEV6Y3FHb1YrVHc9PSIsInZhbHVlIjoiV3NlN3pPTHoyQVljYXpRSzIwbkRSSWgvaHNXRmdqR2RqWFV6Nk9hczdYd1BnZk95bW9VRUN4MUdqOWlCUnJsYmlHL1FLeE50OEdNeGZ6QTZLRFVCR2ZvY0hTVXZVR1daUGFSWFdtN0g2UVFiRVJZVjhENXZiZ3FUNXlkSXdRTzVGSG56TmZTdThsVHJkUytQR1gyaGNxVkMrdGNXMFVUZ1NvWGZzT1BRWWlJQTI0K05KRmlqRFphckFSb0FJQm1wN1dwTWZaaDVwVUJJRGRkUE5QS3JVaEhWaFVJZkZidkRhZzYzeEY2cGNIdE9qL1haQjJaUnpzRjhkVGljSWFrUU9CcldPZmxYSVBiQ09tcERndjFONlowR1MzZHlmRytXOTAySXRUdVEzWmJHUjhWeTNwcE84Um5aUEY4VXZWNjZmcjR5WGxweHdwQUZsV3NiU0Jkcm1nWDAyU0lvVWxNWUZyWm9IT1JqZmtsOTVwMG1yZ09mbmh2TGRyVkplSXhObFA2OElDNFZ5UlVIYUlBNmtIV3FVNUFra0p3QSswTnZqaVJhMTlsRVhMeXUrQmJXcEwrdUdWSFJQUTlWS2xVYXFxMTdmR3hXK3dsV3NCOTBCTExwTlA3eFgxcE5rZFhtL1l0Z01raFdXQXcybmxUdDRyaEFhVmM1K1AvOU1PWDQiLCJtYWMiOiIxZDEyYmMxNjVmZTQ2YWU5ZGZhNjM4NTQ3ODM2MTg4MmY3NjdmYzlmYzZkOGZiNmNjMGQyOGI1NzAxZmY2YzM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:14:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitmOG01SjN3SVRJWWlpaHdlSEtWc1E9PSIsInZhbHVlIjoiTUs3TTYwMFc4V2JKSUpRS2p6b2lsdjNtOExUSWdaT0ZYUldIS09NRDFHTmlSQ0N4REsyOVdpV2tVeXd3VXJMUTBjaHI5ZmdYNENKMXRNL2lXMnJGL0xuNXpzOE81d3lOMTBnejI5N2Rsa3FWQy9yMHQ0M3dXMU9yY25rc2hCNGRJaVpFSGhkTlRKbW5XSnl3V21NQXcxSVBLUzJYVWIwQ05hM3JVYWEyOGpNZHc1QVhoVTZzQnFxUGxuZmc3M1FOTWNyRS8zYWxhY0Y1ZUpKbnlFRVNvUkxueTJhS2hpWmVVcXNtYlYzVEovOFF0dm9pOTRKS2ZYcUtWVk1UUDhnQURWWG9kWHB3Q0UzZDhxazdZVGFMWE02cVA3Z09uUUE5enNUOGxIMXZQNTJTb21Jei94NXNKRnk3dTZUMVdiVmhDYkpVYzJsLzMreDIvN2FoUFBoTmM0NjhwV2E2QTBsd2YzcTM2S2x6emFTSytFdnFzWkhwRmVYT2pMdFVFY2tvWmp5WTZJdXVJVnZNcXZtQ3FlUlJLOGdrMVBOV2x2RFQ0NFQ4QjRyZC9NUVBsNStoWERQaUE5MTVWQVNUc1ZUS1VRQmZtZHdiVzBXMVBRRDRSQ3V1cGZ6V3IzTFRldWJydHVoZ3kxcDZNb0xPbldpRXY1TFdYci9WREdlT3hYaFMiLCJtYWMiOiIxZTcwMjEyMjlmNzY0MTM4M2RlNWRhZmZlZGZmOTJmMmI0Yjc0NTJkY2I3NWZlY2IzMTRhYWQ2YmI3MjY1OTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:14:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFqdXJwUkZBRnFUTEV6Y3FHb1YrVHc9PSIsInZhbHVlIjoiV3NlN3pPTHoyQVljYXpRSzIwbkRSSWgvaHNXRmdqR2RqWFV6Nk9hczdYd1BnZk95bW9VRUN4MUdqOWlCUnJsYmlHL1FLeE50OEdNeGZ6QTZLRFVCR2ZvY0hTVXZVR1daUGFSWFdtN0g2UVFiRVJZVjhENXZiZ3FUNXlkSXdRTzVGSG56TmZTdThsVHJkUytQR1gyaGNxVkMrdGNXMFVUZ1NvWGZzT1BRWWlJQTI0K05KRmlqRFphckFSb0FJQm1wN1dwTWZaaDVwVUJJRGRkUE5QS3JVaEhWaFVJZkZidkRhZzYzeEY2cGNIdE9qL1haQjJaUnpzRjhkVGljSWFrUU9CcldPZmxYSVBiQ09tcERndjFONlowR1MzZHlmRytXOTAySXRUdVEzWmJHUjhWeTNwcE84Um5aUEY4VXZWNjZmcjR5WGxweHdwQUZsV3NiU0Jkcm1nWDAyU0lvVWxNWUZyWm9IT1JqZmtsOTVwMG1yZ09mbmh2TGRyVkplSXhObFA2OElDNFZ5UlVIYUlBNmtIV3FVNUFra0p3QSswTnZqaVJhMTlsRVhMeXUrQmJXcEwrdUdWSFJQUTlWS2xVYXFxMTdmR3hXK3dsV3NCOTBCTExwTlA3eFgxcE5rZFhtL1l0Z01raFdXQXcybmxUdDRyaEFhVmM1K1AvOU1PWDQiLCJtYWMiOiIxZDEyYmMxNjVmZTQ2YWU5ZGZhNjM4NTQ3ODM2MTg4MmY3NjdmYzlmYzZkOGZiNmNjMGQyOGI1NzAxZmY2YzM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:14:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitmOG01SjN3SVRJWWlpaHdlSEtWc1E9PSIsInZhbHVlIjoiTUs3TTYwMFc4V2JKSUpRS2p6b2lsdjNtOExUSWdaT0ZYUldIS09NRDFHTmlSQ0N4REsyOVdpV2tVeXd3VXJMUTBjaHI5ZmdYNENKMXRNL2lXMnJGL0xuNXpzOE81d3lOMTBnejI5N2Rsa3FWQy9yMHQ0M3dXMU9yY25rc2hCNGRJaVpFSGhkTlRKbW5XSnl3V21NQXcxSVBLUzJYVWIwQ05hM3JVYWEyOGpNZHc1QVhoVTZzQnFxUGxuZmc3M1FOTWNyRS8zYWxhY0Y1ZUpKbnlFRVNvUkxueTJhS2hpWmVVcXNtYlYzVEovOFF0dm9pOTRKS2ZYcUtWVk1UUDhnQURWWG9kWHB3Q0UzZDhxazdZVGFMWE02cVA3Z09uUUE5enNUOGxIMXZQNTJTb21Jei94NXNKRnk3dTZUMVdiVmhDYkpVYzJsLzMreDIvN2FoUFBoTmM0NjhwV2E2QTBsd2YzcTM2S2x6emFTSytFdnFzWkhwRmVYT2pMdFVFY2tvWmp5WTZJdXVJVnZNcXZtQ3FlUlJLOGdrMVBOV2x2RFQ0NFQ4QjRyZC9NUVBsNStoWERQaUE5MTVWQVNUc1ZUS1VRQmZtZHdiVzBXMVBRRDRSQ3V1cGZ6V3IzTFRldWJydHVoZ3kxcDZNb0xPbldpRXY1TFdYci9WREdlT3hYaFMiLCJtYWMiOiIxZTcwMjEyMjlmNzY0MTM4M2RlNWRhZmZlZGZmOTJmMmI0Yjc0NTJkY2I3NWZlY2IzMTRhYWQ2YmI3MjY1OTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:14:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121237961\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-987075438 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987075438\", {\"maxDepth\":0})</script>\n"}}