{"__meta": {"id": "Xf7a1a8e59ed8814167682c12fefff0ae", "datetime": "2025-07-31 15:56:58", "utime": **********.732204, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977415.678387, "end": **********.732328, "duration": 3.053941011428833, "duration_str": "3.05s", "measures": [{"label": "Booting", "start": 1753977415.678387, "relative_start": 0, "end": **********.535719, "relative_end": **********.535719, "duration": 2.8573319911956787, "duration_str": "2.86s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.535766, "relative_start": 2.8573789596557617, "end": **********.732331, "relative_end": 3.0994415283203125e-06, "duration": 0.1965651512145996, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421104, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00743, "accumulated_duration_str": "7.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.657991, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 68.237}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.688276, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 68.237, "width_percent": 16.016}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.696696, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 84.253, "width_percent": 15.747}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1414531150 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1414531150\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-325559737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-325559737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2037827134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2037827134\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-811611093 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5qM1VWWjlPdlhpZm5xVTNtMi9IN3c9PSIsInZhbHVlIjoiRDVyVnQvM0NSZFFkMERjeWFBSElaYnhxOWtxZ0psdElIcDhmYmVYRUtLaGhFUkJtZXRhWDRTSEg5djV3bjE5eTRYY1pLQzM4Tk4vekxEM3VRNW0rT1ZWRHptblpKcW5wbTNuczIzM0M4WEFmZVQwRG1NVExlVFFESC82UlA4UkNTMnAzK1gyNHo2ZlNqb1BNRHJYWGVrZmkwc1RDb0ZXVzVoWEczVGZZUnRqWlVISzRaaHBzODNtakMyVjhhSmRqNkdEcjdLZWd5TVFxcit5RXFmM083SVRwR0JEOGRER29mYkY3RjlLZkRsN1FSNWd4WlZCN0VtZFlhVTJrTWtaZ05XSVdsTXJCejN4MXd3dzhKYlM5VGJ5YVNpbEZZeXRtMkIyV3FCNG1aUHN1UllqUmFJNU9Gcnl2U0tOL0dPRHRWQTVPL0lmb3I5Myttc24xenRucGtWYXl2aEZYVUJoUEdZaE5HbHVZSW5UZEU0TzhJVkZUaCt4OU1LQWljbVpqZldsTjZMSlRZTWltUG96UmNFVGVjYjlmZU42SGxnTWRzNXg4Nm4xRUVMdWUyOXU0cW5YeHVRS1FIUERRcTFlTXpkcXRlQUQ3WmVnOW9LUVJRSmV6SzFjZjdVcXBzMWNZQ0o1R0hqMks2aUxFVTlPWVFoQU1EQUdtNy8yUkpEcTYiLCJtYWMiOiI5MDc1ZjNjZTE5YmUxYmUyMGNjOGMxODE2NDM1MDllNjliZDAyZTNhOTU4MGI5YThiM2RlMzVjNjBiZmU1ZDQwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkJ3YU9SUnFwQ2JGdmVtMmFmNzh5UUE9PSIsInZhbHVlIjoiT01xdzdtcFlEbXljbmhoVE12dHZzbHJNZ29LOGQ2a0xPa2JyWTMydmwwWEU0Y3R1Y2RpZ0lQejRXUTJSNVY0UVBKMThkUzVvKzJLQmdqb0JsTk8yYUtTZlNZQmdmVTB5R3hjZW1mYlpxcm42ZENaVkQ0UlVXbXJQVDZHZjRVdGx2dHBQK0lFWDNPVmdLNDEvOGdWUjcxaGFhNUVsaitGUUpPbXJMdHAzSXFUUkdxUWkzeWJOalg5NHdoK1p3Rk5qYXd3SE5BZ05xbXRJb3NsVHNWN0htb1JYK0pJM2NkYXR1L3pNS3l6anJ3WERucjlGWWVWR3puakt5cUhsZ0tuR215bjE5Q3JWSFA2eGY4aWV4aS9ZdVBhdjlXQUJZck01MlpRcTBrZkZqSmJiTkt6SEY0WjVaOUlZb1BHSzNpMHphVCtyZk5wcWlrWXF0allEbUFWM0RBSEhYZER3R214WlpJeVByNk1aV3M1cWwrbHN1REdPK1drUWVkdVFSWkVkcE9Ncy9OQTRFako4NW1YY0xFa1NpWFQ2WmgxQ3pWaE0vallIK08xY1NZUkRyUTRsL2grazVJTkJtM0ZBS1poWnZrK20xNitpOHB5ZHNLZ3J2SWFYZFlDNE9RaDRwSEx6ZlJRVGNDRVBHRDdBbzlOeng3Ris0S3dmemF3MXRkcmciLCJtYWMiOiJiZmVjOTdhZTQ2YWQ1MjI5ZWEzZjQ0YThhZGUzMGFkZWU0ZGIwY2NmNTFkMTM5NDI4MmI3NTZlOThiYzVhYjQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811611093\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-247203046 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247203046\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-859153157 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:56:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd0RDg4SXdaalJmVmREL25NdkpTdXc9PSIsInZhbHVlIjoiWnljZGNLWHd0NFd4cllYT0VZOXc4ZEZtU2hvRGFySHFXRVQyRzBySFNYKzhOeW1Ra0JaYTVlY1d1SGFIOW1iQjBxWjgwZDFBSi9ERHlQclNjQ1J6OG9TblEvK0V4T3dneW9ZUnNwZ3kyT1BKdDZyQnV0V2o2SEtYdmFQWTg2MnFjL2hFMU9oUERPZWIzVzNNYUdTREI3cC9RNzJvZ2RydGRmNmdudGs2a0ErQ3pya0dKNVRrZU5xVjAvcVFrelFWMjhJSEthL2dUWGZ5ejdYbnRjR2RzMy93NU1kWGJWaXpEYzRFWlRpWmlwZ3FEQzd4SzFVdW9VV0M3WUw0dlVDNzRRaXBaZWFkTTVibThNb0JCSlZmMXJMeUllUjRrTGllT21nQXBxQVorZzE1YWQwQ1IwaEt5VjZjeVlHbTdkLzQvQjNLdDNoWFNGbU9Ga25TZDA4cHpVYlFqMFltQ0psczNEOStlOFRUQnFibUkxNlBjQWxnblRVZnF1eXdJZmlmbUhmZ0R1RFplTjlIQ0IzemQ5T2VwYThGazlFRVBiMlM5ZUlhVHVNZ2ducW43amtFTVpyRGlUdmZTNnhkUlhEZjB1QmREaVYrVXAzcjgyUHR1MVBJaXdXK25BYXk0QVM5KzZ6MkxiTjlxMFB6MEd0cXdRZUt4Q1ZIWFpKSGRBZHQiLCJtYWMiOiJiMTU2MzUyMWFjMGE0YzBjNGY1MmZlM2UyMWU2M2MwMzUxMzgwMGNmZTk5MjA3NjExNTNlMTRmM2Q3ZDQyMWU3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:56:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImRTOGF6VEVpZmNudktsRHhmNUJaQ1E9PSIsInZhbHVlIjoiZ3AvODZzS3IwNmp1aHg0N3hBdSt1bCtWYk90dU1tOVJXM0hLaFBuOStxeGRQMlhzc3hBRXFpazdmQUdaTFhOV2NWcnFjbkE3Tm0zQXlCSkpTeVM3QjFzYklZSjBwVFhsSVEycGxQbFZSdGVPWHBuRVJnaTZpNjlFaG5ENllZTVY4d21PYU9wWWJxcVBhUmRFWExIblByQ281MTdHMmtqdTdHeUpvbk1FalRsbU81SUNWczV5cGFsbHJTeWhyWHM3a2FZZ2ZRcHBDNDVaMVN5MllPZ3RMeWdpVTdMbHQ3UzBYSVVweWNhMzhOZlUvYjZzZHBmMTFDRFVyTjYwVGxIS0hPbTBYaXVPb2RiWWRSZTdNV2lETGloeWlaVzFOQ2JHeVIvQ3NuOVl3WVZKZEpUQTQyMXlUVzc3Z1NBMkEyMGxGa2hlUlZiczVJb3pXWTVqNHlabjR4ZnA0VDlwUWRjTXFORmd2RG9MRGUwbHFhZ2sreGJzdFNaMEpUTnpxK3VLS0M2VTlNd1JNMFV5SnZBMHY5Ylc4OFlaOG9BSit1c1RsUmV1MjVORTg4YXJQcnNlbmJlb2Q2WnB2WVgvMjdLMlA2anpLYTNHUUthUmpZdFNNNDZjWW5qeUlMemxWVDlBM3ZwS2xCbEVmK2FTNDNFVVdRaHRLQ1NJeUZwd2ZnL1AiLCJtYWMiOiIxZGQ2YThmMzkzMzEzYzRjMTE2Mzk3ZjQ1NjBlMzRjMDA5ZTkyMGMyZDVkNzYzZGJkMGNiMzk4NjBiNjU4NjU4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:56:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd0RDg4SXdaalJmVmREL25NdkpTdXc9PSIsInZhbHVlIjoiWnljZGNLWHd0NFd4cllYT0VZOXc4ZEZtU2hvRGFySHFXRVQyRzBySFNYKzhOeW1Ra0JaYTVlY1d1SGFIOW1iQjBxWjgwZDFBSi9ERHlQclNjQ1J6OG9TblEvK0V4T3dneW9ZUnNwZ3kyT1BKdDZyQnV0V2o2SEtYdmFQWTg2MnFjL2hFMU9oUERPZWIzVzNNYUdTREI3cC9RNzJvZ2RydGRmNmdudGs2a0ErQ3pya0dKNVRrZU5xVjAvcVFrelFWMjhJSEthL2dUWGZ5ejdYbnRjR2RzMy93NU1kWGJWaXpEYzRFWlRpWmlwZ3FEQzd4SzFVdW9VV0M3WUw0dlVDNzRRaXBaZWFkTTVibThNb0JCSlZmMXJMeUllUjRrTGllT21nQXBxQVorZzE1YWQwQ1IwaEt5VjZjeVlHbTdkLzQvQjNLdDNoWFNGbU9Ga25TZDA4cHpVYlFqMFltQ0psczNEOStlOFRUQnFibUkxNlBjQWxnblRVZnF1eXdJZmlmbUhmZ0R1RFplTjlIQ0IzemQ5T2VwYThGazlFRVBiMlM5ZUlhVHVNZ2ducW43amtFTVpyRGlUdmZTNnhkUlhEZjB1QmREaVYrVXAzcjgyUHR1MVBJaXdXK25BYXk0QVM5KzZ6MkxiTjlxMFB6MEd0cXdRZUt4Q1ZIWFpKSGRBZHQiLCJtYWMiOiJiMTU2MzUyMWFjMGE0YzBjNGY1MmZlM2UyMWU2M2MwMzUxMzgwMGNmZTk5MjA3NjExNTNlMTRmM2Q3ZDQyMWU3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:56:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImRTOGF6VEVpZmNudktsRHhmNUJaQ1E9PSIsInZhbHVlIjoiZ3AvODZzS3IwNmp1aHg0N3hBdSt1bCtWYk90dU1tOVJXM0hLaFBuOStxeGRQMlhzc3hBRXFpazdmQUdaTFhOV2NWcnFjbkE3Tm0zQXlCSkpTeVM3QjFzYklZSjBwVFhsSVEycGxQbFZSdGVPWHBuRVJnaTZpNjlFaG5ENllZTVY4d21PYU9wWWJxcVBhUmRFWExIblByQ281MTdHMmtqdTdHeUpvbk1FalRsbU81SUNWczV5cGFsbHJTeWhyWHM3a2FZZ2ZRcHBDNDVaMVN5MllPZ3RMeWdpVTdMbHQ3UzBYSVVweWNhMzhOZlUvYjZzZHBmMTFDRFVyTjYwVGxIS0hPbTBYaXVPb2RiWWRSZTdNV2lETGloeWlaVzFOQ2JHeVIvQ3NuOVl3WVZKZEpUQTQyMXlUVzc3Z1NBMkEyMGxGa2hlUlZiczVJb3pXWTVqNHlabjR4ZnA0VDlwUWRjTXFORmd2RG9MRGUwbHFhZ2sreGJzdFNaMEpUTnpxK3VLS0M2VTlNd1JNMFV5SnZBMHY5Ylc4OFlaOG9BSit1c1RsUmV1MjVORTg4YXJQcnNlbmJlb2Q2WnB2WVgvMjdLMlA2anpLYTNHUUthUmpZdFNNNDZjWW5qeUlMemxWVDlBM3ZwS2xCbEVmK2FTNDNFVVdRaHRLQ1NJeUZwd2ZnL1AiLCJtYWMiOiIxZGQ2YThmMzkzMzEzYzRjMTE2Mzk3ZjQ1NjBlMzRjMDA5ZTkyMGMyZDVkNzYzZGJkMGNiMzk4NjBiNjU4NjU4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:56:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859153157\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-170089600 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170089600\", {\"maxDepth\":0})</script>\n"}}