{"__meta": {"id": "X18255bb045e33a860e2f8df1de3c97f2", "datetime": "2025-07-31 15:47:36", "utime": 1753976856.597071, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753976835.617914, "end": 1753976856.597155, "duration": 20.979241132736206, "duration_str": "20.98s", "measures": [{"label": "Booting", "start": 1753976835.617914, "relative_start": 0, "end": 1753976843.203849, "relative_end": 1753976843.203849, "duration": 7.585935115814209, "duration_str": "7.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753976843.207788, "relative_start": 7.589874029159546, "end": 1753976856.597164, "relative_end": 8.821487426757812e-06, "duration": 13.389375925064087, "duration_str": "13.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44313616, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=58\" onclick=\"\">app/Http/Controllers/DashboardController.php:58-75</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 1.46027, "accumulated_duration_str": "1.46s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6830862, "duration": 1.46027, "duration_str": "1.46s", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TRf3GNBBfDeKjrk0u7TxYnwpnARSS51QzBhar5Bt", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-501309606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501309606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1900753341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1900753341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-701883636 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"644 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701883636\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-190577193 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190577193\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-827845104 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:47:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">https://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktpMjNhUmJUUXJORk5NeUZoQzdtVEE9PSIsInZhbHVlIjoiUmtQUmhmMFR5eTY2eWtaa2xLZ1VIRDJoMzI3YkJrc0IwWkc5WkFNOTEzVHI4NVh5TDU0aFQ5QzduaVhkcU4vbjZGM0cxdU51bVM0SEI2bVFnNW4vQnZCMjVCamFmWHJOVElEV2NjYk9CSThjNGh1RExiWUQvSEFWcUhHbk1rRDdsSTFnUm5vU29lNCtFSFJaVnFpZTFJYkVjeElSbGloeDAvZDZxUFU0WmtpdkhxaTFydHFhRlFuTTlwMDdhQnNtWHdyMFlWdmQweC81dUYyNGxSYjIyY1FLSnhSS2ZxaExaZXJpSkVjZk51VUFFNFVUREJrTkFmZ3lqZDZqUk11cVdQQ3hsb1FVYWRsa2hvVjcyOGZYMzlaL0tsRmhHaTlnaEtNRXVOZGlUemZHZWNXdVpTdWo4dytlMWlFbFBLM2wyTnN4Y2J1cmFFYXhoWExVdzJqZ0xqcnEveUNuQXN5QSszbFA4M2pSNytJbGtZV3dvWkU4Y3pnUWhTQ1MwcGMwcTJ0TGhBWi9QcHUxMVVJd1BDckE0QjRhemc0Qjg5Y0FwQWpyK1JDZWF5am5JR2ZXaTB0ZmNPbkk0cHZXcDczWUJRVGR0SUZqenVyd0laRzlUNzRsRzVHWE03dWFvYUVvOERIT1RoNkMvQ0JmdWgrc1p3NDVod2xSUXpmSXgvVGUiLCJtYWMiOiJlZWNiOWVlZDdhYWI4ZDc3OTI5MjRlNmI4NzM5OTk2MTAzZTY2NjU5ZTZhNzVmZWU2YmNkNjI0Nzc5ZjY2NjdhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:47:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Im9aTThoRDVGbzB2WjQ5VmdSdEJmakE9PSIsInZhbHVlIjoiV1hodjVwNWpEa2hYbnpnUlRJZksyUTlkMlBxbHlEZmRBWjIrd2lMQmp3S3hzVDV2bE9mMDJUS0VmaXNyQUl4Vk1Xc0M0RjRjRitkaGhETElUSE8xVkFWYlNVMTl6QWFHT25QMWZsY1ZMVHhySnB0VnNSYm9EdUVxYTM5UjE0RHdnOGdVME83TTdOczN6T1dEOFlqK2R5R2lQTTFmaFg5QlM0QlVqbXhDMXEwc0pVMFQwbzNDUjBWMG0vWFNSdkV3UWNGUm1nM0dGaG03T1FNZnVZbSs5Y1ZNYm41Zk5nOUJVOUZKTWhxWTE0dFgzb29UMWVVYnA5UmtidlpPTkhUYjVMSmdOem9hL0N5dkhwMS8rZU1sVWtHQzRBUm82U0ZNV0M2bkhiZzEyZ2llekxSa0dyV0F5QVI2RnI1c0o0dkF0YnZ6dGhQNmFTMEJtNEU5dzdSVjErVlBUYTM4ZEFuWEtrZjM1WkY0Q3pSZXBWVHcrdUloUGtzZGlHS1U0bVYwMVNFRzJXck9qRmhKV3RUOGt0REsrTm4vdFF2cXBFZk1oQThQa2ZTSHZ0ck5iclh5bi9DWGpsaENLdzNjMlRDbGdOZVBkRGcxYUZObmpyMFVuQThxU0FSQURWdkJqRisydjZyeENyR1k1dmhxOGRYbVdLTFJpZzJwd1U2dkRTS0ciLCJtYWMiOiI1OTg3MzE2OWYyYmQ3OTg4NjBiOGExN2E3NmNmOWRlYzg5MTFmMDAzYTA4MmM0NDZlMzQ4NGU5ZGEwYTg4ZTg4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:47:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktpMjNhUmJUUXJORk5NeUZoQzdtVEE9PSIsInZhbHVlIjoiUmtQUmhmMFR5eTY2eWtaa2xLZ1VIRDJoMzI3YkJrc0IwWkc5WkFNOTEzVHI4NVh5TDU0aFQ5QzduaVhkcU4vbjZGM0cxdU51bVM0SEI2bVFnNW4vQnZCMjVCamFmWHJOVElEV2NjYk9CSThjNGh1RExiWUQvSEFWcUhHbk1rRDdsSTFnUm5vU29lNCtFSFJaVnFpZTFJYkVjeElSbGloeDAvZDZxUFU0WmtpdkhxaTFydHFhRlFuTTlwMDdhQnNtWHdyMFlWdmQweC81dUYyNGxSYjIyY1FLSnhSS2ZxaExaZXJpSkVjZk51VUFFNFVUREJrTkFmZ3lqZDZqUk11cVdQQ3hsb1FVYWRsa2hvVjcyOGZYMzlaL0tsRmhHaTlnaEtNRXVOZGlUemZHZWNXdVpTdWo4dytlMWlFbFBLM2wyTnN4Y2J1cmFFYXhoWExVdzJqZ0xqcnEveUNuQXN5QSszbFA4M2pSNytJbGtZV3dvWkU4Y3pnUWhTQ1MwcGMwcTJ0TGhBWi9QcHUxMVVJd1BDckE0QjRhemc0Qjg5Y0FwQWpyK1JDZWF5am5JR2ZXaTB0ZmNPbkk0cHZXcDczWUJRVGR0SUZqenVyd0laRzlUNzRsRzVHWE03dWFvYUVvOERIT1RoNkMvQ0JmdWgrc1p3NDVod2xSUXpmSXgvVGUiLCJtYWMiOiJlZWNiOWVlZDdhYWI4ZDc3OTI5MjRlNmI4NzM5OTk2MTAzZTY2NjU5ZTZhNzVmZWU2YmNkNjI0Nzc5ZjY2NjdhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:47:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Im9aTThoRDVGbzB2WjQ5VmdSdEJmakE9PSIsInZhbHVlIjoiV1hodjVwNWpEa2hYbnpnUlRJZksyUTlkMlBxbHlEZmRBWjIrd2lMQmp3S3hzVDV2bE9mMDJUS0VmaXNyQUl4Vk1Xc0M0RjRjRitkaGhETElUSE8xVkFWYlNVMTl6QWFHT25QMWZsY1ZMVHhySnB0VnNSYm9EdUVxYTM5UjE0RHdnOGdVME83TTdOczN6T1dEOFlqK2R5R2lQTTFmaFg5QlM0QlVqbXhDMXEwc0pVMFQwbzNDUjBWMG0vWFNSdkV3UWNGUm1nM0dGaG03T1FNZnVZbSs5Y1ZNYm41Zk5nOUJVOUZKTWhxWTE0dFgzb29UMWVVYnA5UmtidlpPTkhUYjVMSmdOem9hL0N5dkhwMS8rZU1sVWtHQzRBUm82U0ZNV0M2bkhiZzEyZ2llekxSa0dyV0F5QVI2RnI1c0o0dkF0YnZ6dGhQNmFTMEJtNEU5dzdSVjErVlBUYTM4ZEFuWEtrZjM1WkY0Q3pSZXBWVHcrdUloUGtzZGlHS1U0bVYwMVNFRzJXck9qRmhKV3RUOGt0REsrTm4vdFF2cXBFZk1oQThQa2ZTSHZ0ck5iclh5bi9DWGpsaENLdzNjMlRDbGdOZVBkRGcxYUZObmpyMFVuQThxU0FSQURWdkJqRisydjZyeENyR1k1dmhxOGRYbVdLTFJpZzJwd1U2dkRTS0ciLCJtYWMiOiI1OTg3MzE2OWYyYmQ3OTg4NjBiOGExN2E3NmNmOWRlYzg5MTFmMDAzYTA4MmM0NDZlMzQ4NGU5ZGEwYTg4ZTg4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:47:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827845104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-361703468 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TRf3GNBBfDeKjrk0u7TxYnwpnARSS51QzBhar5Bt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361703468\", {\"maxDepth\":0})</script>\n"}}