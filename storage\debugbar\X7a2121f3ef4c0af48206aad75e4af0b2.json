{"__meta": {"id": "X7a2121f3ef4c0af48206aad75e4af0b2", "datetime": "2025-07-31 17:13:17", "utime": **********.620745, "method": "GET", "uri": "/invoice/contact-details?contact_id=customer_4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981995.742208, "end": **********.620781, "duration": 1.878572940826416, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1753981995.742208, "relative_start": 0, "end": **********.115105, "relative_end": **********.115105, "duration": 1.3728969097137451, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.115191, "relative_start": 1.3729829788208008, "end": **********.620784, "relative_end": 3.0994415283203125e-06, "duration": 0.5055930614471436, "duration_str": "506ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46344416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1703\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1703-1761</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01158, "accumulated_duration_str": "11.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5292928, "duration": 0.00883, "duration_str": "8.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 76.252}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.582652, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 76.252, "width_percent": 13.472}, {"sql": "select `id`, `name`, `email`, `contact` from `customers` where `id` = '4' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1727}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5948122, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1727", "source": "app/Http/Controllers/InvoiceController.php:1727", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1727", "ajax": false, "filename": "InvoiceController.php", "line": "1727"}, "connection": "radhe_same", "start_percent": 89.724, "width_percent": 10.276}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-1899662297 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1899662297\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-68013264 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">customer_4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68013264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2081626953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2081626953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-859953317 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldKeWVFRnlJenc5Z09HRkNkRk1YR0E9PSIsInZhbHVlIjoiWnQvcnhxOFJWUHdrTjdzdGZyWjdOMUM3bk1kWWdtWE1ocTFsOUlidUNRdDY5ZnFQTlVoWTgwbGxaN1lsa245STBvVXdjWS9aSEk0T1RXQzVBMGVsKyt1RkZOY0xEeXUySFR0TTQrZit3WWhWY3FjdWE0WnpGRTk3Wks4ZnlXN0ZTMWZ2YVUrUWhPUXlqc0xvS3NwZkhVLzExcWtyYWJPYVdZMi9QT25PSVJvcGFvN0JDSVFCQzNLYlU0dmVwV1VJSjRKNjRIWmkycXM3ZXI5QXI0cUxmVnNuUFJYTVlsT3gxYTd3ek13MEF2Qlg4VEhYUXBUMzZWak5hYWtocUJ5dURqYWZNVmtJSlJEcmtPdkNtbm13c1hGa0tTdi9KYnhaUzRlRjJyVy9yK1FPNDJ3Y0lvMjEyRXNnSGFDR3U1ZmZOU2p2RGlaaVNHbXdkbkVFdGxiRGtmUmlFTEJ6VFdiNDIvYjRJbHFyS2NzMUtIKzN5YmF5Ry9nZFQycDkwR1Jpdm15dUltNURSMmxkY21IUkFlNnZ3VmpvSDZEZ3ZDSlJ6VkZneHNVWnRKWHFkM2xEbUdPVVYwVkFOOFNRNjZxenRmRk1GcGxsN0VSZUk3dXpjMFQvUHlsVTFyM1pLRTJpTUY1clBHcmd2N1MyVEE5M04wVXJDMnlzUXR5Wk9xMzYiLCJtYWMiOiJmNDYzYTE4Y2NhNTc1MTdhODVlZWJiYTFlNDNkOGUwODNlZjJkMDhkOWIwMzdjZjg3ZmQ5NjljYmUxOWI0M2U5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik5Mc1JiYUdSaW5reitqeUl1OHJwTnc9PSIsInZhbHVlIjoiQ1pCWXVGOFNMSll2SCtjdzVMMWh3ZkRiNjRlVXEzNC9VT1pzdSs1Z1l3WnFaaHdWQnlkOE42VGQydDVGUWkrOWQwczV1TGhMUmhWQmFLbHJvRHVCK1RpQ0xwRWdGWnY1QXVxQTgwbXJQTWxGTGdzM201ci9FZTlGaktLcDNRS1dTQW1xWkoxQmFHd2VQVWFtaG12VXNORkxHQWI4SWhZNUZNTS9yWHNmR2xIQzVBVlgra0hyMlJrbnJEUnRtRmM5c1hTU0U4T0U5R3I5a1N5SEZWdTVtMXAxRkJkYUQyYmNXTkxkSVhGaVNTZTNxKzlNN1picnFXbjk0dTBTc1gvR0hncGZ1b1U4M3Eya0V6enZ2Wm5UVUFIRkQ3SWREK0RCNHVpUkx4R2R0bjJscW1sdGY1Y0NWd3RnSGhLTmJaUFczRTIzelZNbXdHTWRwa1BtbDBpYjNRSGpVK2ZaVjNBcThJTDI0eHlaM1B1ZzkvcFZjcm90WWpLTEJuMHVsMG9xNGNIcUVLbnR6R3M3NTVTZTgyMXVQSXhWTFhaY0JmUjZkUWpvR1hMSTVrbkZnL1N4d2d1S2t6STdLRXdnUk1DdjNCbU1rNFAzeGYrbTJVRk9MZ2daUTh0OHA5SXFnRUFKdThDUXFOQ2JlMjZSUHhUdU5Sa29NNFljZmtNeWN6aEsiLCJtYWMiOiI2MDEwMzIzNDZjZWNmZjNhYzFlMTkxNjM3ZDYxOTM4Yzc4YzVjNDQxNmJkNGRkYjViM2RmZjY4NTZkMzkzMTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859953317\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-341628819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341628819\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1237974753 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:13:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ2NTY3bXNYZ3hJYnNYZFRybkxmbEE9PSIsInZhbHVlIjoiS3ZEYUV0WXNyMUcrKzR2TzZzK01WQnIrNm1jUVcwZklQdk5XcFpkVTdVeFk3T0t1U0hJQ240TE9CZGRtcjBadlFvSmhCemxIcVZ3L0ZSd0RZVFlmVzVKdXJueFNkSlNiSTg2UDU1NWV3ZnJ2VURKeXlNRThaVjJKSjFoZm11bzRNZUdBMzFBaWo1TmRDWHZDYXpnTFIzSXJKZmNnVXVhaytOeWk3ZjhZK2FPcjR0QktBZ1haWnc2QzlHb1AxNEtNcEFaRjI4cmMrKzUwMmFtdk51Wmo4SjFWQjI5QUVJd2FxZTc0VXgwRm9WT3Q1K3cwZG9qSEZWMjIzM21teGUxTG9rSHlFTE1oSWhpamFUM3o1WGE5WXpvblAxeU1qWjlReTVaV2txS2VjS29ZdkwzR1lSS08xN1lXUlhyQnRqVGRHYVdvZTFXZjNPZXlkVVIrcjdYUlFRelNUaWNZOFcvRlFXN0VGTUdXOUtUTmtOVFdKdExxcTJjWHZ3OEZFUHFEUDZJajZwVE5HcVNZVGpJTkxzTGJDTjZGREx2WVZJR2l6ZlBJNjJpRkRmcUx6TThZUFBjOFoxSGRZaFByVFRiU2o2TDg3RG5sMmZBRGZicFBBeXVnZEFGaytrallwVWNCMXN3aitLRXEwVUJrUVBaK1FGclBxRU9pLzFuTnpXbGkiLCJtYWMiOiIyN2M1ZmU0MWY0YWE5NzM4OWRhZWNmMWM3ZDI0NWMwOTRlNWZjNjczMmNlZDkzNzI5ODA4NzlhMWEyZmU5NGRiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:13:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhxSmtEVU81cW84K29jelFiY0E1dFE9PSIsInZhbHVlIjoiVGtqRUtncCtuRE42UzduQnNyNkV2b1J1M2N2eWowM0t0VG1oQ3BtRG5DNWxzaTE5RytVeVFlbjIyTkcvYnIrYzlCOHVDNlJmUmJrUVhxY3hiWUFLZ0RRd29PMlJTODJSRUV0V1ZtSE5TVHZRMWVjZ1pObThkNXd3a3dLRnpSRFpFaHZJVi96cGloR0J6ZHhSTEt0amx1V3B6VmpJNTY5NG1OTG1DcWU3ckZrR0c4dG5nbDN0UXVRaERzU200NHF6ME9kQ2RpZGtlVXIrdzd1dXEyZjRITE4zYVRXOVFvVUFhamdZckdaRTVOVVhjcDlQR01YdU9sTW1YQXVpRXFua2lSRnFsWFVzOTZCUXhpYk9LdlM4MXNBOTFlMkRSTHFOY3RSZitqNGJFSi9hT0VUVGMvaG5jT1psQXZvSkM2NXlnRUxIcW5hZ09rQlhjYWFMWVhaWlhIVFpXc3Y5a1I1YzdlZ1VZdW1EdnpPd0hDNUFNMkRrR2pnTm91SWtlVmJnMkVWNDArL3phc3FVZVJ2MmJZVmZlSWRxWlI4elJqejcxb2dQbm1UMVhONGhHSlZHNWpJNjhQNHkraW1hUGtPc01ueUpUK1dGVzh1V1cwa01RYXg1czRraUk0OWVQbnQrS3Bpdkh2S2QrTFU2Z3BhSFRQTklwL2VpT2lpbHRXYSsiLCJtYWMiOiI1MzlkYjVkODdmNjE4ODI0YjkwZjAxZDlmMjYxNWM2OWQ0OTc5MzY2M2Q1MmM5NmMxZTEzODVhNjE5ZGM0Yzg2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:13:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ2NTY3bXNYZ3hJYnNYZFRybkxmbEE9PSIsInZhbHVlIjoiS3ZEYUV0WXNyMUcrKzR2TzZzK01WQnIrNm1jUVcwZklQdk5XcFpkVTdVeFk3T0t1U0hJQ240TE9CZGRtcjBadlFvSmhCemxIcVZ3L0ZSd0RZVFlmVzVKdXJueFNkSlNiSTg2UDU1NWV3ZnJ2VURKeXlNRThaVjJKSjFoZm11bzRNZUdBMzFBaWo1TmRDWHZDYXpnTFIzSXJKZmNnVXVhaytOeWk3ZjhZK2FPcjR0QktBZ1haWnc2QzlHb1AxNEtNcEFaRjI4cmMrKzUwMmFtdk51Wmo4SjFWQjI5QUVJd2FxZTc0VXgwRm9WT3Q1K3cwZG9qSEZWMjIzM21teGUxTG9rSHlFTE1oSWhpamFUM3o1WGE5WXpvblAxeU1qWjlReTVaV2txS2VjS29ZdkwzR1lSS08xN1lXUlhyQnRqVGRHYVdvZTFXZjNPZXlkVVIrcjdYUlFRelNUaWNZOFcvRlFXN0VGTUdXOUtUTmtOVFdKdExxcTJjWHZ3OEZFUHFEUDZJajZwVE5HcVNZVGpJTkxzTGJDTjZGREx2WVZJR2l6ZlBJNjJpRkRmcUx6TThZUFBjOFoxSGRZaFByVFRiU2o2TDg3RG5sMmZBRGZicFBBeXVnZEFGaytrallwVWNCMXN3aitLRXEwVUJrUVBaK1FGclBxRU9pLzFuTnpXbGkiLCJtYWMiOiIyN2M1ZmU0MWY0YWE5NzM4OWRhZWNmMWM3ZDI0NWMwOTRlNWZjNjczMmNlZDkzNzI5ODA4NzlhMWEyZmU5NGRiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:13:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhxSmtEVU81cW84K29jelFiY0E1dFE9PSIsInZhbHVlIjoiVGtqRUtncCtuRE42UzduQnNyNkV2b1J1M2N2eWowM0t0VG1oQ3BtRG5DNWxzaTE5RytVeVFlbjIyTkcvYnIrYzlCOHVDNlJmUmJrUVhxY3hiWUFLZ0RRd29PMlJTODJSRUV0V1ZtSE5TVHZRMWVjZ1pObThkNXd3a3dLRnpSRFpFaHZJVi96cGloR0J6ZHhSTEt0amx1V3B6VmpJNTY5NG1OTG1DcWU3ckZrR0c4dG5nbDN0UXVRaERzU200NHF6ME9kQ2RpZGtlVXIrdzd1dXEyZjRITE4zYVRXOVFvVUFhamdZckdaRTVOVVhjcDlQR01YdU9sTW1YQXVpRXFua2lSRnFsWFVzOTZCUXhpYk9LdlM4MXNBOTFlMkRSTHFOY3RSZitqNGJFSi9hT0VUVGMvaG5jT1psQXZvSkM2NXlnRUxIcW5hZ09rQlhjYWFMWVhaWlhIVFpXc3Y5a1I1YzdlZ1VZdW1EdnpPd0hDNUFNMkRrR2pnTm91SWtlVmJnMkVWNDArL3phc3FVZVJ2MmJZVmZlSWRxWlI4elJqejcxb2dQbm1UMVhONGhHSlZHNWpJNjhQNHkraW1hUGtPc01ueUpUK1dGVzh1V1cwa01RYXg1czRraUk0OWVQbnQrS3Bpdkh2S2QrTFU2Z3BhSFRQTklwL2VpT2lpbHRXYSsiLCJtYWMiOiI1MzlkYjVkODdmNjE4ODI0YjkwZjAxZDlmMjYxNWM2OWQ0OTc5MzY2M2Q1MmM5NmMxZTEzODVhNjE5ZGM0Yzg2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:13:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237974753\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-24929196 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24929196\", {\"maxDepth\":0})</script>\n"}}