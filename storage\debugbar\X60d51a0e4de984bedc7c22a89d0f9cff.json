{"__meta": {"id": "X60d51a0e4de984bedc7c22a89d0f9cff", "datetime": "2025-07-31 16:53:47", "utime": 1753980827.007774, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980823.780954, "end": 1753980827.007843, "duration": 3.226889133453369, "duration_str": "3.23s", "measures": [{"label": "Booting", "start": 1753980823.780954, "relative_start": 0, "end": **********.50615, "relative_end": **********.50615, "duration": 2.725196123123169, "duration_str": "2.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506183, "relative_start": 2.725229024887085, "end": 1753980827.00785, "relative_end": 6.9141387939453125e-06, "duration": 0.5016670227050781, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025910000000000002, "accumulated_duration_str": "25.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.684079, "duration": 0.00866, "duration_str": "8.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 33.423}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.780198, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 33.423, "width_percent": 16.21}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8419209, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 49.633, "width_percent": 13.817}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.884753, "duration": 0.009470000000000001, "duration_str": "9.47ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 63.45, "width_percent": 36.55}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1201356027 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1201356027\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1400360678 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400360678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-593224207 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-593224207\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2050187874 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijk5QzFTREdFcVZwZU15VGVHSHZNZ3c9PSIsInZhbHVlIjoiWkFCbEhVdjlYc01BS0p5dDdaeFZyaUdsK2JodnlxVzV3RDFVeVhIWGQyZUZFMG5iVDdvM0IvYkRRMzFva3ZFTFpzM3k2V09BYW93dlFleGFPRXRXcWptQmlQWGpPMGozWVNvUUJzUkpMR1lrUUhyK3pISFE3RGJZcWtaT2huUzQ2YTY0aGhsQTk2VnNGeGduQjhoQkdJQ1JscnJ2eVM2dUxnS0s1NHRJZFVWRENqTE1UQXJSZmNUYlR2TVdsUHdRUHJQZ0FRMlJNQmZRb08zakFjWW5ENURDaVhIMkZOenZlclZLUlkrTHlvbm9KSC9NVVFOdVhDQnhlSmxDUHJJdURlcHFBSlNUdWY1b3ZDN0dqRlRHazFDWGlTMHB5WEpOczIxSEM2QzRnU0J3bGt2RUdXYk5NZHhMTXFSeGZmS2VvN2lBYzNtSnNBTzZpZlo2dGtQaEcrMHZORmRXeWIzZmdERVRWMFUrVGxnR1R1Q3VhdS80NUc1eVdmWGlwTVUrM0hWUkhRYzRkRU5OeFAwKzJVMkYyYmlHdFVBYkFsMnFFai9UWHZncWh3NmRjVXN4SlI5VVJBeDZiSGNuTGEySkJBRWRnNGtrYUVJQ3pnL0J3dWZ4eFNmYVZTT01UVkpjUW5wVHJmU01JR0RrRXoyZEhuL0Z5S0hiK1MzSUxtR0UiLCJtYWMiOiI2OWI4MmE1YmY3ZjNjNDYxZjlkYWQwM2VlOGVkMGM5YTg5YTk5MTZjMmMyNDVjNWYxNmQwMjI1MDc3ODU1YmQ3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlFac05VeEhqdHpVU05JbGhVNWpjV2c9PSIsInZhbHVlIjoiL1FSMW9WTW5ZSVQ1SFVrMTJwQjNIcktTK0JQNjkvRmdMdGVaWmFYK1FYKzE2UUhqODRaMlg5Si9xbHp4S1FvNkhYdXNPQ1ZEaVVjVTBMZ2FlR09leWozUHF0SnNFNEZBQU4rempBZ0R5ejc2akV5RnVOYlVpQTZWNTV2TUZMdWlIeFFqSWZNTHMzWUhEcEViMUFGVCtUMUl3RjlsVS9RQVhZSGZyUEsyeW1JQnRiT1JjMjJSS0lzdVlxcGs0Z0ZGdEU0S2lMdWt2cWw5RitIS2NaTFZEZDB5SkJ4VnhhWFR6VHljYURuRUFyZUlnUk82Rlloem9FaFFGbWEvMmNIVS9lZmd0UkNENVNlc0d2akdBOTR1dFRnZVEyRVI5ckRlTk9CYzdiVllCNGpSc05kdFluaDBwNVRWV3I0M1lEL05NWXZwc2FzU2pOblo2eVdoSmdQWkFTTENGRlpZeTBrR1M1ei9NN1ZsVm5IZ3hEUnJvMjVuUmZxNFpHU0ppcmg2M3U1eHhNVURVTUk5MmNNc083c1J5MVIzMHVNTFg1aWRBcUdDMVA0ZEVoS2dFajgvWEVveFh6TDRPdytlcWJyTVBweWRBTDZSSE1yRnAxQk1JVG1ZZXFiQnpvcU56eVVWQnBQeFhQRFBKdllSRG9Mc1J2b3RwTGo2VGJMRXNtM0giLCJtYWMiOiIwNmUwZThhNDU5MDI1OWI5Y2M0MjI2ZmE3YTkwNTUwNTdhYTA4MGRmYzg3N2RlZjY3NWY1NTJmNGRhYTE0ZjYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050187874\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-817564087 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817564087\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1117024424 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:53:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR5VjduQ1RuNXRHR3hrUE04R1dqSWc9PSIsInZhbHVlIjoiOEtDZ1J0TjNGQVErYS9FTEpzYjNwVTVRRVhYQlc5b2crSGRVNWgveFJiRTBkWFgxem1oRnR2SlZicXdhT2xVNzQ2WmxUMGt4YmloZVFEOVdUNFBRYitXdTM1cC9FSEpEcXRxSnJHYU1uekRMempvcnJ1Q2dxTGxIRHZZMUFuNTN0cWphM3p2UGJxcnpkQ2xPZHlrZ3NMZ2hxWSsydTU5V1B2Q0NNa2h4TENzUW5Ub0FlZ08wbExUOEhpQnJhanh0RVh6em0zQXdlcFNNMjBMbnloV3I0Tmp3dmFjL2ZxRUdHZjRHQjk5Uk5rbU9Zd3F1c3hHc3FmbjVpWGRDcWxlc29YRHd2NUt0UHZiSHh0VVVVSUM3MFR1OFRKaFFEK3NYV2FIN2cyZkNzVC9RU0p0VEwxbE1JT0RGalRHWHZiV0ViU1d2OVExQUJ1VWo5NEpQTVJlZjJsWjlQQzdpYXFXdkgxczVrTXZkUCtqdnRPVmRRVEpQZlJtQTE5RUNxU2YvUjNHUFF3bkozOFllamE0OVNIME9LRHNlcG5Pbi9hMUJxOFQ0SThoMXprbkt4V1JNT1RTUm5OTmxBdW9YRUVFTkoxUm0wVTNlbUdXMHppUStDd3VlRWh2WUkxcDljZU85Q1FkTkx2T2RtSzNhRTY3dGZ6Zmc3Tk1icHZReWlVWnkiLCJtYWMiOiI2MzFiNWNkM2E5MzEwZmQzYzdmY2Y2N2VjODYwOWFkMzc3YmJiMWZlNzJhZTU3NTFlZGExODczZjNiZTgxMjc3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:53:46 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImxzZXFvTGNxWktwVVM1SHQwcVRaZ3c9PSIsInZhbHVlIjoiZ0ErVzQvamNVb0tDTHJPRlVQQVZ0MnJZYUZHQWs0bWxFYjlpNXFISkFnbEZ6cnFDb2xEaUx5RTE5bTNwUXpabVVJeWlmQUZxVytpamIvRFE5bzRVOWQwN2ZDWk51ejR4dXdGNTh6V25keE1hZktLOGtIU1ozMG5tQkJ6MThqM2N1YWpkNDF3RjdleEJFSXRkQlVZeU5Xa1pnZkk4WXAzM0pMVno5ZytQcHFLSlA3aXR0S0pDVGZiVDR4eXJKUjdDVVdWazFFNHExMXdQSGdsTDdSWXE1UWpoNUp3UE9qbWpSR3VSakpJbWQzNW9nSW5Uakp3YjlieGdIM1RnckwyRnVrK2NnamhrWXhDNVgrUlNoMXpmUTREKzJSMGtId1VCWlIzbFJqUzQ1OHN2YTNyYmFIcVVMaG1rZ2R2MzNwYkduUXdxSjdyODExK2ZhRUhXZ2MwQkpCQkpRVDRsQ1MrSjV4UEo0N2xBMXpJb01nblN3SmE4ZDJFa2I3T0RUTktmNGVEYjNTSmpUZEVSdHFrTE9obEVMcElVajNZVmhrNlJsUXFyL0NOMHZsbWhJVEZDRHo4YytpRGNNUE9jaGxsWnd6elVDS01XTXRaaFhOa2tNRDJWM0huR2l6MGlzOElnMFZkdk9pOFM5UWFOcXdjNWk4ZndFbVRVYlk0WlNacUsiLCJtYWMiOiJmNzQ3ODljY2VjNzI1M2UyNDUxN2ZiYTBkMmQ0MGZhNWRjZDI3MDJjNTcwYWM3MDQ2Y2Y1MDlkZWEwYWM3ZjEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:53:46 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR5VjduQ1RuNXRHR3hrUE04R1dqSWc9PSIsInZhbHVlIjoiOEtDZ1J0TjNGQVErYS9FTEpzYjNwVTVRRVhYQlc5b2crSGRVNWgveFJiRTBkWFgxem1oRnR2SlZicXdhT2xVNzQ2WmxUMGt4YmloZVFEOVdUNFBRYitXdTM1cC9FSEpEcXRxSnJHYU1uekRMempvcnJ1Q2dxTGxIRHZZMUFuNTN0cWphM3p2UGJxcnpkQ2xPZHlrZ3NMZ2hxWSsydTU5V1B2Q0NNa2h4TENzUW5Ub0FlZ08wbExUOEhpQnJhanh0RVh6em0zQXdlcFNNMjBMbnloV3I0Tmp3dmFjL2ZxRUdHZjRHQjk5Uk5rbU9Zd3F1c3hHc3FmbjVpWGRDcWxlc29YRHd2NUt0UHZiSHh0VVVVSUM3MFR1OFRKaFFEK3NYV2FIN2cyZkNzVC9RU0p0VEwxbE1JT0RGalRHWHZiV0ViU1d2OVExQUJ1VWo5NEpQTVJlZjJsWjlQQzdpYXFXdkgxczVrTXZkUCtqdnRPVmRRVEpQZlJtQTE5RUNxU2YvUjNHUFF3bkozOFllamE0OVNIME9LRHNlcG5Pbi9hMUJxOFQ0SThoMXprbkt4V1JNT1RTUm5OTmxBdW9YRUVFTkoxUm0wVTNlbUdXMHppUStDd3VlRWh2WUkxcDljZU85Q1FkTkx2T2RtSzNhRTY3dGZ6Zmc3Tk1icHZReWlVWnkiLCJtYWMiOiI2MzFiNWNkM2E5MzEwZmQzYzdmY2Y2N2VjODYwOWFkMzc3YmJiMWZlNzJhZTU3NTFlZGExODczZjNiZTgxMjc3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:53:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImxzZXFvTGNxWktwVVM1SHQwcVRaZ3c9PSIsInZhbHVlIjoiZ0ErVzQvamNVb0tDTHJPRlVQQVZ0MnJZYUZHQWs0bWxFYjlpNXFISkFnbEZ6cnFDb2xEaUx5RTE5bTNwUXpabVVJeWlmQUZxVytpamIvRFE5bzRVOWQwN2ZDWk51ejR4dXdGNTh6V25keE1hZktLOGtIU1ozMG5tQkJ6MThqM2N1YWpkNDF3RjdleEJFSXRkQlVZeU5Xa1pnZkk4WXAzM0pMVno5ZytQcHFLSlA3aXR0S0pDVGZiVDR4eXJKUjdDVVdWazFFNHExMXdQSGdsTDdSWXE1UWpoNUp3UE9qbWpSR3VSakpJbWQzNW9nSW5Uakp3YjlieGdIM1RnckwyRnVrK2NnamhrWXhDNVgrUlNoMXpmUTREKzJSMGtId1VCWlIzbFJqUzQ1OHN2YTNyYmFIcVVMaG1rZ2R2MzNwYkduUXdxSjdyODExK2ZhRUhXZ2MwQkpCQkpRVDRsQ1MrSjV4UEo0N2xBMXpJb01nblN3SmE4ZDJFa2I3T0RUTktmNGVEYjNTSmpUZEVSdHFrTE9obEVMcElVajNZVmhrNlJsUXFyL0NOMHZsbWhJVEZDRHo4YytpRGNNUE9jaGxsWnd6elVDS01XTXRaaFhOa2tNRDJWM0huR2l6MGlzOElnMFZkdk9pOFM5UWFOcXdjNWk4ZndFbVRVYlk0WlNacUsiLCJtYWMiOiJmNzQ3ODljY2VjNzI1M2UyNDUxN2ZiYTBkMmQ0MGZhNWRjZDI3MDJjNTcwYWM3MDQ2Y2Y1MDlkZWEwYWM3ZjEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:53:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117024424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1926036297 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926036297\", {\"maxDepth\":0})</script>\n"}}