{"__meta": {"id": "X26f1c9243866c7d30000d7aafcb97f26", "datetime": "2025-07-31 16:19:54", "utime": **********.278095, "method": "POST", "uri": "/finance/sales/store-installment-plan", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:19:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/finance\\/sales\\/store-installment-plan\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.272546, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753978792.369966, "end": **********.278134, "duration": 1.908168077468872, "duration_str": "1.91s", "measures": [{"label": "Booting", "start": 1753978792.369966, "relative_start": 0, "end": **********.023758, "relative_end": **********.023758, "duration": 1.653791904449463, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.023772, "relative_start": 1.6538059711456299, "end": **********.278138, "relative_end": 3.814697265625e-06, "duration": 0.2543659210205078, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53593696, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST finance/sales/store-installment-plan", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@storeInstallmentPlan", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.store-installment-plan", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1623\" onclick=\"\">app/Http/Controllers/FinanceController.php:1623-1740</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01459, "accumulated_duration_str": "14.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.113493, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 37.217}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.142432, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 37.217, "width_percent": 9.184}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.174022, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 46.402, "width_percent": 5.826}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.1980731, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 52.228, "width_percent": 6.923}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2078469, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.15, "width_percent": 6.443}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2149591, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 65.593, "width_percent": 6.648}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.22171, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 72.241, "width_percent": 27.759}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/store-installment-plan", "status_code": "<pre class=sf-dump id=sf-dump-675314622 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-675314622\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-531901751 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-531901751\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1754509817 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>customer_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">lead</span>\"\n  \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>customer_phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9932313212</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-31</span>\"\n  \"<span class=sf-dump-key>product_price</span>\" => \"<span class=sf-dump-str title=\"8 characters\">89000.00</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">offline</span>\"\n  \"<span class=sf-dump-key>down_payment</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5000</span>\"\n  \"<span class=sf-dump-key>discount_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>payment_frequency</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n  \"<span class=sf-dump-key>installments</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>payment_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-01</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">83000</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ok</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754509817\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-389803353 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1785</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryIL2KY0VLObwBR1Cp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikt1OGI4NWh0TXFNUWMraUlrYXlFRkE9PSIsInZhbHVlIjoibkk5ak9kajF3blhuNUdXL1g0ckxsRWNOUHlWbzk3eGpET1dTQnp0VHJmdUxBZHhvdkMveGlzMG50MTFZNDFDWVBWRGJaMUFla2hsSmd0ZG1Sb01RL3ZwOVlVSGtOdTdXYXdYQjFYcVRCN2Q5YzhQT1k2SlNha3grQktpcVBvekk0ZTJzcjNhcVVqZlE2VWxmM2RlSkRUdGMyL2hFK3RLUW9HWFpTT3pzcHVYNDBCdlE0SFBwRmltM3l2OE1FNEw5eTdodUtKckF5TENzYUhYVUIxbnMwKzJFM2ZhTTdhZkNJOE1NTGswNitvOUUvRVFwV0toWWdtK1NzMWh2UWY0Q1UvWVlkNjNGbVFIa0FxenA5bE4vY2JHY252YjJaeTY2ODVBY2t3T2dUdnZYbWdVakd1SWJyaUZMbUdvcnduMGh3WVpqdkRnNGpXaURERmNMZmFPZG5IK0d6YjZwRnVEdGlIUEwvMVJsU3ZIYVYvTGJlZWdEUzhiVjZIUHYzMjc3TWpPc3FBNlV0eVE3M1dQUnFDUHZ0V1MyQS9zbjcvTTMvSXVyV01LYmRrUjlUZ3pLbUIxMEl4bjl2VjdINCtIcDQyVll0Ym5RVFlHall2Y01TTkRuSlZnNXN6Q1BvNWRFeUlrcGEzcTBlOWwvNnVrUEptVXUzMGU0bjRWaFM5SDYiLCJtYWMiOiI5OWJjN2FiNTc4NzNmNDY2MzZjYzg0Mzg4YWQ4MDdlY2ExNjlhMWI5NGIyNjQwYWVkMWJmY2Q5NDQ0YzkxNWU3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjZHelFVNXpzOGV5WVgrUG9wL2JkeUE9PSIsInZhbHVlIjoiT2FxVzR0NnRoM2RYUFE2UU9IRTRQOHVSQUtZYVFxb0svVis0QlM5dGVza2ovay9FRzU2bXhFbkZDTnpwRzNLbm43TURYaThEVHMzQW1vR1pnd1JLYXhzeElNRk5zeFpVc0tlaC8xZ0I3TkZhWFlBUVJNVlJ2YmtBYnVCK2pZeVJGSG9PMnVzU3JzSFJqV1RYZHRGY2dOTWZGUmQ3encyeUJFenZLTWNUMnltcEdYY2dOL3JON2hnR0JMTGl0Wjhkdmd4a1AvUWFIdlh6NU1OV1o2a3B2NHVIRlM0eUhrQTBCbjg1YXV6M3Awb2JSMnJEN3B3MXVBU0VjNmtRWm05U0NKQ3N1b21FU21zdEN0MUIzZEZodUtEdzNNcGlGWmlMbmxCcjdwMEJuMmZWRjNHZVNmUE5Ed0syenM1YjRMWktPbkJPN08ybXBBTzNhMlVIRGFBaHJFWjBEVy81d0tkVDZtUEZUVnJydmNacU9oWlpqbEwvY3RvUk1oc1NRQyttYjM3U2o2NlE1OHpRWHZ1N1NIOGFJYXFRQzB2Q1B0MVZYaFErQUIyYXZ3UGk5bDBHeitRS2JHeGtEQlpaUUJtL2dhRjJVb1BHbE83WUNUMllNazZYMmNPZnVKY1NQSDl4U0ZXamFEd1JhZHZ6cjJaY3FVZzZvQzBXdUVUUVhRQWkiLCJtYWMiOiIyZjljZmY0NDg5MTkxYzA0Y2Y2MWRhM2E0OTI1NjNiYzBmMDQ0MjI2MjAwMzkzMWM2YTViY2YyZTNjMmViNWE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389803353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-234319416 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234319416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-528780201 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:19:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktmcHRKUnJYNEdUNVp0YWtUVDk2OVE9PSIsInZhbHVlIjoialMxZTlnMGpJOHZCalAyUmZBMUhTbHlndWRtYzlKRnkvcGJ4VnJ6NW5KcHJEdzZObzhCS0JXQnRtR21MbklDSVlSNFVVK3FLN3lJMUpUQ1VFd2h3b0RLTzNHSkpVcjUwS2t0aGtxdGFoWE51eVhsTCtBMVFMOVE1TWUvR0hBNFVnNmIxS3NvWjlsa0RGSTZKTFBSNldHc3pHc1l2MUhNT3Nvcks2cGUrcS9QTEhJZXplc0pkZGJQcmkyUDVPblNPQ2hQa0ZMV3R1UE01K0ZQSFFrL1lqOEk3QXJSUnR5SWV2U2ZDSDk4TVVrdHFtTDRPZlBVZmtUMGNwTlQzWTJlcFF4T3lMQTBYckRKK1R0dERvY3NzSE9BZ21ST2ZUNFZ0WHJvVEpJMkpUQ2FEQVhXNU4vMklMYmdhMldrUk9GQ2M5UGJkNVBPYnZuTE9ZOW1VZUplcDBZM3F1M1V6VitVMEhyR3RNcGJLWURHZ2tERGhqZDVQTDduU3pJZmdnbGxSU1BBSEJxZkRXdG5KcnprZUJrd0ZacGpqOGFvenVJMGoxWm5NR29Ld2M4clBJK3dBTlZYRlpFenNEb2RxbUxGRmQ1dUJ5a241OE5QR0J3VUxFTWJ3dzdUUlMvb25QRUhISnF5K1VhNm41SW94L0lkL1psVDBTY1ZrM014cnlnSDQiLCJtYWMiOiI1ZWQzNmMzYmE0ZjFjOTU0MGIxZjUxZjRmMWRmNDFkYjVmZTdjYTFmNDgwODk0MmE4NGJjZTRiOTMwZDc5NDdmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:19:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlErb2ZpK1UwdytwaDJXcUhKR2QvSVE9PSIsInZhbHVlIjoiNHZMbkV4bkkzMlkwNlBVWmZxb0hOakFnQ0xWZ3NPaXFqRmtmWUZmeVN4bm04Q0VVZ2RkeGJEQW92YmRvUDlRRXRrdmJURUpLS2h1bGN0NENRd0hkaGoxSTFSRkFPN3JVZG1vTVNlb0NwcjFVWkJySzA3MWsrN2VCcENDM3UrWHY4UnhRYnNQRnFQS2xXZHBrUGNBbTJvMUxWRjF6NmUrNE95V0JITDd6Z2xoMjcxQnlvSFVyek4ydll4SUEyZHVWcVRMRmdyeW1iaS9oQlJvaWpsNzh1NW5xQ2dWNGltT0JOQVN5QUhWOXd6RkxIeWcvMis4c1JVMTcrMDVEYTF0cmYzQlB3YXMyMGhYVFVRV2I3WngyRGRDTjB1d2MrSXIxeG1YS1BweWNwM0JUMG5KM2YvcEw0T1VrUE5TN2RYL2hRazE5Y0U2NVdzSUQzU0VFNkpXaS9TdlVTM081UUxYaENrV01SMEZjbW4yUlJsa2t2ZTI1NUFaNFdrS2NLa3VMN3NLSDU3UTM3OUd5ZkVpSzNObzNJSVNUS1paMVlGSnN0Y3oza1YvanExUkN5U2ZaZWpGekFUNVVWNFBnN21VMklpTEVWdU9xcVBpckszeTlrY1g5eFJVUVNZTTZPUmEwTzJLTndEUVJqbXRGaVE2UUJZblF3eUtBL29mMzJlM1AiLCJtYWMiOiIwMGU5ZDNjYWQzZjkxNzhiNTdlZGY5MDllN2ZjMjhhZDZjOWI5ZjE4OGVmYTA1NWFmNWIwMDIwZTFhNjM3ZjBiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:19:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktmcHRKUnJYNEdUNVp0YWtUVDk2OVE9PSIsInZhbHVlIjoialMxZTlnMGpJOHZCalAyUmZBMUhTbHlndWRtYzlKRnkvcGJ4VnJ6NW5KcHJEdzZObzhCS0JXQnRtR21MbklDSVlSNFVVK3FLN3lJMUpUQ1VFd2h3b0RLTzNHSkpVcjUwS2t0aGtxdGFoWE51eVhsTCtBMVFMOVE1TWUvR0hBNFVnNmIxS3NvWjlsa0RGSTZKTFBSNldHc3pHc1l2MUhNT3Nvcks2cGUrcS9QTEhJZXplc0pkZGJQcmkyUDVPblNPQ2hQa0ZMV3R1UE01K0ZQSFFrL1lqOEk3QXJSUnR5SWV2U2ZDSDk4TVVrdHFtTDRPZlBVZmtUMGNwTlQzWTJlcFF4T3lMQTBYckRKK1R0dERvY3NzSE9BZ21ST2ZUNFZ0WHJvVEpJMkpUQ2FEQVhXNU4vMklMYmdhMldrUk9GQ2M5UGJkNVBPYnZuTE9ZOW1VZUplcDBZM3F1M1V6VitVMEhyR3RNcGJLWURHZ2tERGhqZDVQTDduU3pJZmdnbGxSU1BBSEJxZkRXdG5KcnprZUJrd0ZacGpqOGFvenVJMGoxWm5NR29Ld2M4clBJK3dBTlZYRlpFenNEb2RxbUxGRmQ1dUJ5a241OE5QR0J3VUxFTWJ3dzdUUlMvb25QRUhISnF5K1VhNm41SW94L0lkL1psVDBTY1ZrM014cnlnSDQiLCJtYWMiOiI1ZWQzNmMzYmE0ZjFjOTU0MGIxZjUxZjRmMWRmNDFkYjVmZTdjYTFmNDgwODk0MmE4NGJjZTRiOTMwZDc5NDdmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:19:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlErb2ZpK1UwdytwaDJXcUhKR2QvSVE9PSIsInZhbHVlIjoiNHZMbkV4bkkzMlkwNlBVWmZxb0hOakFnQ0xWZ3NPaXFqRmtmWUZmeVN4bm04Q0VVZ2RkeGJEQW92YmRvUDlRRXRrdmJURUpLS2h1bGN0NENRd0hkaGoxSTFSRkFPN3JVZG1vTVNlb0NwcjFVWkJySzA3MWsrN2VCcENDM3UrWHY4UnhRYnNQRnFQS2xXZHBrUGNBbTJvMUxWRjF6NmUrNE95V0JITDd6Z2xoMjcxQnlvSFVyek4ydll4SUEyZHVWcVRMRmdyeW1iaS9oQlJvaWpsNzh1NW5xQ2dWNGltT0JOQVN5QUhWOXd6RkxIeWcvMis4c1JVMTcrMDVEYTF0cmYzQlB3YXMyMGhYVFVRV2I3WngyRGRDTjB1d2MrSXIxeG1YS1BweWNwM0JUMG5KM2YvcEw0T1VrUE5TN2RYL2hRazE5Y0U2NVdzSUQzU0VFNkpXaS9TdlVTM081UUxYaENrV01SMEZjbW4yUlJsa2t2ZTI1NUFaNFdrS2NLa3VMN3NLSDU3UTM3OUd5ZkVpSzNObzNJSVNUS1paMVlGSnN0Y3oza1YvanExUkN5U2ZaZWpGekFUNVVWNFBnN21VMklpTEVWdU9xcVBpckszeTlrY1g5eFJVUVNZTTZPUmEwTzJLTndEUVJqbXRGaVE2UUJZblF3eUtBL29mMzJlM1AiLCJtYWMiOiIwMGU5ZDNjYWQzZjkxNzhiNTdlZGY5MDllN2ZjMjhhZDZjOWI5ZjE4OGVmYTA1NWFmNWIwMDIwZTFhNjM3ZjBiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:19:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528780201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-787513901 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787513901\", {\"maxDepth\":0})</script>\n"}}