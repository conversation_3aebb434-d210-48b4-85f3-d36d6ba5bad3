{"__meta": {"id": "Xc60258c9165d5d64c8a50ed46e92322e", "datetime": "2025-07-31 16:33:37", "utime": **********.072072, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979613.364712, "end": **********.07214, "duration": 3.707427978515625, "duration_str": "3.71s", "measures": [{"label": "Booting", "start": 1753979613.364712, "relative_start": 0, "end": 1753979616.792175, "relative_end": 1753979616.792175, "duration": 3.4274630546569824, "duration_str": "3.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979616.792219, "relative_start": 3.***************, "end": **********.072146, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "280ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "M4Zx6FexkcIYAXnj5PTe1ajk7mFzZd9Y1SkgB9uO", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-741827498 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-741827498\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1267467778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1267467778\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1339190305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1339190305\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1353890903 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353890903\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1656715463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1656715463\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-658212942 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:33:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijk0U01GOUhubitBRmMvN1F0Mmw0N0E9PSIsInZhbHVlIjoiRkYrR3lBcWQ1QS91b0FCeit3ZUpINEhLdGp4OVJ2enBtV3U0QzdHWEdNdkJ0NnhhbS8wK1pLOU9zMFo1VDNiYThORzFxNUo4WW9PWVh0N3dHQ2J5ekswc2RFNU1RMXZkUUFlWlF3YSt5bHI2L1IrVm5GeWRqT3JYKzVlL0xJMGJtdFVCRjdHRzBjSDNaMlkwbFI1ZFkxaXYwZkRGRTF1M2Q5RTR3Q0dhUTN0ZXB2ODV1WklFVmVON3JWS2ppU0Q1aEtjNk1CYm9NdlJQWTFxdGNvSEFoZWplbmlOK01LdGxsb0NEa2szOEtreGlZVlg5SnFYdlZWK2xrRTh1M3lzanZGelVLckxkTjBodmxkOHpBbVZUUThhcGZ4S0gra2ROdlVYelRtSVIyY1dWUHZTWGc1RXJHUGZsbzAxVStJdFNpY05TZSthUkpMQnhySm1PbmxGTlNFblpPNXo3R2VzSWdRamRzbDF4bThNYnpxV1lkNVhabUlmTGU0T2ExZ2NnV3F2SUptbEdtRTF1NUpQSFZrY0hFb2xyZUMwRFNhK2dnTXZmN2UwZEFRUjFWZ0luUjBpNjM3bXl3NzA5Y2RzalJDZmptU2VUSmNaelQzQ09YaXZMQnIxenVuczJ1SFdUcHNib09GMzNiL01xWEFmeUNMZ1ExM2Q2eHM4TFhFTVoiLCJtYWMiOiIxMmJhMzQyNDAwZjU5YzkxYTMwZjJkYmE2Y2ZkM2FlODJiMDVhZjdiMzU3MjE4MDViYTIxM2EzZjE0YTIwMmE2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:33:36 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InI4L2lWSTc1R1daeG9mczhSUmRleWc9PSIsInZhbHVlIjoiNHU1VFFIN2xxMDFWN1RJUXE2ZTZqenhpeWd2Q2FhTmlQTWNhT3I1QWljZVFuRzEwWnIwUDRIaFFDM2lNS0NpekdUV2ZsTm9HMklaQWt6MVlobmVBY1JZYjF3SnR2c2dhckxGc0FRS1BKa1NZanBMNm42WVdOeDZnSGNnTDFUemFCcW4zZVRjeURRck9ZTjdhMmorN2twWEw1VTZKYVk2N0dUajg5SzRMalRXMVo0dWdPM3prTUQrWjdqak5TMHdrd2Eyakg0Sk0yZ1NsaFBhbEZndUNNUDdoQmZBNlFOWDYrWTVoOHNmUjUyYzNxakVzS0xxbzhGU2ZNTTM1ZWZidGhlWVpiTU9rdmVPQUU3a0FwTS9QdDJCU3NNSFVJRkJBcjJUNjhOUDhMNlQzaFVKSDQ4WjJqcUhQU1JOYVVqdERoNlRWQzFZejRBK0ZyU3lRK0lkamFsU3FEM1BTKzJnVHd5NFY5THVRSTB3YmpHaDVtbmRDbGNkN2p6WkRNQU9veTliK2hpNDdYdkFTZllRbXdQcjYyd2I4OUxVdFlQRnZQNVh6N2NWS2ZQTDVlVHNXV05LNzdSaUF5MEE1a1JoMlZkMkRIdjJHTEdrUVlmb2JCTWJPdWp4dHhheUUxZUJDaTNKQmFtYXB5MW1RWWNDQWhlYnYwWnhhL0Y5eERsMEoiLCJtYWMiOiI4YWE4MWQxYzdhNTBjMWM2M2Q4MDVjZjUyMTdlMjJjMjUxN2FjYzVmZjM2MjRlOThmYmY4M2EzNTAwMzk2YTEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:33:36 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijk0U01GOUhubitBRmMvN1F0Mmw0N0E9PSIsInZhbHVlIjoiRkYrR3lBcWQ1QS91b0FCeit3ZUpINEhLdGp4OVJ2enBtV3U0QzdHWEdNdkJ0NnhhbS8wK1pLOU9zMFo1VDNiYThORzFxNUo4WW9PWVh0N3dHQ2J5ekswc2RFNU1RMXZkUUFlWlF3YSt5bHI2L1IrVm5GeWRqT3JYKzVlL0xJMGJtdFVCRjdHRzBjSDNaMlkwbFI1ZFkxaXYwZkRGRTF1M2Q5RTR3Q0dhUTN0ZXB2ODV1WklFVmVON3JWS2ppU0Q1aEtjNk1CYm9NdlJQWTFxdGNvSEFoZWplbmlOK01LdGxsb0NEa2szOEtreGlZVlg5SnFYdlZWK2xrRTh1M3lzanZGelVLckxkTjBodmxkOHpBbVZUUThhcGZ4S0gra2ROdlVYelRtSVIyY1dWUHZTWGc1RXJHUGZsbzAxVStJdFNpY05TZSthUkpMQnhySm1PbmxGTlNFblpPNXo3R2VzSWdRamRzbDF4bThNYnpxV1lkNVhabUlmTGU0T2ExZ2NnV3F2SUptbEdtRTF1NUpQSFZrY0hFb2xyZUMwRFNhK2dnTXZmN2UwZEFRUjFWZ0luUjBpNjM3bXl3NzA5Y2RzalJDZmptU2VUSmNaelQzQ09YaXZMQnIxenVuczJ1SFdUcHNib09GMzNiL01xWEFmeUNMZ1ExM2Q2eHM4TFhFTVoiLCJtYWMiOiIxMmJhMzQyNDAwZjU5YzkxYTMwZjJkYmE2Y2ZkM2FlODJiMDVhZjdiMzU3MjE4MDViYTIxM2EzZjE0YTIwMmE2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:33:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InI4L2lWSTc1R1daeG9mczhSUmRleWc9PSIsInZhbHVlIjoiNHU1VFFIN2xxMDFWN1RJUXE2ZTZqenhpeWd2Q2FhTmlQTWNhT3I1QWljZVFuRzEwWnIwUDRIaFFDM2lNS0NpekdUV2ZsTm9HMklaQWt6MVlobmVBY1JZYjF3SnR2c2dhckxGc0FRS1BKa1NZanBMNm42WVdOeDZnSGNnTDFUemFCcW4zZVRjeURRck9ZTjdhMmorN2twWEw1VTZKYVk2N0dUajg5SzRMalRXMVo0dWdPM3prTUQrWjdqak5TMHdrd2Eyakg0Sk0yZ1NsaFBhbEZndUNNUDdoQmZBNlFOWDYrWTVoOHNmUjUyYzNxakVzS0xxbzhGU2ZNTTM1ZWZidGhlWVpiTU9rdmVPQUU3a0FwTS9QdDJCU3NNSFVJRkJBcjJUNjhOUDhMNlQzaFVKSDQ4WjJqcUhQU1JOYVVqdERoNlRWQzFZejRBK0ZyU3lRK0lkamFsU3FEM1BTKzJnVHd5NFY5THVRSTB3YmpHaDVtbmRDbGNkN2p6WkRNQU9veTliK2hpNDdYdkFTZllRbXdQcjYyd2I4OUxVdFlQRnZQNVh6N2NWS2ZQTDVlVHNXV05LNzdSaUF5MEE1a1JoMlZkMkRIdjJHTEdrUVlmb2JCTWJPdWp4dHhheUUxZUJDaTNKQmFtYXB5MW1RWWNDQWhlYnYwWnhhL0Y5eERsMEoiLCJtYWMiOiI4YWE4MWQxYzdhNTBjMWM2M2Q4MDVjZjUyMTdlMjJjMjUxN2FjYzVmZjM2MjRlOThmYmY4M2EzNTAwMzk2YTEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:33:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658212942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1992032245 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">M4Zx6FexkcIYAXnj5PTe1ajk7mFzZd9Y1SkgB9uO</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992032245\", {\"maxDepth\":0})</script>\n"}}