{"__meta": {"id": "Xb3c13b4713bacadeea5aedb8af177961", "datetime": "2025-07-31 15:52:06", "utime": **********.880534, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977124.396143, "end": **********.880626, "duration": 2.484483003616333, "duration_str": "2.48s", "measures": [{"label": "Booting", "start": 1753977124.396143, "relative_start": 0, "end": **********.627146, "relative_end": **********.627146, "duration": 2.2310030460357666, "duration_str": "2.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.627196, "relative_start": 2.***************, "end": **********.880635, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sTfRohnrOvgV9MlaiOY1DOletCnHi76G368ijFS8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1608011848 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1608011848\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-47456420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-47456420\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-829010121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-829010121\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1133113333 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133113333\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-887473338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-887473338\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1469764221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhGSWpQRnNDU2VtdzhkU1E2K3B0Mnc9PSIsInZhbHVlIjoiUW5ic05ydUVsRG5CbmZoYmplV3BITDhDMks0dVUwazRrUlVhNXlZaUFIc3l4RGxIWnRjY1JLRGpXZmVEYUhLMDZnZHR2b3o4MC9pOVZHYzVOWEprSGw2WERuKzZRV3prSW9JbHpPdFFiTVNkd2V1UFVaMVRQYWZsMHFuQmpucktySXNlT0x5MHBhdzRsZENwbE8yTFlKa1JTYnJXYXFPM1lTLzZHb3h4cHI5bHYwOVBENlpHczdhWkJ3YTlhTXJxTmc1SXFwYTBlNFdnVVBLbCt1YmNSUjY3eUllL0pYQnRJTVpDaEpzRlNjZmpzeE04YURQbG5wRm1Dd0VVVmpDQlJUbnluUDk1RHdzRnMvTlgzSUw2TU9CWU5OYlBxMDRtNGg0c2d0ZStFNGNyeVZCRWpCVWNEZXVyTjgvYnovMS9YT29VV21RRXFrZHBBRG5XWEQzazVBRU1tTnp2MHRPMkpOM2FMTnpWWng2VWREWG10cU5mVGlNM0lSUUtxT3d5VW1YSlVoVVM0alhvWXYxQllibkZhK1cyKzN3Z01SdHUwL2VTTWRObnNTMkdWQ0ZTY21tNkIzaldYUkw0T2F0eWxpUGNXTGszcUtCT2NxYTFPQ04vNEVlREp4bG9LWXFvNy9FSExKMHVtZ3o3NFAxL01za2pqOThESVY2MzRONW4iLCJtYWMiOiI0YTY4OGZhZmJjMjg4MDY5YmM2ZjEyZmU4MjVmMzFlZTlmNWY5ZTkzMjU2YTA4OWJmZGIzODk3YzQ0NjU5YWRlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFra21xb3Z6QTFFTC9nNHg5QkhHcFE9PSIsInZhbHVlIjoiQnJNWmFsYkhMMi9WRWNEWTRPK0ZFQkZyOGU2QW1LQWpmUjNTVEZxb0RReEdWT0N5S21zLzZYZGdrRGpNVzlUTEM0Z1E0RXMrYXA2M3J3Y2p2aXJkejhwNnVnMG9hZlM4YlVGVzlidktBQ0U3akNaWks4UldxeWhQTVVxYXJvSE93aDJCcVVQcVZHdUNyb2V1cExKZDZDNFRDWjE4MHFSRjRNVStLOG51NlJDZ04zQzk3ZzZPeVdORVZwbVJ2a3NMdHExbmwxZGdRdjZub3dRMGZwREhPazNvTXZ4RGlwL1pLTjBxc2lSeERvdFF4NWFxUFVMaTF1Y1FqYlpucG1NTTZKdW82cjFDa0VYMUt2SWRnTmkwMUJzNnkzQ1ZyeVo3WElzdGNGTFlsR0p3MWpPVTNFRXd4ZU0xS1gyTTQ2aVJzTk5GdFB2MVBKOWh2UkNYajlvckdjQWoybng5L1E1KzBYcSttaHNZV2lVQnUrU3IzYU5rZ2pVOU8rcHRZNGlxYWNoTDlienBEdnp5UUxnT25rRVkvN1dyY2QyZG83Unp1KytGRUoxdThUOXY1Z3YyTGpISlFNTWhvUk02cmwrcjFMeVpvY1I0cE9seThCSGdDYmRrVUpYVlZEVlBNQXloVzVORzF1S3gyNDJWdjQyQ0V2TVRjdmNodUNsTmlpeWwiLCJtYWMiOiI5YzI4NjY4ZWMzM2VmMzRhOWVmNmEyYzEzZjk4M2Q0ZWRhODY1MzBjMDUwNDZhMGE3ZTI3NDFiMzdmZjVkNzhkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhGSWpQRnNDU2VtdzhkU1E2K3B0Mnc9PSIsInZhbHVlIjoiUW5ic05ydUVsRG5CbmZoYmplV3BITDhDMks0dVUwazRrUlVhNXlZaUFIc3l4RGxIWnRjY1JLRGpXZmVEYUhLMDZnZHR2b3o4MC9pOVZHYzVOWEprSGw2WERuKzZRV3prSW9JbHpPdFFiTVNkd2V1UFVaMVRQYWZsMHFuQmpucktySXNlT0x5MHBhdzRsZENwbE8yTFlKa1JTYnJXYXFPM1lTLzZHb3h4cHI5bHYwOVBENlpHczdhWkJ3YTlhTXJxTmc1SXFwYTBlNFdnVVBLbCt1YmNSUjY3eUllL0pYQnRJTVpDaEpzRlNjZmpzeE04YURQbG5wRm1Dd0VVVmpDQlJUbnluUDk1RHdzRnMvTlgzSUw2TU9CWU5OYlBxMDRtNGg0c2d0ZStFNGNyeVZCRWpCVWNEZXVyTjgvYnovMS9YT29VV21RRXFrZHBBRG5XWEQzazVBRU1tTnp2MHRPMkpOM2FMTnpWWng2VWREWG10cU5mVGlNM0lSUUtxT3d5VW1YSlVoVVM0alhvWXYxQllibkZhK1cyKzN3Z01SdHUwL2VTTWRObnNTMkdWQ0ZTY21tNkIzaldYUkw0T2F0eWxpUGNXTGszcUtCT2NxYTFPQ04vNEVlREp4bG9LWXFvNy9FSExKMHVtZ3o3NFAxL01za2pqOThESVY2MzRONW4iLCJtYWMiOiI0YTY4OGZhZmJjMjg4MDY5YmM2ZjEyZmU4MjVmMzFlZTlmNWY5ZTkzMjU2YTA4OWJmZGIzODk3YzQ0NjU5YWRlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFra21xb3Z6QTFFTC9nNHg5QkhHcFE9PSIsInZhbHVlIjoiQnJNWmFsYkhMMi9WRWNEWTRPK0ZFQkZyOGU2QW1LQWpmUjNTVEZxb0RReEdWT0N5S21zLzZYZGdrRGpNVzlUTEM0Z1E0RXMrYXA2M3J3Y2p2aXJkejhwNnVnMG9hZlM4YlVGVzlidktBQ0U3akNaWks4UldxeWhQTVVxYXJvSE93aDJCcVVQcVZHdUNyb2V1cExKZDZDNFRDWjE4MHFSRjRNVStLOG51NlJDZ04zQzk3ZzZPeVdORVZwbVJ2a3NMdHExbmwxZGdRdjZub3dRMGZwREhPazNvTXZ4RGlwL1pLTjBxc2lSeERvdFF4NWFxUFVMaTF1Y1FqYlpucG1NTTZKdW82cjFDa0VYMUt2SWRnTmkwMUJzNnkzQ1ZyeVo3WElzdGNGTFlsR0p3MWpPVTNFRXd4ZU0xS1gyTTQ2aVJzTk5GdFB2MVBKOWh2UkNYajlvckdjQWoybng5L1E1KzBYcSttaHNZV2lVQnUrU3IzYU5rZ2pVOU8rcHRZNGlxYWNoTDlienBEdnp5UUxnT25rRVkvN1dyY2QyZG83Unp1KytGRUoxdThUOXY1Z3YyTGpISlFNTWhvUk02cmwrcjFMeVpvY1I0cE9seThCSGdDYmRrVUpYVlZEVlBNQXloVzVORzF1S3gyNDJWdjQyQ0V2TVRjdmNodUNsTmlpeWwiLCJtYWMiOiI5YzI4NjY4ZWMzM2VmMzRhOWVmNmEyYzEzZjk4M2Q0ZWRhODY1MzBjMDUwNDZhMGE3ZTI3NDFiMzdmZjVkNzhkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469764221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1099778035 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sTfRohnrOvgV9MlaiOY1DOletCnHi76G368ijFS8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099778035\", {\"maxDepth\":0})</script>\n"}}