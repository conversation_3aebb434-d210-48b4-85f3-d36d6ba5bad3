{"__meta": {"id": "X39a7737b268014d2e3e99348e5b620ab", "datetime": "2025-07-31 16:45:16", "utime": **********.507464, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980314.112188, "end": **********.507554, "duration": 2.3953659534454346, "duration_str": "2.4s", "measures": [{"label": "Booting", "start": 1753980314.112188, "relative_start": 0, "end": **********.253863, "relative_end": **********.253863, "duration": 2.1416749954223633, "duration_str": "2.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.253907, "relative_start": 2.***************, "end": **********.507561, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8N7nTvCeSMXVBwIP9J5esIP0QWkMelBlzT18aO2B", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-519979193 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-519979193\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-873357573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-873357573\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1231089573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1231089573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-780978245 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780978245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1271414827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1271414827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044821293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:45:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBuQUw0TEJ0V2xYMkR1YWhPWUlsbFE9PSIsInZhbHVlIjoiTXNSN3BXMmd0ekJ6OGNlRmdPeXhFYXlIMTNIUTU3UFZMUmdQKzBFQ01PUFMra3JvbnBkYWFBTHMvRFg0R094NjlSTzA4akswUHVIQzVENG9FbGhMb0dXZVNuMm96SGZnTnNyeDRSUGhvaGJraytTNG5VQXppTW9zYUZ1dHRqdDJETU42K1k1aEMzRklqUXFMcG9kakJBVzF2RTRldXA5cU9CeXR2UGN3Sy9GT2wyTFNtSStZanJaRTlYaUVONEFCamhvMkdkdVMzYzl2Z1BuUE1EQjdUcURQVDlTMzFRb2QrR0ZQMzNEdUdNYjhma0o1TWdIODJjTGNyNCtEckhIVU1EMGM3K01zaGVkM0RMencyQmh0S3hSOVc0akM3R3MzdjhQZFg1L1lEdDFpNWhJR0xvMkU3VlFCWW5OcWxnVzVScnhCdjVwNWQvY3I4LzFmbmk5bFE1ZWZsaGo4emhlRncrZzNYcnhOd1dJNGpCamE2blh2ZkV3Y3A0M3MxOGJRYnMvcUh5ZTY4YkhMVWt5TEs5TGRKWXR5cUkySVYyQU9ZUDhzOHhIekxYUU5kQzlOZnNiL3FjdGJBSW95cjUvUE9zWmwxR3JaMWZldktGbklQZFpYTlFvMnh6dzZmQis5QjhiL1R0Q2JMb2dNd0dnaGlXZ2diQ25OUThiNUxoQzMiLCJtYWMiOiIwZTJjMDA3ZTFiMzZjNjc3Mzc4OTBkNTMwODg3ZTM3MTZjYTQ5ZDAzNjBiNzI1YmY0ZDQwN2E3NmYwNzE0MTEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:45:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ilp6UlMzMUkrYXBmL1k2SmczN3Q2aXc9PSIsInZhbHVlIjoiRGhaTHliYVVVQ0VDWGd2b2FHN3dSbUI4Mk9zN2VIbXFiQ2pIVzRlSTI0L1N2NE9iRmtNd3NjRW0zcXI4RDN6dzRxN3hxNDJTcGtBU0xyZ1c4Z1pKdEVjeTNxYXFScjlEbUhYZFhvazBQWDd3ZjZEK3N3a0l0Z0dhMW9HczdRNEJzU3dwWFdGNzRiWi9tR0QzNVkyNjlhZlFsbnVjd3pWU05GVHpjVS9wa1Jaa2YyYWgwcGhqV3pLWjJDa0ZKeXdxZGU5THFTeG9WcCsxZ09zeXFEREJBQ2diSDZDSTQxUVptcmNKd1J5K3ptQ2JmNnJGK3hpcUhBSktLcUdwcXpRUVRHUXlDTUYyZ0p6MytHd3YvcC9BcldyQ2pFSElBYzNCUjViQkdGYlNkNkNGRUVwNSs4ejVJcFFTQ2l0dFByQ1lJU2ZCUmRnZTRhOXQ2c0l2ZzBUY0Q2UVJwLy9iL3hsSXh5bXFYODVVejl6MGpoVXZQRUxPak9GT2V2OUdiSjhSeS83WFo4K3lpeXd0TEwrRTc0TEdXQURteWMyUi9uUWR5Ui9zamxHdWhZWHhKdUJTVG9FNGtIVFl2RXA2NDlqdTJUdllsM2tnUHJ6czNQb0RYdEwwYXNNYmRRRVJWRTNhYlVGNGhvOEcvUkVvM2RTVEQ3a1A2QVEwQkQ5eFoybFMiLCJtYWMiOiI3Y2NiMGU2MzNlYWMzOWFiNmQxYjE3NjdmNjMxZGVhNzhhYzc1YzgxMzRhMDg0ZDRhMTg2NmY5ODNmMTI3NGQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:45:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBuQUw0TEJ0V2xYMkR1YWhPWUlsbFE9PSIsInZhbHVlIjoiTXNSN3BXMmd0ekJ6OGNlRmdPeXhFYXlIMTNIUTU3UFZMUmdQKzBFQ01PUFMra3JvbnBkYWFBTHMvRFg0R094NjlSTzA4akswUHVIQzVENG9FbGhMb0dXZVNuMm96SGZnTnNyeDRSUGhvaGJraytTNG5VQXppTW9zYUZ1dHRqdDJETU42K1k1aEMzRklqUXFMcG9kakJBVzF2RTRldXA5cU9CeXR2UGN3Sy9GT2wyTFNtSStZanJaRTlYaUVONEFCamhvMkdkdVMzYzl2Z1BuUE1EQjdUcURQVDlTMzFRb2QrR0ZQMzNEdUdNYjhma0o1TWdIODJjTGNyNCtEckhIVU1EMGM3K01zaGVkM0RMencyQmh0S3hSOVc0akM3R3MzdjhQZFg1L1lEdDFpNWhJR0xvMkU3VlFCWW5OcWxnVzVScnhCdjVwNWQvY3I4LzFmbmk5bFE1ZWZsaGo4emhlRncrZzNYcnhOd1dJNGpCamE2blh2ZkV3Y3A0M3MxOGJRYnMvcUh5ZTY4YkhMVWt5TEs5TGRKWXR5cUkySVYyQU9ZUDhzOHhIekxYUU5kQzlOZnNiL3FjdGJBSW95cjUvUE9zWmwxR3JaMWZldktGbklQZFpYTlFvMnh6dzZmQis5QjhiL1R0Q2JMb2dNd0dnaGlXZ2diQ25OUThiNUxoQzMiLCJtYWMiOiIwZTJjMDA3ZTFiMzZjNjc3Mzc4OTBkNTMwODg3ZTM3MTZjYTQ5ZDAzNjBiNzI1YmY0ZDQwN2E3NmYwNzE0MTEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:45:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ilp6UlMzMUkrYXBmL1k2SmczN3Q2aXc9PSIsInZhbHVlIjoiRGhaTHliYVVVQ0VDWGd2b2FHN3dSbUI4Mk9zN2VIbXFiQ2pIVzRlSTI0L1N2NE9iRmtNd3NjRW0zcXI4RDN6dzRxN3hxNDJTcGtBU0xyZ1c4Z1pKdEVjeTNxYXFScjlEbUhYZFhvazBQWDd3ZjZEK3N3a0l0Z0dhMW9HczdRNEJzU3dwWFdGNzRiWi9tR0QzNVkyNjlhZlFsbnVjd3pWU05GVHpjVS9wa1Jaa2YyYWgwcGhqV3pLWjJDa0ZKeXdxZGU5THFTeG9WcCsxZ09zeXFEREJBQ2diSDZDSTQxUVptcmNKd1J5K3ptQ2JmNnJGK3hpcUhBSktLcUdwcXpRUVRHUXlDTUYyZ0p6MytHd3YvcC9BcldyQ2pFSElBYzNCUjViQkdGYlNkNkNGRUVwNSs4ejVJcFFTQ2l0dFByQ1lJU2ZCUmRnZTRhOXQ2c0l2ZzBUY0Q2UVJwLy9iL3hsSXh5bXFYODVVejl6MGpoVXZQRUxPak9GT2V2OUdiSjhSeS83WFo4K3lpeXd0TEwrRTc0TEdXQURteWMyUi9uUWR5Ui9zamxHdWhZWHhKdUJTVG9FNGtIVFl2RXA2NDlqdTJUdllsM2tnUHJ6czNQb0RYdEwwYXNNYmRRRVJWRTNhYlVGNGhvOEcvUkVvM2RTVEQ3a1A2QVEwQkQ5eFoybFMiLCJtYWMiOiI3Y2NiMGU2MzNlYWMzOWFiNmQxYjE3NjdmNjMxZGVhNzhhYzc1YzgxMzRhMDg0ZDRhMTg2NmY5ODNmMTI3NGQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:45:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044821293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1783344830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8N7nTvCeSMXVBwIP9J5esIP0QWkMelBlzT18aO2B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783344830\", {\"maxDepth\":0})</script>\n"}}