{"__meta": {"id": "X17826f93ce2c924527f226dc4611529a", "datetime": "2025-07-31 16:25:21", "utime": **********.21697, "method": "GET", "uri": "/finance/sales/contacts/customer/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979118.758214, "end": **********.217003, "duration": 2.458789110183716, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1753979118.758214, "relative_start": 0, "end": 1753979120.945767, "relative_end": 1753979120.945767, "duration": 2.1875529289245605, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979120.945801, "relative_start": 2.187587022781372, "end": **********.217006, "relative_end": 2.86102294921875e-06, "duration": 0.27120494842529297, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00925, "accumulated_duration_str": "9.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.141016, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 58.703}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1772032, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 58.703, "width_percent": 25.946}, {"sql": "select * from `customers` where `id` = '4' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["4", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.190633, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 84.649, "width_percent": 15.351}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/4", "status_code": "<pre class=sf-dump id=sf-dump-1335780882 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1335780882\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-254209294 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254209294\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1003632122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1003632122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFMSytSZ0RheDNENkc0UnlrWHh6c3c9PSIsInZhbHVlIjoieXBCdGtJcUxCeTQ1eEFENmFldVpNT1M5d0RUNi80TGlYZUZWYnlFL1R4T1dObXVpcjBRdWZzdmQ1S0VuWjEyd3N2Q0VhenQ4bTRQTUtaelQxQ0xWS25nbnFseWl5ekh5NFd6bE9NbGlGSGxSTG8yd00zY2tBeFY5c3Y3U3FrWmZjL0VhRkx0clE3QmxEcllLM1lSeFNxSUluMG4zTW9BSUVXcmpZd0dvZFlmYktwS2pRNXBldmFZTGtsU2Q4M3NZdHRiclZLNUNvWDF3bXRXQTdVOXY4VUNjN0FxSEpkU2RnM25NV25YZFEwbmg2RG5tM3Vwd0xQblNaYzhFTlJZMEkrRFc1V2s0MEFhaEFkOEVKS0FRQ2QxRFVrZElhQXdIWFprOTlFcUFkZldZNmdqU1ZpQUpyaGEwRGpOTUpTNGsrZVBLRUtPSnhUY1FZL1lOQWc1cjZaSUh5QStEVnJuYW51VFZmcU9PYzhZM0wvMUpQbFEwRlpZWWJPdUpLQWVnWVJLV2kzUEplRTVvckxxZ1dqaWVoQjBqTW1CNU01UVJWOFpwYnk4WHZqMkluRVlLMFRINTFsbGN3TVhoUlZvTm50dmxYU3p1ZnVwN2U3Rk92MnVkbkVJU1ZITzdsSURwZUplblZ2RHkyNHVnRGMrdjdveERlSzVoTU9ERktzbkkiLCJtYWMiOiIwMTFhMzc5MTRhZDA1OGU3ZTkxMzAwNDNmZGU5YjliYTAzMzhmNDdkMDc4NGZlZmM4NWVmYTNiMGYzOTYyMmM0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlVLdGV0V2lDMUVlb2lDaVA0c2o2b2c9PSIsInZhbHVlIjoidGFIb1ZHS3phUGVlcFVlOEIvemtmWDdzTWpkU3JabFZwZ1lvTkNPMVVSVWlJS2ZFZWs1NkFwY3o2WnZQRGxvVE9kdHhJU3JVMzYzUzZRL002SFVHalRIZFhNSGdnVjIzQjlSbm5zUmt6UFpBZytCMVNKVVc3TjViWUZ3anpHRHRCS2hRNENyeXpFZnBnUWtHNjUxb1lwM0E3SDhSd255RVBMM0RXblNtRDQ5S1NlbFBTN0xsRVBoSjg2RThkdDhNMld2OTFoN1FMUG90b0xkTlpzcFBmdFRrblNkSW45MXZETFVFNHpzM05LSHIxVHdQUklwSkpaMjdyUXBwd3V6NUlOUlg1Zms0T0VER0VNOTFFV0JkZEdrNSs3WWd1M1k5S0hvdlBaWVMvdzZDY2czVldPVWZ6QzcxVjU3VTc5MzMwTWdCajlBUUhMK1lKZkoxa1c2L3lvdmFVb0V6WCtxbmozWnFOOHJLcTFNVWVZOHFjUkxEMVdaRkUwLzFaWFN1UDUveFRuODVaaFFyZXg4ZDlhb1NBWmJBL0d2NmlweTlacU96ME1lMkRUS2Q1QXJRQWNiS3BjaVhNWFo5NW0wblhJTGpLYUxENXdRVkk1MjROQjg1YlRCZ0YzeG1SbUE0R3IyYTU4TTlLbUg1VXNXWTV6SkQwclNxQldlanpBTmYiLCJtYWMiOiJmYzVmZWQxMWMyMTc1ZDNiN2FmZjE2MDQ2YTgwOWM4YzZmYzhjYjdlY2Y4ZDc2ZmMzYTY0OTA4MzVhNWJiZGMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-207604727 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:25:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRjTFA0K1dPcmR0Ujg0T1NwN2RxRGc9PSIsInZhbHVlIjoiNlFwZVY2K0V4NnFTNHJWdjYyclRFcXloZVZONzBGazFHVmlUdWZUdE9pNFYvK3ZRSXFRSCtDbHdiMitvOHV5Y1luMTBCUmdrNmtURCtFa2ZYTTZUbjRNdDBHUDlMeStvQ2ZHZTI1b2RMb1BTejRVQklxcnFlUGplemFQelp0Z3RkOUFRdENTT09qd2ZidzNXNTJYT0diNkZRVHZBSDAxTk85cUV4TThyeTc0UnhNbmxQZlEzUkdRdnpUbU4xOFd4YWlFTVA0S1R1ZmNXelR1V3VGSTRLbm16TTlJM2Rwemo5NGpKMmJEYlNDeUlNaHJOSWI5UTcwYnU0K0JXSWFPdzFhbDJuZy9LYjdNVEtGZ244SnRzWjliLzNRbkpqbnZXRGhQbHBHWEhKTzBVbGx5YmcxVmh5NzFHNjdLL3RWdUk1c1NSNWZTNlkwbWJrQTR1OW81T1QxVUJ5dE9CL2VYV3NCYWNjYkdqdUpkcStDTUtHc01idmg1SnZjWjRNTmEyWGtWZ1VnRDZjeEJJR1RIK2tRbkhCTkpPa0R4cE1OZ0l4YytqakN6bWFlcVdXSEJHNmt2STVaNWtUSENwdEFsWGsvVXo2UHRHMHh5UU9YaGMzTDhZbWhvUE1OS3ZlZHh5SDlLSzJ5b2RZdDlLMDVCaWZXZHB3RzhGVktGcmQvMDEiLCJtYWMiOiJhN2ZiM2FhZDlmZGIwOWY2ZWYyMzQ5Y2E3NWZlZWJjNTgwNmU5MTViMWU3MzFiNzM2NjkyNzllNzYyNTk4ZDZlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:25:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InFOVmo5ZkpjYURVVndYU1puMk5GTkE9PSIsInZhbHVlIjoiSDZVek9aSE1HdElQNDZIaHQzVncvS1ZQTCtxZkNEZ3g0RDY0VmlNVS9sN0d3anpHUUFZVHplM0JYN3BtbVJXZTVSWThCVUF4WVFaTklZY0w4S2NucG1UNjFYT0hoQ3N5WDk0WjUwZmd5MGFBVlVOVmk3Z2syVzRvRmJLTkhOYUs4UUdnRjBHQ3NmTCtPeVB4Vzg1ekh1bjRqSlYyZGhsQjQyRzZWYmlkUVRvOFBZQmcwcEwybzJCMXl1eW9SQ1N6UFo4ZDVsY1B1Qko2RHlyN0NUZDh0QnppekVvT0RhcDlUZnd2OHYzMVBDUFk2RDNNY250L21UM3hCTlgzMUpEV3AvNFUxNFdSV3VyMU5xTGtab0FNMlR4ajQrNXN6ekNHUGJqNTJ6cFp0R0VPb2llVURFZW90WExQQ2pzUVdPNjE5aVJ1VEl4U2pyRE4wdXA5eGNKcFBlNUYrdGJQVGlvTXJTM3JoRmdPbURWZndReTgxNXd0MjA3Y0RidmgyNDRzcU9OV2l2RktWK0Rvbkx6YnZscHJWMkRkcnJtVk1weVo4NDhNMlNjNWhkenV1MWR3cUFnenl1WUlXR3dyd1djKzBYWkJlSjdtMVFqVmUyNTJvcnlxamFRQ0tGOTNPZEpaYUM3amYxY3pCaE5ubGpKNHFzUjlxRFFEeldKQjNRWGIiLCJtYWMiOiIyMDQ2OGY5NzVmMDM5MWQ4MDljNTRkZmM0NDJmZWQ5YWQ3NjQyMWYyZTk4NDVmN2Y3NGM0NTI0YzEyMmM0M2IxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:25:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRjTFA0K1dPcmR0Ujg0T1NwN2RxRGc9PSIsInZhbHVlIjoiNlFwZVY2K0V4NnFTNHJWdjYyclRFcXloZVZONzBGazFHVmlUdWZUdE9pNFYvK3ZRSXFRSCtDbHdiMitvOHV5Y1luMTBCUmdrNmtURCtFa2ZYTTZUbjRNdDBHUDlMeStvQ2ZHZTI1b2RMb1BTejRVQklxcnFlUGplemFQelp0Z3RkOUFRdENTT09qd2ZidzNXNTJYT0diNkZRVHZBSDAxTk85cUV4TThyeTc0UnhNbmxQZlEzUkdRdnpUbU4xOFd4YWlFTVA0S1R1ZmNXelR1V3VGSTRLbm16TTlJM2Rwemo5NGpKMmJEYlNDeUlNaHJOSWI5UTcwYnU0K0JXSWFPdzFhbDJuZy9LYjdNVEtGZ244SnRzWjliLzNRbkpqbnZXRGhQbHBHWEhKTzBVbGx5YmcxVmh5NzFHNjdLL3RWdUk1c1NSNWZTNlkwbWJrQTR1OW81T1QxVUJ5dE9CL2VYV3NCYWNjYkdqdUpkcStDTUtHc01idmg1SnZjWjRNTmEyWGtWZ1VnRDZjeEJJR1RIK2tRbkhCTkpPa0R4cE1OZ0l4YytqakN6bWFlcVdXSEJHNmt2STVaNWtUSENwdEFsWGsvVXo2UHRHMHh5UU9YaGMzTDhZbWhvUE1OS3ZlZHh5SDlLSzJ5b2RZdDlLMDVCaWZXZHB3RzhGVktGcmQvMDEiLCJtYWMiOiJhN2ZiM2FhZDlmZGIwOWY2ZWYyMzQ5Y2E3NWZlZWJjNTgwNmU5MTViMWU3MzFiNzM2NjkyNzllNzYyNTk4ZDZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:25:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InFOVmo5ZkpjYURVVndYU1puMk5GTkE9PSIsInZhbHVlIjoiSDZVek9aSE1HdElQNDZIaHQzVncvS1ZQTCtxZkNEZ3g0RDY0VmlNVS9sN0d3anpHUUFZVHplM0JYN3BtbVJXZTVSWThCVUF4WVFaTklZY0w4S2NucG1UNjFYT0hoQ3N5WDk0WjUwZmd5MGFBVlVOVmk3Z2syVzRvRmJLTkhOYUs4UUdnRjBHQ3NmTCtPeVB4Vzg1ekh1bjRqSlYyZGhsQjQyRzZWYmlkUVRvOFBZQmcwcEwybzJCMXl1eW9SQ1N6UFo4ZDVsY1B1Qko2RHlyN0NUZDh0QnppekVvT0RhcDlUZnd2OHYzMVBDUFk2RDNNY250L21UM3hCTlgzMUpEV3AvNFUxNFdSV3VyMU5xTGtab0FNMlR4ajQrNXN6ekNHUGJqNTJ6cFp0R0VPb2llVURFZW90WExQQ2pzUVdPNjE5aVJ1VEl4U2pyRE4wdXA5eGNKcFBlNUYrdGJQVGlvTXJTM3JoRmdPbURWZndReTgxNXd0MjA3Y0RidmgyNDRzcU9OV2l2RktWK0Rvbkx6YnZscHJWMkRkcnJtVk1weVo4NDhNMlNjNWhkenV1MWR3cUFnenl1WUlXR3dyd1djKzBYWkJlSjdtMVFqVmUyNTJvcnlxamFRQ0tGOTNPZEpaYUM3amYxY3pCaE5ubGpKNHFzUjlxRFFEeldKQjNRWGIiLCJtYWMiOiIyMDQ2OGY5NzVmMDM5MWQ4MDljNTRkZmM0NDJmZWQ5YWQ3NjQyMWYyZTk4NDVmN2Y3NGM0NTI0YzEyMmM0M2IxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:25:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207604727\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1587328925 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587328925\", {\"maxDepth\":0})</script>\n"}}