{"__meta": {"id": "X6dc9d69a26e1891182e699d0426d88b9", "datetime": "2025-07-31 16:33:45", "utime": **********.198233, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979621.352281, "end": **********.198299, "duration": 3.846017837524414, "duration_str": "3.85s", "measures": [{"label": "Booting", "start": 1753979621.352281, "relative_start": 0, "end": 1753979624.737681, "relative_end": 1753979624.737681, "duration": 3.38539981842041, "duration_str": "3.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979624.737736, "relative_start": 3.****************, "end": **********.198306, "relative_end": 7.152557373046875e-06, "duration": 0.****************, "duration_str": "461ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.027010000000000003, "accumulated_duration_str": "27.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.044593, "duration": 0.027010000000000003, "duration_str": "27.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-164260213 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-164260213\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-186559611 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-186559611\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2101737399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2101737399\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-535236618 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVjNW00dmdqc3ZRaFNSOTNzNnFrb1E9PSIsInZhbHVlIjoidTdhTXRncmI4cEZrSm5RTmZTZytaT04yVTdKcEZTd09SMDVpNnNIZHJTY2tLMWtCK3hrZWhpcWNITkNTWWFSaS9iSTFpUVMxWnBPaVppS1lTcG1DZGUyZXNUT2pPUGpMTjNOM1NOSEV3RTdESDF1WFB5YUR3dHQ2U24yRDd5dytqY01iSDY5NzJpa2Fza2J2dGdLaXZ4eUEwcm1LZEhVTEg4YzRqNFNOamxtck9La1FzYWVaVFBwc1MzdEVDc1NGakZsUGgxeGVLalMzUXl0QUlySUhhUUZabFM4Z2Y1NzJVak9MQU5pRG1COElpM3ExTWNYZXR4LzY3TzFDYXl3WWJKbnh3bEV0ZnBHVVN2Z3ltQ2NhMktKY29JbUl2VHB3QmlHUW9HdkU1UGxhaEpPcG45ZkZHSDBnTlJQM080Nm5oRnNHclVWRGliUzh6VHJxaHVLUFFHN081QjB4VFQxY2J1WGpmRG1wd0M4UFMwZXlDT2t2SVhUdmJVdEIzVUlVeXJPanJCMGkwMnJCOXhSUldEQ0dTTGlpQmd0VEswSnJnMGZ1MTMvUDlVUzMwNHJMRFg5cE15anRkOHEwSGk4SGxLay9neEhxOVY4emNnZ3c2RVRkWTB3QVhVV2JFajZOOHlrTzBwajZUUDQwd29EMHVXajF4MUZJRUJOdWM2QW0iLCJtYWMiOiJmNTU4NWRmN2NjZmFiNjE5ZWQ1NDhlZTE2YmU5MmVhYTUwNzMzNjNkNzYyNTMwMGI0ZmEwYzFlODhlNGQ0MDViIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InZUaHVKKzBZalNnSDEzeVVna3FKeFE9PSIsInZhbHVlIjoiTzV0M1lKSWdoYU5lNThLTEw5VGd3VHVGNE56N2w3OVp3TUc3UnpyTGlFVWZvUGFYRTlEd2xId3d6ZG91dmVGc3BwNC9KYnBXRzljei93ZnR0Y2JkZHFvUmNFVnN4eWxhUEJZM0V0Um1tVWZQM1l4MkowNDVSREZCbXhsR2NGcUtqNit1MFZ5Y1VoVVVqV3cyaXhMQXZhOG1PcWNUanUrQ1J4UFpJVm5CQXJwMXRpVys2bmJQNk4zTnJXbHQ2UVQ5d3d4UllNUmR3cCtKZ0F1ZXpsTitoWUR4T1BYMjlMQXNMTHFpdVRzYjVRWXlXTEhzR2RzclZrL3E5UFVObUEzMzZIZjYxNk9oR0Mycjl3WUd1ckJFS1hRTUcwTm5GUzRPUk9jUG0wUHA1MThIb2UvUzUrQXY0R3kxc3RlbUtCZHJ1RE9IS2xSemVodnRjczM0RWpueVBTbGdzc0M4VURUaGZlbHhLQTQ4SE9PdCtsenVSZTJhZzBtQXNiWTZjTW9VaWc5bm00QTZjcDMyQnFsSXlSTGNuZ3kvL0o4WFJCK0F0M05UTVViRzlXWCtQbUgwWmVZQ1dtV3c0SjgvaE9yWFZ0V1dxbE02aXlVSTNKMURGSHBvZ2lUYms5cWk4SDNDNTFIY2FtTndsNGxycEFIaUgvZ0NyL2J4L2VpL2NlK0ciLCJtYWMiOiI3ZWM0OWVlMDJkNDhjMzEzNzk0MDQ4OTJlYmE0M2ZkOTZjYWRjZDhlYmQ4ODdhZGNjMzI2ZjBjMGUyYzJkMTVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535236618\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1997674094 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997674094\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-419818581 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:33:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitsdTZnRndOV3c0clVyQmdxU1FoOUE9PSIsInZhbHVlIjoiRnA2MVR2NkFBZ25oM3dqbXZLQmtZWVZSNWd3VllEY2MrUzFCYTQ1am9BbUVZbUkvR1dvRnBLTm5PUGNvZjBIWnhiRFNEM1NjWkZIRTZYek43YncraXo2Mml2eTE4eitpUDVuY1ZPa3JTL0ZHenE3QzJ6UUlCNFhFKy9IOHYwcTNobUozeTJxMVI5SUh1RlNLUnN0cnhSVFJrVS9WNk1jb0R3R0RKV1BvaFpEQ3BmQ2dRYTdKR1RYVjZKZ1VjQy9pbFpzc2MyVUN1dm1lNUZrQ1BpM1ltczVhOUs5NEJZQlBGNDI5c3hZTGl4eWFUTmIvdzk2QVJhaDZzS0xrc0hPamlhdWphT1VhVkhxWTVwd21lbi9HVnVmSkJrQlRtVDdaQ2JzRkxJK2t5dmFtMnBsU1N1NUZBWFVwcGJBQ3N0c1JQbzVjOUp3M3pSMCtMaCtoc2xNdENtcXlRZTRJcGt4Y3hDMFpDUVFhbjBiQlQ5alZJY05JTklVWWhYanJDeEpkNzYrS1l4MGdhY21WV3NjYjY1Y3dGT0dUR0dJTmRPVTVEVWRLNFJwVUUrbHNqZHRwWWxRZ2lJZnpuTHZ4REVrWk90UWJnTUtSb29KamtrbUcwS1dCeHFSK1VwdHlSb0l2a0MwSWg1UXpObEdGU3BhYTNjRVRHTFZJTFdkQkY2MjUiLCJtYWMiOiJkYTg2YWYyNzExNjVhYTY2MDRlNGU5MzEzM2JjNTcyNDExZTliNTRkY2NmZmE4MTg4MDllZmZkMDkxZTBjODEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:33:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZqVFFYZVlqaldoUG1ackx0N1VzZ2c9PSIsInZhbHVlIjoiWFZ3bGZrRjlpQ0VmeXpKSkJxSmxSOXlJVnoxVUdsT3RDN252MHJhN2pyTlpyOUF1cVd3eVNSSFdYYjZuU0ZMYjRBUk9FaEpTQ3VMNHJ4c3RVUThhci9FWGpHcFFqQnpYVDFLdzdnbUlpclBrbXBldUJuc1RoMlh4M1pWODdQV2hmYVY2TUNuQWxDTTlJb0N0MjhwQkc3WURJdXFDZFU0UnBZZFhDOG1tejhXOWFabUc4bVNiQkNScWF2M1VlN1pWUXMxYm1TOTd4Q0h2NDJ3Nkh4ZUEwMUx2WittZTgwYkROWFExWHU5bklKY3VXWUtYS2tGbk5Fc3pDTUxaYitOanpsOVNqY29FODNFS0VlZ1BBSXo4VUNERlRuWkR0Y3owWTRkZmZtM0YycEVudWh5V1c3YThKcE5PUGxuME90RWliSVFOZzU5clVDVHF1RmZaTU5zaXRzNkVjTDNQUGNxSUsrd0xWYkZmRUp2dWV0eDVHZy9sYkhISTEvZWg4YWtmWGdiZHFBMTJwTmNMTDc3NEowcEFTLytEOUxWY21VYzU5dFpJbElxMzFmcHd1elBQMnFBQlZvQ3prUW5USkd6cmlvOHJpRjhZR3IvQkZ5K1l5L1VON0xMeTc4N093S053MzBJcFNFN0FEV2pMc2xObmp3aTNFMWh4MENHOEpYRzAiLCJtYWMiOiJmOGVlNTc2ZDNlOTc4N2ZlYTNjYWFiZDNiN2ViMTA0ZDQ0MjFlMTdmMWI3ZjY1YmZmYjgwNDBjYzI2MGNmN2RmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:33:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitsdTZnRndOV3c0clVyQmdxU1FoOUE9PSIsInZhbHVlIjoiRnA2MVR2NkFBZ25oM3dqbXZLQmtZWVZSNWd3VllEY2MrUzFCYTQ1am9BbUVZbUkvR1dvRnBLTm5PUGNvZjBIWnhiRFNEM1NjWkZIRTZYek43YncraXo2Mml2eTE4eitpUDVuY1ZPa3JTL0ZHenE3QzJ6UUlCNFhFKy9IOHYwcTNobUozeTJxMVI5SUh1RlNLUnN0cnhSVFJrVS9WNk1jb0R3R0RKV1BvaFpEQ3BmQ2dRYTdKR1RYVjZKZ1VjQy9pbFpzc2MyVUN1dm1lNUZrQ1BpM1ltczVhOUs5NEJZQlBGNDI5c3hZTGl4eWFUTmIvdzk2QVJhaDZzS0xrc0hPamlhdWphT1VhVkhxWTVwd21lbi9HVnVmSkJrQlRtVDdaQ2JzRkxJK2t5dmFtMnBsU1N1NUZBWFVwcGJBQ3N0c1JQbzVjOUp3M3pSMCtMaCtoc2xNdENtcXlRZTRJcGt4Y3hDMFpDUVFhbjBiQlQ5alZJY05JTklVWWhYanJDeEpkNzYrS1l4MGdhY21WV3NjYjY1Y3dGT0dUR0dJTmRPVTVEVWRLNFJwVUUrbHNqZHRwWWxRZ2lJZnpuTHZ4REVrWk90UWJnTUtSb29KamtrbUcwS1dCeHFSK1VwdHlSb0l2a0MwSWg1UXpObEdGU3BhYTNjRVRHTFZJTFdkQkY2MjUiLCJtYWMiOiJkYTg2YWYyNzExNjVhYTY2MDRlNGU5MzEzM2JjNTcyNDExZTliNTRkY2NmZmE4MTg4MDllZmZkMDkxZTBjODEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:33:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZqVFFYZVlqaldoUG1ackx0N1VzZ2c9PSIsInZhbHVlIjoiWFZ3bGZrRjlpQ0VmeXpKSkJxSmxSOXlJVnoxVUdsT3RDN252MHJhN2pyTlpyOUF1cVd3eVNSSFdYYjZuU0ZMYjRBUk9FaEpTQ3VMNHJ4c3RVUThhci9FWGpHcFFqQnpYVDFLdzdnbUlpclBrbXBldUJuc1RoMlh4M1pWODdQV2hmYVY2TUNuQWxDTTlJb0N0MjhwQkc3WURJdXFDZFU0UnBZZFhDOG1tejhXOWFabUc4bVNiQkNScWF2M1VlN1pWUXMxYm1TOTd4Q0h2NDJ3Nkh4ZUEwMUx2WittZTgwYkROWFExWHU5bklKY3VXWUtYS2tGbk5Fc3pDTUxaYitOanpsOVNqY29FODNFS0VlZ1BBSXo4VUNERlRuWkR0Y3owWTRkZmZtM0YycEVudWh5V1c3YThKcE5PUGxuME90RWliSVFOZzU5clVDVHF1RmZaTU5zaXRzNkVjTDNQUGNxSUsrd0xWYkZmRUp2dWV0eDVHZy9sYkhISTEvZWg4YWtmWGdiZHFBMTJwTmNMTDc3NEowcEFTLytEOUxWY21VYzU5dFpJbElxMzFmcHd1elBQMnFBQlZvQ3prUW5USkd6cmlvOHJpRjhZR3IvQkZ5K1l5L1VON0xMeTc4N093S053MzBJcFNFN0FEV2pMc2xObmp3aTNFMWh4MENHOEpYRzAiLCJtYWMiOiJmOGVlNTc2ZDNlOTc4N2ZlYTNjYWFiZDNiN2ViMTA0ZDQ0MjFlMTdmMWI3ZjY1YmZmYjgwNDBjYzI2MGNmN2RmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:33:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419818581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-374137257 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374137257\", {\"maxDepth\":0})</script>\n"}}