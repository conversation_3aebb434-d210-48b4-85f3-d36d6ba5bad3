{"__meta": {"id": "X6506ccc5f06496cc4040a9f5a4afe9b2", "datetime": "2025-07-31 16:46:14", "utime": **********.830903, "method": "GET", "uri": "/finance/sales/contacts/customer/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980373.304768, "end": **********.830939, "duration": 1.5261709690093994, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1753980373.304768, "relative_start": 0, "end": **********.637911, "relative_end": **********.637911, "duration": 1.3331429958343506, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.637938, "relative_start": 1.333169937133789, "end": **********.830943, "relative_end": 4.0531158447265625e-06, "duration": 0.19300508499145508, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02361, "accumulated_duration_str": "23.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.752821, "duration": 0.0206, "duration_str": "20.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 87.251}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.798471, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 87.251, "width_percent": 7.2}, {"sql": "select * from `customers` where `id` = '4' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["4", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809511, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 94.452, "width_percent": 5.548}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/4", "status_code": "<pre class=sf-dump id=sf-dump-483789778 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-483789778\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2130078428 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2130078428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1889035640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1889035640\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImFzNm9MV1owWmE0ZHhHd2E0RzRVeFE9PSIsInZhbHVlIjoiVjRpRUpmNXR3bTZpUkhMSlcrMEk4SW5xeTZ3MnEvNHE3anlPK1ZmVldzbW83ZjljRnFHbEg4NVppMXRtMDkvYk9BL0E5bFVVSkpJOWE4N0pTNUpla0czVFlzd2hVM1ZQQ3NUUHdzS0Q3ejlhb2RBLzBOWG0rSWxscTY1WDByTUNGalVaTTcyVXMwVzZkSW9Lc3FBTFNSUW5KMThiMjNOWm5FUFh0S25VKzdzYWcwTDhIQmhkd3VNc29ZYmpJT3pjeEU1OFF6QmhvN1BFZUQ5SHczampwRWZ1enFsaTI2Uy9seUlVd3d0RDRXUjZDek9ZOENnTFZ1N0wxdWJIS0JaclRqSUZFQVlpd3BETytUYjE2OHVtWGFuVy9Cc3dZaUhsZTQ4eG9MaWNjT0RlVmE0bTgrTG1WbVVGM05USjFiTXc1VmdDUjJNODdkMDV2cFl1dzh2eWtEeGo3Qlcrdk5SeXJPcHMwbERRcVM0aTd3YTdJc2ROeXNURk1DdmMyTFdhdHFtVTZvZzZKVWlabjNHMjlTQUdhb0lLWm54Qk1EMmdpdmlzWDlKeDJBUHNNS2NlbXJCOThhRFZKSFZ1LzNwcmFwVjlwQzh3cGphUzBjV0hoUDF0TTllSk0wRWVxczRnL0picGsvUWhHaGk0STdpWTJDTG5VMnhmZXkxNGFzb2IiLCJtYWMiOiI4YjhmOTRkNzI2NTVlYmM1MDk2ZWE4MWRmYjg3MDExMjFkOGRiYmRkODJiODgwYzI3YTBlMjgwODY3ZmE5N2JiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjRpbHVJdHJrUHlkSWlzMUJFWXpIZnc9PSIsInZhbHVlIjoiR0lDSE5jRVIvem9hdjVDRlUyb1FzY2o3OTNNRU13QlVmd1B3cFNHM1BnRFJHZXV5ZnhuNmFIL3g2c3ZLQjEyejFaZ29kbjlHNjB0VjBoZVlBd01sOUtkMmduV0dqNFRHaW0vcHFEbXVqaUpFZVB3QWx3RUF6UWpNb0l1V0F1djRjNWxGSURKQTBEa0NHejVtNTdhTEJYQXRDMEV5V2lVMFdnZ2NUK2NDa1F4YTF4alY5cEp2Q09EWWdVdEdNcVY3bFc2bW0yWmZ0THdLc0tFdjJnNThXV1Q0ZjRRQTZOWjRSU21mOXREZzNCMThGbzJoYjJiQ0FjZEN5SUtxZGxVM0kxYzJVWDRMaGpVc0ZpN3lsVVYyL0JqSVFuOVgzcTRuQlByZURoaDAvNXF4Qm8yUFRFMEo5VjR0d0NYWWsxeUU5NXkrR2Y3anZFV1R3YXhYVlM2K1c5MmdidTJNc2FmMXVBdTlONHZWc1JLZS93KytVYXJGZGJ5Q3lVUFh1WThPTjlXL0N4enNuNUFEcFl1WkpQUkEwUmZxWXI1bjF2cTBibFgrVzNVUzI4OHY2ZG9YcGFMSWJSK0QwRlhQVTZmcU11My8wSHFJcWUwVWo4VkpuMndzbzNabnFLMnE3ZEh4VFQyb2pmenYwbkhzUHJJOGorUE1Jc3ZOdzJyaUpuY2giLCJtYWMiOiJhYWQ4Y2YwMTdhZGM2OTI5MWYxZDJkYTgyNTQyNTUyNGI2MzMwYTdmZjAwYTZiNWIyZWExMDZkOWE2Mzk5Yjg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-950129860 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:46:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InV4Sk0yaDFRRldUNFFxb1pjNStQT3c9PSIsInZhbHVlIjoiUTlEemYrOWJrcS92QmVLWHVEeXFzVnEweDQ1elZ2eTJlRjdBV3Q3ZWdvNWpRcWdSZ2tzeVZQOEEzNFM1SjB4aTVUNXY0Q0ltNGt4dVF1aHAzRlptdUQxeVozczUxM3dnQ2FJNzVDOVRub09SZ2o3M0pxVnlMY21hV1diTWZ1NVBjQVE0NDJsNmt5WU5TTnJCQXZrNm9jU243ZGJNVFJMZW5Xd1Q4OUhyNTBGRzJUQlZQRlArU2hDYk1hNjJSVnk1WkZlMWQwMTR3eEc2bWxOaU9UbEYxWVVJQlBxSU1qTHRJTnlyMkdiWUVGbE1vaWFoZkJCTTlYN1RKcGtGejNSQkxoOWRPRDlvemI1OFBzWHl5YWVjSHBYUTJDOStvbysvczNoQnpnclJXb3Y3NTJiM0JPaVlKZUQxeTVZOFVuSnZXa0NOSy9nd2Nud2RQR1JSNmljb3pRZ0srOVNBOGtSQjRGR2tTa2N1cGc3UmRBSE9FdGZxRStYTHVxMExDMjVKbTBCVVQvbmJhRlI5K3pyZnJMSmRpKzhjejV1azVVcjFjaEVVbFRvWkFzZXFacHRoMkVLNmRDZ0Z0cXNNWUdITXZlZGdaZzdYWHpUbnEzNkswRUxSVEIvSXd3MHVNTnhieFFqZzF6ckFVMUovbFU2NmtwQXhPS3pIV2ZSZkhBcWIiLCJtYWMiOiIxZGQ2YWYzNGY3ZmNhOTBlODYxODIyZTczZDEwNjYzMWZmMzUxZTI3ODQwNzczMmExM2QxMzBmMzJmMTg1YzYwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:46:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNQUkVTL2k4cmpVQWExK3NwM3JUSXc9PSIsInZhbHVlIjoiWVJGUTFxb3grcG5jYnp4Y1ZUZlMzR0NycnhxUVRYMGkyWmJNYVQ4SW1GeDYzOGZnTlBDdVRJTldLVzZ3TW5YaU05RFo1aXEwbzlGVmVMTXhQWG5SVGZnSGJ2MTkrRnBLeWptQThqSElqdlY2ZGdrSW1wQ2F0TWNONDB2N2Y1Ui9JRjJSR21ZQUpBSlJOa3BOK0dMdVl2dVpRNUpRKzUrU3lxUGMrUkx4aVdaNFRnam42MXFIU1FQV0daUW1uTGlKeTdMQkY4TjBhTGlnUXNKYUhWeUdQRWtFRHpxMzZWb0JWTUN4UW1uNnhvUE5ZNWc3SytrYkE3ZmQyMW5SaDROK1o5bmZDQ08xcTgxMmZCRFlPS3NrbE9PanptY1IrZ0g2VkdLOWJ1c1JxRGlhVXM1cGNTeXZLVlJPQ0l0YTczM0VhSDhOOUFLTnR4K3pLNlFhRGpKQVlWK1hybHpiTEtCUmhYMG9HQUV0T1lBSkgwWlQxMXppQy9VanlKU2ZhOWVyRGNLemI4UjVSbGhZTC9xVnNnTnRkclM3YldSNXJWNUZReGE0cUxXc3F0S2hvTmdkV0t6b3hBdG53amVMSFkwMHhkWDdYR2dCbnpaVE5SWXBURUdhb3UzVkVvczdaYTR3QWl5STdibUNMTjVEWVByVzE3QmJwV2Z4VnU4SnlwT1kiLCJtYWMiOiIwNjM3ODBiMzBlYjNhM2NkOGZkOGE5MmU4NGZjMTkyZGI4ZWFhZjRkMzIwMWIzZjE0ZjUzMGM2MmUwOTEyMWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:46:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InV4Sk0yaDFRRldUNFFxb1pjNStQT3c9PSIsInZhbHVlIjoiUTlEemYrOWJrcS92QmVLWHVEeXFzVnEweDQ1elZ2eTJlRjdBV3Q3ZWdvNWpRcWdSZ2tzeVZQOEEzNFM1SjB4aTVUNXY0Q0ltNGt4dVF1aHAzRlptdUQxeVozczUxM3dnQ2FJNzVDOVRub09SZ2o3M0pxVnlMY21hV1diTWZ1NVBjQVE0NDJsNmt5WU5TTnJCQXZrNm9jU243ZGJNVFJMZW5Xd1Q4OUhyNTBGRzJUQlZQRlArU2hDYk1hNjJSVnk1WkZlMWQwMTR3eEc2bWxOaU9UbEYxWVVJQlBxSU1qTHRJTnlyMkdiWUVGbE1vaWFoZkJCTTlYN1RKcGtGejNSQkxoOWRPRDlvemI1OFBzWHl5YWVjSHBYUTJDOStvbysvczNoQnpnclJXb3Y3NTJiM0JPaVlKZUQxeTVZOFVuSnZXa0NOSy9nd2Nud2RQR1JSNmljb3pRZ0srOVNBOGtSQjRGR2tTa2N1cGc3UmRBSE9FdGZxRStYTHVxMExDMjVKbTBCVVQvbmJhRlI5K3pyZnJMSmRpKzhjejV1azVVcjFjaEVVbFRvWkFzZXFacHRoMkVLNmRDZ0Z0cXNNWUdITXZlZGdaZzdYWHpUbnEzNkswRUxSVEIvSXd3MHVNTnhieFFqZzF6ckFVMUovbFU2NmtwQXhPS3pIV2ZSZkhBcWIiLCJtYWMiOiIxZGQ2YWYzNGY3ZmNhOTBlODYxODIyZTczZDEwNjYzMWZmMzUxZTI3ODQwNzczMmExM2QxMzBmMzJmMTg1YzYwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:46:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNQUkVTL2k4cmpVQWExK3NwM3JUSXc9PSIsInZhbHVlIjoiWVJGUTFxb3grcG5jYnp4Y1ZUZlMzR0NycnhxUVRYMGkyWmJNYVQ4SW1GeDYzOGZnTlBDdVRJTldLVzZ3TW5YaU05RFo1aXEwbzlGVmVMTXhQWG5SVGZnSGJ2MTkrRnBLeWptQThqSElqdlY2ZGdrSW1wQ2F0TWNONDB2N2Y1Ui9JRjJSR21ZQUpBSlJOa3BOK0dMdVl2dVpRNUpRKzUrU3lxUGMrUkx4aVdaNFRnam42MXFIU1FQV0daUW1uTGlKeTdMQkY4TjBhTGlnUXNKYUhWeUdQRWtFRHpxMzZWb0JWTUN4UW1uNnhvUE5ZNWc3SytrYkE3ZmQyMW5SaDROK1o5bmZDQ08xcTgxMmZCRFlPS3NrbE9PanptY1IrZ0g2VkdLOWJ1c1JxRGlhVXM1cGNTeXZLVlJPQ0l0YTczM0VhSDhOOUFLTnR4K3pLNlFhRGpKQVlWK1hybHpiTEtCUmhYMG9HQUV0T1lBSkgwWlQxMXppQy9VanlKU2ZhOWVyRGNLemI4UjVSbGhZTC9xVnNnTnRkclM3YldSNXJWNUZReGE0cUxXc3F0S2hvTmdkV0t6b3hBdG53amVMSFkwMHhkWDdYR2dCbnpaVE5SWXBURUdhb3UzVkVvczdaYTR3QWl5STdibUNMTjVEWVByVzE3QmJwV2Z4VnU4SnlwT1kiLCJtYWMiOiIwNjM3ODBiMzBlYjNhM2NkOGZkOGE5MmU4NGZjMTkyZGI4ZWFhZjRkMzIwMWIzZjE0ZjUzMGM2MmUwOTEyMWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:46:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950129860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1647122772 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647122772\", {\"maxDepth\":0})</script>\n"}}