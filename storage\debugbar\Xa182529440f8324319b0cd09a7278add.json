{"__meta": {"id": "Xa182529440f8324319b0cd09a7278add", "datetime": "2025-07-31 15:52:14", "utime": **********.56867, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977132.95642, "end": **********.568733, "duration": 1.6123130321502686, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1753977132.95642, "relative_start": 0, "end": **********.46152, "relative_end": **********.46152, "duration": 1.5051000118255615, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.461544, "relative_start": 1.****************, "end": **********.568737, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R7ZcUcj3cVqG1K7OnmKABetkidWUGWO2We7atiQP", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-721620719 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-721620719\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1397811741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1397811741\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1725975925 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725975925\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1691795761 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691795761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-882699043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882699043\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-871157162 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpVYzIvTDZvcW1qM0JNdjBlVDcyWkE9PSIsInZhbHVlIjoiTXJURDMzYXpjbFkwcTQ4YXZocUdTalluYlJQdCtDcGVOREJDcnBJdFliYzRwMWF4YWVxaHorN2xoOGEzN2d6U2czNFVseGFEbmozVlVzdld4MzB4Y1Jsd2s0d0RVdlJrRG1kVlJtK3Qxa0FucmxET1VDOGdoL3VNU3NMZjUyVDZiUWlZMEhLZG5td0tQWE5xNXhkU254QWcrTDdRZ2NDczcxRmNoYlJlT0ttYmdPM2o1dHp6eHR0Rm1nMUd4TFZFaEtFenNMTlI0RTRTMTVqSFZxVVlMTVJJRGw3eDcrN0hpUTcxQVZyNjUvdEtoMUZaUTFnR2pYSlRDeFlaNzl2MXg2bTJTcnNxVWZHdFJrV3o3UUp0LzBIYU84Z0w2eFNoTVp3SmtMRmtBR0pKRGNpZkE4RXBXQnkvR0owR3lVQkM4ZnQrU0ozQWtKL3dvd2J1STNQMmQzNnVVSjJhTllQdVdJdzNkdUZEZHVVdTF0d1MvN1hHT05vcWVlRGhnb2hjdE1kVkVFYXNzV2ZYbit6T3pnOHgvVDJBc3ZpUGZzMkJnaEV6em1zdExjeDNpdFZzZW1iVDJ4VUUwM2hWUC9yV0RaUkV2V3RCZzlqWnhGTy9HSXlIeXpEZ3RRVkNRT0xaMnU5YWlnbHdDSFVoemJaVkdvRis1NzRodTQyUWMrRVQiLCJtYWMiOiJmNWM3NDc2ZDczZmQyZGNmNzNjOTc4YWY0MjNlNjhjMDFjMWFmMzBjMWNlNGU2ZDU0MTI5NGJmNjhiNzllOTA0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9UcHNyWVVCMTgyenNhN09adjZ3Rmc9PSIsInZhbHVlIjoiMDdHdko2M3NxZW5zR0FBS0llRlVNbGIwODhIenMvT21QaGZXSlJVM0VSZW45SWl2NHZxaTNsWlEvS1hOMGJRbHhhcVF2aCs1VXN2bzdZemlRaTBISHdRYVA4THBYbnczYWI4YzNwYm9ObWh1NTFFaU1VcFpDNGNYL1E3SDRGRnpZWEdqTDN3UFI0THZROGxibnR2M1JnajIxTmVwQ2E0ODJienlEQUNIQ2sxR2JlME1BWExMVGhJQ1YzTmxOdU1WazhlUHlDTDIxZHA1bkIrc2MyaGp4VVNwdzhPenFWckpQRFNtQjZ0dXdYMjZFUklqdHBrdGcwUmwvbHU4NTd5WW9GaTh4U0J5c04vdWUvb1VxcEREanBrN0hZdTlUSkZ6cU5Pdk9PWUhtVk10VDZhSlR3R2JGUHdBcDl4THlaNWRsbHY2SENJNlZXeGpSMjVNUVZyUVIyQ3NMallXK3gvN1h0NUJhc1E2bVRpTzRKKzFkcFU2eXZoYWpiZmxNUS9ud2M0SGhnTXQyZHdVM0JmeDI4TVVTUHpUVUVkeTYzcGNyNVFMZmFNQk1IV0c5UEY5TWlERmtHMjJtcnRoWFZ5eGZlNXhrZjJWelZMeEN4Snl3ZS9LMEN5dXp0aTlwTjFjeVVkVEF2SkpHeCtabFJBaFhXR2xIbU9WR2h6WDR1ZzIiLCJtYWMiOiI3MjliZTI2MTI0MzFiNTM4MmY5ZmVmN2M1OTYzYjIwZjg2OGQ2N2FiYTE1MDNiNGUzODEwNjdmODY0ZjZlMDU5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpVYzIvTDZvcW1qM0JNdjBlVDcyWkE9PSIsInZhbHVlIjoiTXJURDMzYXpjbFkwcTQ4YXZocUdTalluYlJQdCtDcGVOREJDcnBJdFliYzRwMWF4YWVxaHorN2xoOGEzN2d6U2czNFVseGFEbmozVlVzdld4MzB4Y1Jsd2s0d0RVdlJrRG1kVlJtK3Qxa0FucmxET1VDOGdoL3VNU3NMZjUyVDZiUWlZMEhLZG5td0tQWE5xNXhkU254QWcrTDdRZ2NDczcxRmNoYlJlT0ttYmdPM2o1dHp6eHR0Rm1nMUd4TFZFaEtFenNMTlI0RTRTMTVqSFZxVVlMTVJJRGw3eDcrN0hpUTcxQVZyNjUvdEtoMUZaUTFnR2pYSlRDeFlaNzl2MXg2bTJTcnNxVWZHdFJrV3o3UUp0LzBIYU84Z0w2eFNoTVp3SmtMRmtBR0pKRGNpZkE4RXBXQnkvR0owR3lVQkM4ZnQrU0ozQWtKL3dvd2J1STNQMmQzNnVVSjJhTllQdVdJdzNkdUZEZHVVdTF0d1MvN1hHT05vcWVlRGhnb2hjdE1kVkVFYXNzV2ZYbit6T3pnOHgvVDJBc3ZpUGZzMkJnaEV6em1zdExjeDNpdFZzZW1iVDJ4VUUwM2hWUC9yV0RaUkV2V3RCZzlqWnhGTy9HSXlIeXpEZ3RRVkNRT0xaMnU5YWlnbHdDSFVoemJaVkdvRis1NzRodTQyUWMrRVQiLCJtYWMiOiJmNWM3NDc2ZDczZmQyZGNmNzNjOTc4YWY0MjNlNjhjMDFjMWFmMzBjMWNlNGU2ZDU0MTI5NGJmNjhiNzllOTA0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9UcHNyWVVCMTgyenNhN09adjZ3Rmc9PSIsInZhbHVlIjoiMDdHdko2M3NxZW5zR0FBS0llRlVNbGIwODhIenMvT21QaGZXSlJVM0VSZW45SWl2NHZxaTNsWlEvS1hOMGJRbHhhcVF2aCs1VXN2bzdZemlRaTBISHdRYVA4THBYbnczYWI4YzNwYm9ObWh1NTFFaU1VcFpDNGNYL1E3SDRGRnpZWEdqTDN3UFI0THZROGxibnR2M1JnajIxTmVwQ2E0ODJienlEQUNIQ2sxR2JlME1BWExMVGhJQ1YzTmxOdU1WazhlUHlDTDIxZHA1bkIrc2MyaGp4VVNwdzhPenFWckpQRFNtQjZ0dXdYMjZFUklqdHBrdGcwUmwvbHU4NTd5WW9GaTh4U0J5c04vdWUvb1VxcEREanBrN0hZdTlUSkZ6cU5Pdk9PWUhtVk10VDZhSlR3R2JGUHdBcDl4THlaNWRsbHY2SENJNlZXeGpSMjVNUVZyUVIyQ3NMallXK3gvN1h0NUJhc1E2bVRpTzRKKzFkcFU2eXZoYWpiZmxNUS9ud2M0SGhnTXQyZHdVM0JmeDI4TVVTUHpUVUVkeTYzcGNyNVFMZmFNQk1IV0c5UEY5TWlERmtHMjJtcnRoWFZ5eGZlNXhrZjJWelZMeEN4Snl3ZS9LMEN5dXp0aTlwTjFjeVVkVEF2SkpHeCtabFJBaFhXR2xIbU9WR2h6WDR1ZzIiLCJtYWMiOiI3MjliZTI2MTI0MzFiNTM4MmY5ZmVmN2M1OTYzYjIwZjg2OGQ2N2FiYTE1MDNiNGUzODEwNjdmODY0ZjZlMDU5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871157162\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-187599287 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R7ZcUcj3cVqG1K7OnmKABetkidWUGWO2We7atiQP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187599287\", {\"maxDepth\":0})</script>\n"}}