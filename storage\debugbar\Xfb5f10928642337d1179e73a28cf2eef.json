{"__meta": {"id": "Xfb5f10928642337d1179e73a28cf2eef", "datetime": "2025-07-31 16:52:54", "utime": **********.332462, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980772.21721, "end": **********.332577, "duration": 2.1153669357299805, "duration_str": "2.12s", "measures": [{"label": "Booting", "start": 1753980772.21721, "relative_start": 0, "end": 1753980773.974853, "relative_end": 1753980773.974853, "duration": 1.7576429843902588, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753980773.97488, "relative_start": 1.7576699256896973, "end": **********.332585, "relative_end": 8.106231689453125e-06, "duration": 0.35770511627197266, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47018080, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01786, "accumulated_duration_str": "17.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.187146, "duration": 0.008230000000000001, "duration_str": "8.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 46.081}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.242826, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 46.081, "width_percent": 13.718}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.266665, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 59.798, "width_percent": 14.614}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.286714, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 74.412, "width_percent": 25.588}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1106088970 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1106088970\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-531430408 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-531430408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-424381373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-424381373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-690567399 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im4wVitoU3hHVXFia1hpZ0ZGUGU5U1E9PSIsInZhbHVlIjoiY3JvUjdSOWYzdTc3bXpTcnpzdnMycTBNandzUmhscEI0Qms5R09kM0RXZ0Z5WjN5cndmTVR6d0R4ZWpSSkxhZld4NmxlK3pWZlJyZjRCM2h3eXdSTDlaMFR6ejEvSlhMVWk5MFlQWWh6bjlNczJ1TUJXS0t4akNyS1p1ZVh6ak9Xd20rbkNKS09uYUxuYVFudHorTDNOTUtuN0RIcFp3OUsyQ0pHcDNKMlpSL3Z0SC9Ca21hWW5VeHFuUjBOalBYN0ZqSmkzaW4yYmNWcEE3NGF5Z3poc0JZc2wyVXpBYW9rV2VOMEZ2UUt4R29TRTluK0R0cGlEUkxtUnZ4aE9YM1RWOTFqUHdvaVVPK0RjT3lIY2JXTERjME9MdnJ3cTFwYkVFZE9RcXVUZTBMTHg5dkJWUjNGSlBUOHhNNG16WnlkR1NKbWdhLzB1RURLV3FGdDN5dUNmcXd0TUp2aVFGbzNUU1h4dG9aditYNHE3ZXNNbktzZzRQdVFTOWlQSHRRamswUkNzbEkwNkpZS1lpL3BXakVCMlpkdjFGNkFURVhtdjNHQUZUVUYrTk16cm83czFaTXhseWpWdHdobEkyVFhJYXk1Mzl4NFF4SHZINjNMZXdzU2FSc0cvbHNkdXVuWko1Qm1CdmhaQjJXZ0d4aVREZ252VER3NU14dU5BMXkiLCJtYWMiOiIyMGY1YTJlODc2MWYzNzY2M2I2MzJmYzQyNGQwOGJmYWJjNjdkZjhmOThhNDVmZjEzYmE4YTFlNzA5M2EyYTBjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlZlWFdVNEJSOHMyZlZ1a2VJL0ZVYnc9PSIsInZhbHVlIjoiWEo2N25PQ0hjdEp1Wk5RVEpoWlFYK0x5SHhlWkxseXJ4eXBCeStDb0lyUE9tMnU1N0RIQ1IvcDdRT2E4M2tqWXRSWnhhVHR4ZDlXUXNpODQwYlhEa3JUZG9RL1hpTHlJdXdkWnVuWndKbmtUK0YyeStabTB5bVdDYVVmcnl0RXVhbTVHYVV6b0grRVRzalBzMmxoTHF5Zm9ZMGtZT1JDaU1tZHFiRERwOVR3bWFJQ21LcUdaeERxRnQwRjV0UG5TdFVNN2RaclFaY3VOOEEvWFljRzBicTd6WFNYYU9VeEE0M2JFOUdpaGRiQW1CVmZ0aVlPVEduMGFESTh3SjhBNnhabkphMlBVMEZONEZBZ1BjVHpvRG5mOVhIb2lDZU5iZ2pTaC9PQUpTaENZNkhyZzIzMU5PUUJ5M3BGTlVUVkVyaVVqL1lFUktpRXlBYWJYV1hncXNYVEQ4N3VPQVBaRWVJbTdWbk5GVGQ1VlJuZXBPRGZVMGU5SWlBalFYbDIwTGFrSk9hNE9KNG5xU3oyd3pZY2g5MiswNXJOSnB2YUltRjI4MnhXZjluaWQzcmNYaHNsbjlJS3RBUTVaZldEcnFwSWF3MmJDNmZhSWZxYmpGKzZoZUpoVWhGUnNLUk4ycDdTYU5LbUVmbmJoaStKVG9YTEN3aGs4ckJSQVJ6R1AiLCJtYWMiOiJhYzI2M2E2N2M4ZmQxZDlmMzhmNGEwZmYzMDNmZWMxZjIyNTUyNDQ0YmQ2ZjA1OWE3NmIzZDk0YjFjMDRiMzAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690567399\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-127657247 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127657247\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-116386381 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:52:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJsZFpqS0RIZGFlSWVFby9ESENBNmc9PSIsInZhbHVlIjoialRYM2N1UE10UnVlNkhqT1k1RnpFZzJRQUhjVm1kZGw0dk1jdzR4b0t6Z3dWTE1vMmtVRVBPek5wYUxVZ2lhS2pYYUlpRmZLWDdGU2F0UWVnMnNKSUFvRThTTW1SZ2xVYnNINExMazNZd2d1L05ZMm85NnFjcXFURzZhdDQ5MndYcGFBeWRZYzVEUHlqT25FSjQ3ODNXVGNhNHRyYUtrZGpIL1hsZlJDY3FmSGVGRzQ4MUoyUWs4SERRWkJ4eGhrcXNTbkdVU0RRY0tsdU1Wb3R0SGxkY3lib0VpRHgvY0VLaTRUaHFzbU9NS1R0YVZXdnZkK0pIUkNCT2V5TTNTNEViQUdvQzVsdkFEUXhtc1NMejlwTjdadkdIWTBjbGF1RnVDa2hqVHFaMnYxT0tqREYyR0k1SUlpbmtMUGNnN3lJcUUzK3U3bHoyNklPajFRdG85Z25MN1dsdmxHdUt0TXdBeitQOWgxSkJaSTUwMVNoVjNpaDJSWk9Zd25EZXVrRHFyMUFjR05uM1JQN0M2Wk9PRmlLTkRlUWZHVFB2R3hHTFFkaFhURkVZV1gva1pDd1lBelJuSExMUTV5bVQ0NmNGNTk0TjI3VGFBU1kwaS81K2tBQmNRVTdsRHkvcnZncHRsbCttVGtrUmdWSTI3ZWVpVjBJUVlSR2FVT3FPQWUiLCJtYWMiOiJjMTNlYjA4NjVlZGIxYmJjZDg5NDliODlmOTg5M2YyMjk0N2U5ZGE3ZjBlYTE0ODJkMTU0MDQ5ZTliOWIxYTczIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:52:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IldESjB4aER4eHN2VWdBNlo1K1R4dUE9PSIsInZhbHVlIjoibUxTTDRPQ2NkZVd3dldleFNEUlBZZG9UdHcrM3Z5bkNLaVZsckdDVEdMSjFpSmVDRFZ6R3JpOUtlQW8reFA4c2puZ1ZUcVh1SVc4WGU1d2lFSWw3NFlGYVF6UnhqamF2UHR6Ti9YVFdubGVQU2pFRzZLYXcrS0ZYazQwbUVPQnRkd3pMaEZiYWFDRExnTFR4TkpRa0NmSXB5ek9oWnRzM1g2UEZQZW9LNFo1MVMybm5VMFZkb0lkc0RCcWhyY2tLekJ3blY4UFBMQUVIZFF6YWszc2VZMFZkdU9HMDBKQ0xWQjBpVVlOVzRkMVRmUkRXbE5QUDdiNi9nYk83VXFJa2hUV0V2UUV6V3RVVVk0b0dlZ0cyK05USkRMdUtPM3FkWVY5TEh3Zi9zLytUeGRQaGV3NTFZanVrQ0tKY3hDQVluS2F3Z2o5UnZzcUdzdGp1a2RIWGliK2h3SStMbU4xYUM5Y3gyWHBweU9DdkJSQzJXRFVHOWp0N3UvTTIyb3JzY3FISEI0MUJ3SFRJRDFOY1paMndnZnVjc1FYR0N2NkVZbmVtNjZTV08rZ3d6Slp3K2ZSbEdYTnlLc1h2MWNHMFl4d1hnMGR5S0hQdnZRMU1wWk85bTF4ZzBHRHdXemZUZHdwbXlWZjVCL0VSN0JxUm82WkU2ODFJYnBqNEl2UUEiLCJtYWMiOiJlMzUwNmQ4YjlmZjVhYzlhMTQwNTMyYzM2ZmE4MTM0OTRkNDlkNGZiZDI5YzA1MTgyZGY0NDNiOWRhNTAyZmJiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:52:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJsZFpqS0RIZGFlSWVFby9ESENBNmc9PSIsInZhbHVlIjoialRYM2N1UE10UnVlNkhqT1k1RnpFZzJRQUhjVm1kZGw0dk1jdzR4b0t6Z3dWTE1vMmtVRVBPek5wYUxVZ2lhS2pYYUlpRmZLWDdGU2F0UWVnMnNKSUFvRThTTW1SZ2xVYnNINExMazNZd2d1L05ZMm85NnFjcXFURzZhdDQ5MndYcGFBeWRZYzVEUHlqT25FSjQ3ODNXVGNhNHRyYUtrZGpIL1hsZlJDY3FmSGVGRzQ4MUoyUWs4SERRWkJ4eGhrcXNTbkdVU0RRY0tsdU1Wb3R0SGxkY3lib0VpRHgvY0VLaTRUaHFzbU9NS1R0YVZXdnZkK0pIUkNCT2V5TTNTNEViQUdvQzVsdkFEUXhtc1NMejlwTjdadkdIWTBjbGF1RnVDa2hqVHFaMnYxT0tqREYyR0k1SUlpbmtMUGNnN3lJcUUzK3U3bHoyNklPajFRdG85Z25MN1dsdmxHdUt0TXdBeitQOWgxSkJaSTUwMVNoVjNpaDJSWk9Zd25EZXVrRHFyMUFjR05uM1JQN0M2Wk9PRmlLTkRlUWZHVFB2R3hHTFFkaFhURkVZV1gva1pDd1lBelJuSExMUTV5bVQ0NmNGNTk0TjI3VGFBU1kwaS81K2tBQmNRVTdsRHkvcnZncHRsbCttVGtrUmdWSTI3ZWVpVjBJUVlSR2FVT3FPQWUiLCJtYWMiOiJjMTNlYjA4NjVlZGIxYmJjZDg5NDliODlmOTg5M2YyMjk0N2U5ZGE3ZjBlYTE0ODJkMTU0MDQ5ZTliOWIxYTczIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:52:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IldESjB4aER4eHN2VWdBNlo1K1R4dUE9PSIsInZhbHVlIjoibUxTTDRPQ2NkZVd3dldleFNEUlBZZG9UdHcrM3Z5bkNLaVZsckdDVEdMSjFpSmVDRFZ6R3JpOUtlQW8reFA4c2puZ1ZUcVh1SVc4WGU1d2lFSWw3NFlGYVF6UnhqamF2UHR6Ti9YVFdubGVQU2pFRzZLYXcrS0ZYazQwbUVPQnRkd3pMaEZiYWFDRExnTFR4TkpRa0NmSXB5ek9oWnRzM1g2UEZQZW9LNFo1MVMybm5VMFZkb0lkc0RCcWhyY2tLekJ3blY4UFBMQUVIZFF6YWszc2VZMFZkdU9HMDBKQ0xWQjBpVVlOVzRkMVRmUkRXbE5QUDdiNi9nYk83VXFJa2hUV0V2UUV6V3RVVVk0b0dlZ0cyK05USkRMdUtPM3FkWVY5TEh3Zi9zLytUeGRQaGV3NTFZanVrQ0tKY3hDQVluS2F3Z2o5UnZzcUdzdGp1a2RIWGliK2h3SStMbU4xYUM5Y3gyWHBweU9DdkJSQzJXRFVHOWp0N3UvTTIyb3JzY3FISEI0MUJ3SFRJRDFOY1paMndnZnVjc1FYR0N2NkVZbmVtNjZTV08rZ3d6Slp3K2ZSbEdYTnlLc1h2MWNHMFl4d1hnMGR5S0hQdnZRMU1wWk85bTF4ZzBHRHdXemZUZHdwbXlWZjVCL0VSN0JxUm82WkU2ODFJYnBqNEl2UUEiLCJtYWMiOiJlMzUwNmQ4YjlmZjVhYzlhMTQwNTMyYzM2ZmE4MTM0OTRkNDlkNGZiZDI5YzA1MTgyZGY0NDNiOWRhNTAyZmJiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:52:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116386381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1279497369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279497369\", {\"maxDepth\":0})</script>\n"}}