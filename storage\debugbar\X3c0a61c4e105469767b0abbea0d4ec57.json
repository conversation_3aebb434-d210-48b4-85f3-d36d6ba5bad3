{"__meta": {"id": "X3c0a61c4e105469767b0abbea0d4ec57", "datetime": "2025-07-31 16:23:05", "utime": **********.712978, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978984.245149, "end": **********.713013, "duration": 1.4678640365600586, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1753978984.245149, "relative_start": 0, "end": **********.481863, "relative_end": **********.481863, "duration": 1.2367141246795654, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.481897, "relative_start": 1.236748218536377, "end": **********.713017, "relative_end": 4.0531158447265625e-06, "duration": 0.23111987113952637, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01287, "accumulated_duration_str": "12.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.616281, "duration": 0.00889, "duration_str": "8.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 69.075}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.667283, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 69.075, "width_percent": 13.442}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.680853, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 82.517, "width_percent": 8.702}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.689858, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 91.22, "width_percent": 8.78}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1845951011 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1845951011\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1148707532 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148707532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-206443228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-206443228\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1828955585 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlR0SVpFczdjdHFOTGNqZXhWai9IdEE9PSIsInZhbHVlIjoibWcxTEpNK2tlZCtaVzdhTjNEV1grWnp2d1h6Y3NuY3NOU21BZE1IdHJnZTVYV2tDWXFzL1Y1MkhzeDNNekoxTEp1UytkMWdPMVE0SUF5Q3QwUXM0TFp4SndsSmY3N0YrTDZBQ2hQd1hmc3JFNzd1YTRVOVhQbmlZelN6WGwrd2JGcGNQTks1MldhejZvR1JZOFdaWENhWU9uOVBEdFhYMGZoOHZaWVJ5MDcydlNUSkpnSit6U3VEanRoczBiQ2R1SlJIdnVVRk8yMklpL2ZRTTk2YkN3eW82ZnNyeFF3MjF3RG5DaHIyTHFWMTFGeEtJYW14akJRcGpoa1RQTkhQdllTMlBWTU9HSitHOENweWl4SDQ1Nll3aWZUVzJtRDIvSUtNVldRbW1PbnVPS25KZlF4L3F5b1ROVk9BNmg0NUxIRzdXendPVnpwR1ovRyt5SmlOTmszaURpeTBIOWFFeVdWanN5WjRieTRGRDRmSnlySHhCS1psZ0dMaDZVQ3NkWlpTVHVtbkk0UUlPMkdzS2JMN0J6ZnRiWWp1cWkrczBMU1daUVA1Ni9lSGc1c3Q1OVk5R1d3R25hYVZlUXc4SHUwVDhHVEpBUTlTbEJZVC9wOFVCa0RrQlVPUDhkbzdwQS8wSE5qMnZMR0RpWFY2bUxyVzVGTGc3VUFSaUN0c2QiLCJtYWMiOiJjOGI1MGE2NjVhMzEyM2FjYTU5MTgxODkzNGQ0ODk3OGE4Zjk3MGIwYTNhZDI3MjM5MWQ1ZDRmNWZiMmY4MDU2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik9rSkdWcEJRaGpuOFVMS0dWSTlqUGc9PSIsInZhbHVlIjoiSEFtOWpFdVpoOXpJQ1Z6UElGSHZVTFdCRmtSYWNzV1I0WlJON09mVlZFanlCWlNzVm9vSUlKUGd4ZDRma3RvSmFXUUs5cEUyS2orRXRwdHBuMi8veDV5bmlmOVdzOW1vNlBCcUJQUGdVUmpHUHZuVlc0anU5L05OYkhkQzJRTmtuaWI2MHV6aFhiZVJTSEdlSjFDZWdyamhJV0tMaDZ2dkQ0L3hBcE43ZENpenNBTzlza0NaTXhtQ2tPR3NpSENUV0xlT3RSckcyamVXUkNtWXErSndwWG9jWVRPTlpCUUJPcllxOFB3eG5YNThyVEdjMkdlVGJndUZHeFdUMEVleTVXNkVqVDc5dUhsemd4dGhKRTJoU0JFdlIvQ2lFeDBzZnB4akNnanRoZ293QnhKUjNFemJPRXNZbVF4U0NsU0FrY0ZCMEJaRmlWZmp3TmZvOFBWa2VIbm1qaDl1VVlaZUsrUHB6WWdlakJWTk1JUnU4S2RZS0RpSHVUb2xJUUJsOExOQ1ZvUkNIWFc0cHlwNkhsanVEQStUOVZiMjhQazM2QWxVcSt5TDFzQllyYmNnNitSSmFQT1pwRk82em1GTVYxUFREREl0c0puUlJSdkZ0aERGaUdoelFiSHFweXJNL29OcG9LSFY4Yndwbi9LRFgxMG9FKzRpZzNSdVBqbXoiLCJtYWMiOiI5ZTE4ZWI4N2Q1ZTQzNjdhYWEzNTBhNGRiNTYzYjY5MjI4NDJiNjhhM2I5Y2NjMjhjZGVkNDhjY2Y1MTQ3OTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828955585\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1423600316 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423600316\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-261964831 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:23:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRDWEtZMHA4T0RHTElnOFR6S0VvN0E9PSIsInZhbHVlIjoiRW5qWG80d0g1RmJMdGR0d2lYTzhLenM0bTZaS25TQTA0cGhrNWdEeTFOTFpKNDVvZmdIT3JaNXArbXBZV1l0UllPTnZDbGlvTU81NmVmMFZYZnV2N203YjN5bXhPcy9HUklpRUlzWHFQcW5tTEwyQWJBS3hEa1NvMmZjQUdmSGNMNW56VWQzb3FCNFNOcWhtNWNzTFF5d2pKUWFuMHdrVTBYYnloNEppbkxBUy9VYTcrWkszN0tPR1FBbWxqaWFtcEtvT2o0R3RibW8zYjd4STVHcTIvQnJyazR2RS96RVpFY0ZtYVQzUWQ2Vk9CcGdLOUpBamdTT01SYWtHNzdiRDNsZFZzZzd0dmxBWS9kV1d0Zkc4RFNvcmVtUTd4NTQzbEplVVdBRG16UU9JQ0Y3aEJERVpsWmZXSzRIalZsd0ZUdmNISWtNaTBDRVI5bnRkWEFTaERjanp6THRCN3NwMHlRZjNLOEtxWDgwb1dGZjk5eStERllURWZYenBxQ2k5QXBsSVlab2pmNm1vc3cyQSt2UWZsN1J1dDFQRStyaWdNNG16Vmh4NnV5N3NYMG5EeDdyTE1jeUJxOVFtdDBlU1RVZlMvTGVmdmVwYVdhNUJzbzJUUFg1eE5LcmJEWjEyYVhkSk1xZ01xUHFoTmVyQ3lmcXk5dnRrVXI0L1N3WVkiLCJtYWMiOiI0NmNiZjVjNTc2NTAyOGRmNGFhNTE3ODhkMDFlMzI2YTc4NzdmNmY2ODdiMTM0YWFlM2NkODZhZWM2OTMxODNiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBrZVVXVUdMNEdOUXJhYzRWVXFsQmc9PSIsInZhbHVlIjoiS2gzakN2SHExWE9aT0ZZU21SbDZsZUdONGl0RTk5WU11UjdMK1R5YUZ4Wm8vblhkaTFTR2lNNXhJNDUrSHlnUVRvMnRSV2xvcDlCUit2RzhCZm9oaU1iQXJMVXB4V0JLYndkL3ZkMWREd1pYRi8wQ2dpU3RpaXVPSWRyVWhnY3ZJQ3hSdjdHeXBadG9xTXQzMUw5MXRldFRNUU41YW9yWGEzZlJoRjJFdHVrVUZta25LR2NNbjh4L254ZGcwaHhmOTYwM1lQMC9mVnZkaHFEYUIva1YxU0hmUXF1c0ZjNEZ0aVdjeE1qaEt4YWl4Znl2ajJnMDNINEkvLys0VWRaUVc4ZEI3MHJlZHM5MEFRbHZ4UmhRazBPS0hpeEpJb1poNm8xa1JTeTNFT1hFOGk3SUxXSVY3Mkt4T3pGSXBtRHhMbnlkZFh1ejIxV1VyemdqZXpqNThNZmYrRDNXUDNTbmh3ZThkZWM0czE0bjVKT0hWRHNVZENkMTlNK1pPWC80QjIzbHFxaERYZ09kK28rYWxyT2cyY3lnd3gxa25BenUzalJIYTlVZGhGZm8zWDdoOUtvT3RPUkgxUHV6dTZ1blVKdVBseHZQay80REN2czRaTzU0aW1qNEJDZzhJakRURTgvb0dWd3k0cDZDdVpiV000RVVFWFRZU3VSZU43Q2EiLCJtYWMiOiI3ZWU0NTRhMmU4Yjc4ZGIwN2ZlNzk5MjJkODdjOTI2NjBkYzdlNjRjMDk0NGYwMGI5OWVkYjk1ZjcwZDNiMGU1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRDWEtZMHA4T0RHTElnOFR6S0VvN0E9PSIsInZhbHVlIjoiRW5qWG80d0g1RmJMdGR0d2lYTzhLenM0bTZaS25TQTA0cGhrNWdEeTFOTFpKNDVvZmdIT3JaNXArbXBZV1l0UllPTnZDbGlvTU81NmVmMFZYZnV2N203YjN5bXhPcy9HUklpRUlzWHFQcW5tTEwyQWJBS3hEa1NvMmZjQUdmSGNMNW56VWQzb3FCNFNOcWhtNWNzTFF5d2pKUWFuMHdrVTBYYnloNEppbkxBUy9VYTcrWkszN0tPR1FBbWxqaWFtcEtvT2o0R3RibW8zYjd4STVHcTIvQnJyazR2RS96RVpFY0ZtYVQzUWQ2Vk9CcGdLOUpBamdTT01SYWtHNzdiRDNsZFZzZzd0dmxBWS9kV1d0Zkc4RFNvcmVtUTd4NTQzbEplVVdBRG16UU9JQ0Y3aEJERVpsWmZXSzRIalZsd0ZUdmNISWtNaTBDRVI5bnRkWEFTaERjanp6THRCN3NwMHlRZjNLOEtxWDgwb1dGZjk5eStERllURWZYenBxQ2k5QXBsSVlab2pmNm1vc3cyQSt2UWZsN1J1dDFQRStyaWdNNG16Vmh4NnV5N3NYMG5EeDdyTE1jeUJxOVFtdDBlU1RVZlMvTGVmdmVwYVdhNUJzbzJUUFg1eE5LcmJEWjEyYVhkSk1xZ01xUHFoTmVyQ3lmcXk5dnRrVXI0L1N3WVkiLCJtYWMiOiI0NmNiZjVjNTc2NTAyOGRmNGFhNTE3ODhkMDFlMzI2YTc4NzdmNmY2ODdiMTM0YWFlM2NkODZhZWM2OTMxODNiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBrZVVXVUdMNEdOUXJhYzRWVXFsQmc9PSIsInZhbHVlIjoiS2gzakN2SHExWE9aT0ZZU21SbDZsZUdONGl0RTk5WU11UjdMK1R5YUZ4Wm8vblhkaTFTR2lNNXhJNDUrSHlnUVRvMnRSV2xvcDlCUit2RzhCZm9oaU1iQXJMVXB4V0JLYndkL3ZkMWREd1pYRi8wQ2dpU3RpaXVPSWRyVWhnY3ZJQ3hSdjdHeXBadG9xTXQzMUw5MXRldFRNUU41YW9yWGEzZlJoRjJFdHVrVUZta25LR2NNbjh4L254ZGcwaHhmOTYwM1lQMC9mVnZkaHFEYUIva1YxU0hmUXF1c0ZjNEZ0aVdjeE1qaEt4YWl4Znl2ajJnMDNINEkvLys0VWRaUVc4ZEI3MHJlZHM5MEFRbHZ4UmhRazBPS0hpeEpJb1poNm8xa1JTeTNFT1hFOGk3SUxXSVY3Mkt4T3pGSXBtRHhMbnlkZFh1ejIxV1VyemdqZXpqNThNZmYrRDNXUDNTbmh3ZThkZWM0czE0bjVKT0hWRHNVZENkMTlNK1pPWC80QjIzbHFxaERYZ09kK28rYWxyT2cyY3lnd3gxa25BenUzalJIYTlVZGhGZm8zWDdoOUtvT3RPUkgxUHV6dTZ1blVKdVBseHZQay80REN2czRaTzU0aW1qNEJDZzhJakRURTgvb0dWd3k0cDZDdVpiV000RVVFWFRZU3VSZU43Q2EiLCJtYWMiOiI3ZWU0NTRhMmU4Yjc4ZGIwN2ZlNzk5MjJkODdjOTI2NjBkYzdlNjRjMDk0NGYwMGI5OWVkYjk1ZjcwZDNiMGU1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261964831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-274426922 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274426922\", {\"maxDepth\":0})</script>\n"}}