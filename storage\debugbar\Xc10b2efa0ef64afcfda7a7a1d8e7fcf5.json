{"__meta": {"id": "Xc10b2efa0ef64afcfda7a7a1d8e7fcf5", "datetime": "2025-07-31 17:11:39", "utime": **********.52676, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981896.563146, "end": **********.526819, "duration": 2.9636728763580322, "duration_str": "2.96s", "measures": [{"label": "Booting", "start": 1753981896.563146, "relative_start": 0, "end": 1753981898.92855, "relative_end": 1753981898.92855, "duration": 2.365403890609741, "duration_str": "2.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753981898.928608, "relative_start": 2.365461826324463, "end": **********.526824, "relative_end": 5.0067901611328125e-06, "duration": 0.5982160568237305, "duration_str": "598ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02992, "accumulated_duration_str": "29.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.304784, "duration": 0.02565, "duration_str": "25.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.729}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.403887, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.729, "width_percent": 7.052}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.432514, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 92.781, "width_percent": 7.219}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1472173563 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1472173563\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1004447415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1004447415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1292250637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1292250637\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1507376769 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IncreXQwaXp1MXdrQ0RyTkVOL3UrVGc9PSIsInZhbHVlIjoiYmFqRVVnd0svQU1jRWxqZ2FsbE90ZW1TbGNxMDZsSVhnNGwzSEN0eTRiQXdHalJRVHdwbDYwNmcyR2VhYUt3Y2c5YnJCZ1VoMk05UVZZNGJMa09VYjJGeHRPb281bzJvS2swclNlY29vMytYM2pzcjdjb2owUFpqWmlyQlBTOXE3YzkrTzhGR3B0UmJvemhVUmFha3JoRmtEbGhIbVdEazRyZU5wNC9wZHRHT1pKaXVhMFNGcm82U21UZEczalQ3Tm90NUZlUW42WFFjSnR6UXdIREZEUDhKaVpHYmpSTVV6aDgxTGZLY09DdEpoYTBYc0hWK21qV1IrbU1wTTY2QVpUMUpIVXpFcmtLdEwvS1phd25WTHZXTFMvMVRJZ1lGTUk3N0FVSEtyeS8wejdBT1JvZzRQS1pnK2JLVnp3TlFRNnlrQlJlOU0rdnUwWUNMQTVzWG9IV2J6Ny9Cbzg1ZXZSZXdvblFRS3pXMHNJdllXZFpZalJZUG5zVE9qcUdvTEdURXNGUGhIVzhaYzJDU2ptd0hlRHh0OHdXVUpmdlhmWndQUjh3eGw5dW11TjZPKzBRbU9ENEs1QWdoTkN2TzVnTVBYVnZFa3ZHOGw4bHlBb1d1RzA0MFRoeHNYR3ZRdFFjQ01mUnVGYnBqL2FVcHRGZE1LZWdzVHBld2ppUnQiLCJtYWMiOiJhZmNkMTY0NDQzNGJmM2RiNDQwODkwZWJjYTFkZDg0MTg0YjMzMDJhYzk2MWQyNmIwZjZlNTBkNzQ1OTQ2YzQyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjlMUlJGTUIrakhvQlB2KzNtMUFsT1E9PSIsInZhbHVlIjoiaTBoeVlpa2JFYUM0NWp0ZG13WFNxdTVFcUF0STNra0kya2Zhay8yRW83MXVReEFRYW56UWhLQkZZeVhqMUlnT2cyaVBnbnY4ZVhZRlJiempJb0FZYmljL041bU9CN0luRzI1bTVmM3JIRTdETmM4QmJLc3dHclpVbGkrSHZkcUw2ak00NktFTWY2eHhRQlhrMytlSmlMWGFuWUJUeW9PZlZIa2piWnFERzNlNDRzQ3ZBcjZNU0lCV3lYNzlLMFZOMTJJOXRxL2owc01Wc2xsY1RKcHA5TnM3VTVLRU5UV3ZWeGlTd1F3a01CbjJ2dXFWNCtxT2tzK1Jkd1RLd0lybG04V1JlWWQvcG9BZStFZ3dBdzBGYk43eTJyZmI4NncxcHJlMTFwcWVqNjU4dGt6TGRqb0RPc2JOS2xkRzBBQm5JK3FmYjc5ZWUwM040L2tBMkZJaDNuOFZYQmQzbUlNQXN0bEwzdXhXQ3Z1RGt5MTBHK3k3bHFIN0szbklmdkZacEpDajd1SG9OVnJDU2hzVVNOaXp1aEpvdjVpVWtzNHU4blAwYjk5MUYxam9xUHhSZkttb014UllIcDlQVngyTkRWSVlSUTYzV1NUb3lSMkNoTEQ2QStURmR0RkovQ0VBL0crTHNYS0toMWdzRzYyc0ZRL2V4L0xsdTBCdStVd1kiLCJtYWMiOiI4NmU1ZjQxM2IzZjNiM2Y2NWQ4MDEyOTRiNWZmYWRmYTU3MmZmN2IyZDhlYmU0YTMzMWUxNmY5NmVhYzZmZGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507376769\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1676732763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676732763\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1133988926 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:11:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitONXpjeUNZTjVwajZ2VjBGTjRtalE9PSIsInZhbHVlIjoibmd1Z0NVa01BTmZWUDQvNjlNMFBZdUFDSUlRUkhsdnozWVEyM21NeDVBVUxmSkRnS3lEWHBaVlBwRzVwYlhjdDdsNFBEVlpNNXVHcmpqUTBrRm9CdFJZSXdRVHNZdEZpY0JMZXBxSndqU3diSkl1ZVVWN2RBcktiTjA1cmlzOTduU2M3SkcvNkI2NXl3VXNUeTFOYjlKb1pCbzc0ZkFXeHptYlZJRVhVdGRkd0xCM2JTMFZORk5HSmJ5d1c4RDRaUFJzYlJhdVl1ZjMyQkFnN3NaZ2VIWWs5VG53WWIrd29FeHJYMDFXMEh4OVN2UkNpNW9iNHdMa2lNdUFYaUthZktJVitwd3RQNTRRNlBKRHphOGF6VnY3aXRTUS9jZ0xqR0ppUzFxbXlHOGhaUFJ3amorQjJhek5JUU1Wcm0xSGJKeEJWNFN3REo1T2xTZWx6YmtZcnBqdTJRZUM2dkIxVHZIdnc5dEhXL2JXaUVLTklmblNmejVPMmtVYUVyenRJZVdGbVgzZ1kyQ3Qzb2pyTFZlbGlCREVxaGhjM0VtU3RDdzZIQjR3VnBNcUtRcTFvS0lRakZ2c0hZOTB2by9vK2JDRmtIdC9qalhHaWk4a053SllPSzZSS0xXNjEwNzJRaVRTK2psQjZmYlNsR25MeGozdkN1L0VBUDRmUkZOaUkiLCJtYWMiOiJjOGJiODc3MTE1YjlkMzMwZTU1NDExNGM0NWY5MmNkZjRlMjU2NWRiZDNiM2NhODc3YmIzZTY1NjE5OTRkMjg0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:11:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IldWQTBDZ1Q4Q1pWVTAzTHBvU2dGOEE9PSIsInZhbHVlIjoiKzlLQWwxYmJTNFUzUVRRTnArRkc0ZnZDSThlZVpXOExlWGlDV25VTXRFSVYxdzh5UHI1ZVd6Rk43eXhPWENEOWZTS1o4YmlwUEZ1cno4SnZSclhpbThOL01Gb0xaQTJuQTBibGVqaGVHNEtyTGJxc2hSUm1tWFBOVFNHZUE1S0F6ZWJUVG1WblF4R3VOWVV5c3BiRjZybjVZL0IxSU5XSmRQdUJqMk1yNFNoT3JXNVJXZkpWVnozWmg2WGorNkxtNU9vQUgrM0oxdnlmNVFmOC90clhTaWdJUERVcHA5OWd5bDhJcHEzdDZlVXVnakMwc1ZvbU5KMDBPV1dTdEhzdXpGT2ZIeFBsZklGTzY0SGdQQWI1TlZXUnlFZHh6dWMrdmw1YWN1YVVTODBBSVR6eVl0R25VOTRCaEZMOHJPV0xjZGh5V0ttNHB5ZzhmMVhuQlVWdDgzQTU5OUZ4YzZVTU5qZTBzblFrd3F6ajNIVGxnUHN0RkU1Sm5IQVlWVGhzb0hHQkt0eXNORS94ZVYvZU5yTDQvQ2llVmxIVEJXN1BieXhzU25FSmY1eU5YYmpIRGRCRXRMY3FkYU1GUEhpTlVMME14a3VzazM5NWdjZCtpcnlXOHg0dmR4Q1c1bk93eUY2azB4eVNmMnY3N3lHS3ZSNU02R2hwTkRjNlJBcnEiLCJtYWMiOiIyODhkMjJiY2FmMDRmZDVjYzE4NTdhMjU3NWE4ZGE3ZDY5ZDE0NjJkMWNkMTE0MzUyYTJjMjI0MzIwODhjODk5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:11:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitONXpjeUNZTjVwajZ2VjBGTjRtalE9PSIsInZhbHVlIjoibmd1Z0NVa01BTmZWUDQvNjlNMFBZdUFDSUlRUkhsdnozWVEyM21NeDVBVUxmSkRnS3lEWHBaVlBwRzVwYlhjdDdsNFBEVlpNNXVHcmpqUTBrRm9CdFJZSXdRVHNZdEZpY0JMZXBxSndqU3diSkl1ZVVWN2RBcktiTjA1cmlzOTduU2M3SkcvNkI2NXl3VXNUeTFOYjlKb1pCbzc0ZkFXeHptYlZJRVhVdGRkd0xCM2JTMFZORk5HSmJ5d1c4RDRaUFJzYlJhdVl1ZjMyQkFnN3NaZ2VIWWs5VG53WWIrd29FeHJYMDFXMEh4OVN2UkNpNW9iNHdMa2lNdUFYaUthZktJVitwd3RQNTRRNlBKRHphOGF6VnY3aXRTUS9jZ0xqR0ppUzFxbXlHOGhaUFJ3amorQjJhek5JUU1Wcm0xSGJKeEJWNFN3REo1T2xTZWx6YmtZcnBqdTJRZUM2dkIxVHZIdnc5dEhXL2JXaUVLTklmblNmejVPMmtVYUVyenRJZVdGbVgzZ1kyQ3Qzb2pyTFZlbGlCREVxaGhjM0VtU3RDdzZIQjR3VnBNcUtRcTFvS0lRakZ2c0hZOTB2by9vK2JDRmtIdC9qalhHaWk4a053SllPSzZSS0xXNjEwNzJRaVRTK2psQjZmYlNsR25MeGozdkN1L0VBUDRmUkZOaUkiLCJtYWMiOiJjOGJiODc3MTE1YjlkMzMwZTU1NDExNGM0NWY5MmNkZjRlMjU2NWRiZDNiM2NhODc3YmIzZTY1NjE5OTRkMjg0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:11:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IldWQTBDZ1Q4Q1pWVTAzTHBvU2dGOEE9PSIsInZhbHVlIjoiKzlLQWwxYmJTNFUzUVRRTnArRkc0ZnZDSThlZVpXOExlWGlDV25VTXRFSVYxdzh5UHI1ZVd6Rk43eXhPWENEOWZTS1o4YmlwUEZ1cno4SnZSclhpbThOL01Gb0xaQTJuQTBibGVqaGVHNEtyTGJxc2hSUm1tWFBOVFNHZUE1S0F6ZWJUVG1WblF4R3VOWVV5c3BiRjZybjVZL0IxSU5XSmRQdUJqMk1yNFNoT3JXNVJXZkpWVnozWmg2WGorNkxtNU9vQUgrM0oxdnlmNVFmOC90clhTaWdJUERVcHA5OWd5bDhJcHEzdDZlVXVnakMwc1ZvbU5KMDBPV1dTdEhzdXpGT2ZIeFBsZklGTzY0SGdQQWI1TlZXUnlFZHh6dWMrdmw1YWN1YVVTODBBSVR6eVl0R25VOTRCaEZMOHJPV0xjZGh5V0ttNHB5ZzhmMVhuQlVWdDgzQTU5OUZ4YzZVTU5qZTBzblFrd3F6ajNIVGxnUHN0RkU1Sm5IQVlWVGhzb0hHQkt0eXNORS94ZVYvZU5yTDQvQ2llVmxIVEJXN1BieXhzU25FSmY1eU5YYmpIRGRCRXRMY3FkYU1GUEhpTlVMME14a3VzazM5NWdjZCtpcnlXOHg0dmR4Q1c1bk93eUY2azB4eVNmMnY3N3lHS3ZSNU02R2hwTkRjNlJBcnEiLCJtYWMiOiIyODhkMjJiY2FmMDRmZDVjYzE4NTdhMjU3NWE4ZGE3ZDY5ZDE0NjJkMWNkMTE0MzUyYTJjMjI0MzIwODhjODk5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:11:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133988926\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-582747844 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582747844\", {\"maxDepth\":0})</script>\n"}}