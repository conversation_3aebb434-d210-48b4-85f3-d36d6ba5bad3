{"__meta": {"id": "Xb5dff42cdeb3c4bc80dceaf7be7ce172", "datetime": "2025-07-31 16:26:25", "utime": **********.467048, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979182.724664, "end": **********.467106, "duration": 2.7424421310424805, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1753979182.724664, "relative_start": 0, "end": **********.195817, "relative_end": **********.195817, "duration": 2.4711530208587646, "duration_str": "2.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.195844, "relative_start": 2.***************, "end": **********.467112, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.007679999999999999, "accumulated_duration_str": "7.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.391128, "duration": 0.007679999999999999, "duration_str": "7.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-357911891 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-357911891\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-128192755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-128192755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-404349555 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNHWHAxNk11THFuNDZ1emtIV3l0K1E9PSIsInZhbHVlIjoiL3E5TEJSWEVvVU1RaG1tdUdXYktGeGx4VE4vTTV2aEVJSWVacU5TUHArcFUzam5Pb0g5dTJxU05yZUlHdDlNWk00RlB6dWY2OUFkTEJQRWIrT0hrdE45UmN5b2lKMjhqL3oxa2NyckVlUFRmMUoyVm1jdWUwVnc5WEorYVp4cGY2TFZYcUlwQllkamFCTEhpQ3NVdi94YklLYi8rbWlzNzJScTVkZ0JJK2RpVFBkSHgzRTB1MHprVTBYZ3VKKzkyZXFXWWtSQ1ZPTHVmQkJNdDlrR1VPMUE3SlBKL093Ykg4NFBEeU96azJqOHV0ZVp5c2NFT2YwZlpnbFMzMmt3dENiaTk5MjRZbXFtdWJXRWFUT25sbW9yUEtvSmVHZ2dJZUIxRzJjVGV4SDBtS1ozOWFlcko4ZVhsVjhzSlM0NXRwVENiL3dOQVViYjU5RE9DTXFBTG5lRTNObWlvM3EvRjNKSUh3aDRBNkJhYW5OSUN1RngzRXlaNjdZOTRUMFpQUHg0c0dhNmdBb0htOEt1VXArZXN5SlM2TTEvSEZzMGg1QVZxQ0NXKzVGd0tvNkh3R3pJNmJkajFkMTJmUU8vdU4xSS93VTM3NkpEanhlc3hrM2ROSFcvb3NHOUZjbEFCMm5hVGVQakpHOGZ5NFZJYXlKSElDK3dDZ0xQcWY2Ty8iLCJtYWMiOiIxYjBkMWYxN2M2OTU2ZWI3NmEzYTAxNTFjMGYzMDk1YWRkOWE0MDgzNWFmYTk4NDY1NWE0NTMzNTYxYzRlNzk0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IktSYTRzYVV3T1BpQ3FDME5PQmVuQ0E9PSIsInZhbHVlIjoiQ3RNS2I2WEVaSitERjRlaGRYVmNPVUljNU5KQThYUkw1a0JLWDlDa3pMNW15akljUWpReE5UVEM3RzJ1WDdZTWJhVFJacGRIZHdyZDNjdHlxcXA4RjZ6L1ByRW9oUy80dHFZR1NKZU1HWDBZbk1NeHlvYXdnWDVzQmlFSXoyaHBPaHFGTTZPQVVsWTJzd0w2WXA1TkV6bGN4Y0pYaHdaUy9Xb1I1QXJ2NzNSZmZLWVJlcXp2bjdBOW9zdDdZN1NVNmp6dlg1dVJrdGhEZmZueHhmZnhZRXRqU0ZVZzBzNlEzbGMzZm4wZUMwKy9LK1kzSVRIcGpmQ05wM2lvdWhXRFJ2SW9EQWJRU1NnNXdRL2Rub2E4bEtBZFNOeDRudTRDL25USXN0bGlPbnJ4Q2NBWTNyZWNGL0tUMHY2Rml4YmJ6Qms5QTdTc09RYnBYWlJXaVFkRklRMFZUd3BRRTNsMnZEK0htbFM4UHBiSXZuYzdSbnZqR3h0TEdBeXUrMzVwZ0ZULzJSY2Y3WE1nWTZkV3ZwWHBFNHlpMmFPM1lGV2g1MktwMGNHS0c1MkVQOWRVWnJuUnQ4eVY3VkdYS1l3WjlqcEJqNEhFUzFuVGNVSlJTR0kwY21QVVowVG5LNDNRYWVLbXJWM0lMYzZRS3Z1M2ZrekhtTHlYVTBKd0RtL3oiLCJtYWMiOiI3MDBjMWIyYTNmZmM1Nzg2ODM2NWE5NWU0MmViNzNmMjRkMWQ4ZTI5YmMzNjllMDE2ZGUxYTFjYmRjNWFkZjUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404349555\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-148930239 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148930239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1195391012 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:26:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink3aGZ3WVplNk9KR2dEbXZFTUg1NkE9PSIsInZhbHVlIjoiMDhaZVZ6QVVPRTF3QmFZaXI5VXZLQmZmUGpDVVBHZ1FzdktmQ3pQeVNMa095SVdtQ1ozaG05S0NQV1FTbkJvVXJUak1Xejd1aDJUSFlCWFI4Wm1SMUR1dWhuSnBMVXduNGFaVkFQWjJSdXBFVm9Pd1pHNmdrQk5OKzBGbVgwb3R1REdyRUtNdW5yVC9XY3ZiU1FkNnJ0dWZORmJ1M1drNDNudk9OK1lCS093L2JudEtzUHA1dFliWWY3V1VIcnJUa05JdmttbzVTa1lBbXA1L1JvTW9ZbGF3MUxQVHBZTjRYeWlvNzFyYXBrV2x1QnNyVGhBU05Bcm1VR2J3Qk1nYUpJMHdIdEFIYy81ajZrUEdJTEc5dnNpV1ozZ0xwQno1ZGJJRWZTVTA5QjRwS0FzNXd0QzNFdWJLb25uaWgxSm81cld3cHRtNVcrTDcyUmtrTjQxNXpiOTA2cFdBVTFrOUMxWE16d2hQMEczcVh2RlF5SWxyc1Y4RkhBVEVmUGZRTEJTb285SVIxZGNCQ2laWEJaaFRDb0pHNFRoV3VYTjlmNEJweTVaQk9keUhtNXZ6ZzlBRTdKbXBQN1llVUpKVjFSYUNDdHFMSGZENS9SZS9WV2J6d2dLcnJFbi96cEN6OTlsb09WcFJjZjFDdTh3UnZkdHVoU3prWmFMamE0b00iLCJtYWMiOiIzOTdiZTZlNjRiZDA4YjI0MDE3MmM0OGY5MTU5YmI1YWMyOGY5NWVkOWNhODU3ZDJhM2RkZjU4YzkwZjBhZjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpGMk05cUY5VXk4WmY0UjNrYVhwblE9PSIsInZhbHVlIjoiUmM2NVdMTkovRWJWRnJmbllqdHF5UEFuMk8rZHNseXExNitSMFhOdGJJOFYwY2VhMWFWNCt4WnJBN1FvUWZRdDFGekwrOEFWdzQrWEU2aDU1UVhxNTBoWmRSL0x3RHZieXBZUU5jV0xXTk1YZWorV2pKVkpOVEZ5NHZHd0JlUDdJUlhNS1QvWnd6dEJ4eWk2cHFlSTgzZm1tbDJ0Yms0ZFBXY2FKUWYvUk1ybHhUdllDOWpMWFB3d2JYVW16eHNJWWZCUjZEZ0RDVERhWXg3L1hnMzBRQXlYSStMYUdqcHRERzQ2Q1lkSWRoc3ZnRkVtV2MxbFRMUHd3WTczdVlaOG4rd0ErVmFLbkJUZGVQZTZjeGU4UGNJcVpiNXFaQWtXWDJacEwydHFGVzdHTEdxMUg4SWkzZzJMMWlmTThpR0RtZ0RCQlRGRytGdnNNUDEyb1VkWjVEWnBkSGkyMHNYa1o2ZG41SWxSUzJtMHlhemlxL2VSQmhJZXhXYVZYRndNZGlWeTJOaTBFSzQrdnQ5ZmhmT1lMZi9RSDNlRFJxQmxiOG1od1B4MlZReXUxbkNCYVZLNXpSSGhpTElMVDdUVHJSYXFNbXgwekVSTnQ0S1NIK1MwUjEzeHFwYkFaYkhEZE8yQ1duTEU0aXFVdUdxYVZ6NkY0V0NDaDRCM1NXUXQiLCJtYWMiOiIyNDJhYjlhZjVmNzYwZjI0ODg2OTcyOTc4NTY2NjY2MzI1ZTEwMzIwMWYzYzA0YjVhN2UxODgzNGRiYmMwODdlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink3aGZ3WVplNk9KR2dEbXZFTUg1NkE9PSIsInZhbHVlIjoiMDhaZVZ6QVVPRTF3QmFZaXI5VXZLQmZmUGpDVVBHZ1FzdktmQ3pQeVNMa095SVdtQ1ozaG05S0NQV1FTbkJvVXJUak1Xejd1aDJUSFlCWFI4Wm1SMUR1dWhuSnBMVXduNGFaVkFQWjJSdXBFVm9Pd1pHNmdrQk5OKzBGbVgwb3R1REdyRUtNdW5yVC9XY3ZiU1FkNnJ0dWZORmJ1M1drNDNudk9OK1lCS093L2JudEtzUHA1dFliWWY3V1VIcnJUa05JdmttbzVTa1lBbXA1L1JvTW9ZbGF3MUxQVHBZTjRYeWlvNzFyYXBrV2x1QnNyVGhBU05Bcm1VR2J3Qk1nYUpJMHdIdEFIYy81ajZrUEdJTEc5dnNpV1ozZ0xwQno1ZGJJRWZTVTA5QjRwS0FzNXd0QzNFdWJLb25uaWgxSm81cld3cHRtNVcrTDcyUmtrTjQxNXpiOTA2cFdBVTFrOUMxWE16d2hQMEczcVh2RlF5SWxyc1Y4RkhBVEVmUGZRTEJTb285SVIxZGNCQ2laWEJaaFRDb0pHNFRoV3VYTjlmNEJweTVaQk9keUhtNXZ6ZzlBRTdKbXBQN1llVUpKVjFSYUNDdHFMSGZENS9SZS9WV2J6d2dLcnJFbi96cEN6OTlsb09WcFJjZjFDdTh3UnZkdHVoU3prWmFMamE0b00iLCJtYWMiOiIzOTdiZTZlNjRiZDA4YjI0MDE3MmM0OGY5MTU5YmI1YWMyOGY5NWVkOWNhODU3ZDJhM2RkZjU4YzkwZjBhZjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpGMk05cUY5VXk4WmY0UjNrYVhwblE9PSIsInZhbHVlIjoiUmM2NVdMTkovRWJWRnJmbllqdHF5UEFuMk8rZHNseXExNitSMFhOdGJJOFYwY2VhMWFWNCt4WnJBN1FvUWZRdDFGekwrOEFWdzQrWEU2aDU1UVhxNTBoWmRSL0x3RHZieXBZUU5jV0xXTk1YZWorV2pKVkpOVEZ5NHZHd0JlUDdJUlhNS1QvWnd6dEJ4eWk2cHFlSTgzZm1tbDJ0Yms0ZFBXY2FKUWYvUk1ybHhUdllDOWpMWFB3d2JYVW16eHNJWWZCUjZEZ0RDVERhWXg3L1hnMzBRQXlYSStMYUdqcHRERzQ2Q1lkSWRoc3ZnRkVtV2MxbFRMUHd3WTczdVlaOG4rd0ErVmFLbkJUZGVQZTZjeGU4UGNJcVpiNXFaQWtXWDJacEwydHFGVzdHTEdxMUg4SWkzZzJMMWlmTThpR0RtZ0RCQlRGRytGdnNNUDEyb1VkWjVEWnBkSGkyMHNYa1o2ZG41SWxSUzJtMHlhemlxL2VSQmhJZXhXYVZYRndNZGlWeTJOaTBFSzQrdnQ5ZmhmT1lMZi9RSDNlRFJxQmxiOG1od1B4MlZReXUxbkNCYVZLNXpSSGhpTElMVDdUVHJSYXFNbXgwekVSTnQ0S1NIK1MwUjEzeHFwYkFaYkhEZE8yQ1duTEU0aXFVdUdxYVZ6NkY0V0NDaDRCM1NXUXQiLCJtYWMiOiIyNDJhYjlhZjVmNzYwZjI0ODg2OTcyOTc4NTY2NjY2MzI1ZTEwMzIwMWYzYzA0YjVhN2UxODgzNGRiYmMwODdlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195391012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1741229129 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741229129\", {\"maxDepth\":0})</script>\n"}}