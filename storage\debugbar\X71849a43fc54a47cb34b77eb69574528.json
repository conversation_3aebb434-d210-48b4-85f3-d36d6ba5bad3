{"__meta": {"id": "X71849a43fc54a47cb34b77eb69574528", "datetime": "2025-07-31 16:31:24", "utime": **********.210113, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979480.569545, "end": **********.2102, "duration": 3.640655040740967, "duration_str": "3.64s", "measures": [{"label": "Booting", "start": 1753979480.569545, "relative_start": 0, "end": 1753979483.945163, "relative_end": 1753979483.945163, "duration": 3.3756179809570312, "duration_str": "3.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979483.945239, "relative_start": 3.****************, "end": **********.210208, "relative_end": 7.867813110351562e-06, "duration": 0.****************, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EIFnnt1chln6WsGfcBeJQtCWpqL2UMTTs1iT2z29", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1887244033 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1887244033\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1167623435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167623435\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-779569378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-779569378\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1766354683 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766354683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-33107173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-33107173\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-348986226 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:31:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxldkorSFg2MDU4M3V4enFsMzFDY3c9PSIsInZhbHVlIjoiUE9uOVlCam1DVkVQTWlCQ01EaUFCbyt3MDNOYzFENnl1bXNyTjJWdTNTWGhQOHArcnl2cEtndENZUEYvcHBzVTZEWXZ5cUtRVmZ6Z1hKWUFNS085dmpzOEg1Zm4rY2tJRzFpZmFPQ1cyUkUyVXlpcG05dXZnV3l4QmVhSXZ4UE5JT1ZlajJkZmNwNnljaXBYaVFvQ0ltc1pOT3BmSWl0cEtXZFNyRWlUd2xJcHF1T0M0cWE3eitzWG1TalAxbldEN3c5RnBNOVY2VEdTK01BT1RVd2NNZWhZcTlSQk5EVnh6VEpCbjNPZkc4OFFKSmJ4VkxNbEw2L0NZdy9OV25NR3NQQTNvTlc4dzZnc2o3VXVTbmIyNVg5SG05QUdhRmdQc2NFbXBQcW1GQnZOS3ZnTXVnREVrRXZiN2p4ZzRBRU9yTnd6V3JJQ2w5bzhaRnEyak1BY1lwR29Sa2pzaHNZN2ZESCt1S0h6SldxNHJ1VnFtS3JGTlVCbVptNU5MNjVNeHFkRnFqSXR1T3lTUVRTNjdKMjcxNUl0cmRGU2VjUlg4SXFEbDJQWllGakpPa29HT0pCd0dEaVRtVTZhcTJFN3EzYWdqZ24za2ROQUFoMDhhK3R4QlhOc0ZIME1zMEZ1NCt6TS9ZRDB4MWNUU2VaL3JRbjR0QjBNdkc2MnBhNTUiLCJtYWMiOiJkNDU2MzA2YmM0MTliMTMxMTJiYWEzZDdhMjUyNmFiZTI1Y2RjOWRjOTc0ZDljMjBkNGI1YWVmNmU1ZTRkZjFjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:31:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlAvcFRMSW5nYVZBWi81R2p2UUdyTnc9PSIsInZhbHVlIjoiVklvZHBOT0J0VE5MeFp1TFYvN2NtQUppTytMNTB6K2hCSGJVNVZkSHlLNzBOdCtPRThFU3k4WjFWMkx0V1c4TWw3M0ZWYXNQa1NrckJGaFhaR1lTSDFpVmg5ZHQxSStWNTFCNlhjSzlIbGFZMFRCc3NJL0JXL2xrVU11Zjh6NDAwaDlLZEpOYVlOeUpFQTdxdlRpQ3hFRWgvbTh6S1NwOHNrdFUzSk91b2RuZnZZZVB6bWp4QU9oTTc5cGsyUGtCMmF4VXhaeVVZZjhyK25mMTM3Qkh1MUVWQm0zRGVwWkR1WnNiZGQyTkFJbnJxU003b1FOMlJKT0VkZkgxVUdrSmJqMXVVc0hIVU94YS9MSmhqVVlCUHRwT2tRalRTRkNsTHZCbnpObjVBdHNNOW5uMTZWWmtLMnA0UTAvZC8zeHE5eHBhNzlTeWtEKzIrbFc4QlZaL1VHNXpkb2R2ZlREZFJ3MjZ4WWFTZWo3aUI1UG1GSWRmN0lyTVRyWGw3a2ZQTXpKTS9UTnZJQzU5UVVVVkZIZUVFVFhlYUZ0MDlWKzNTZFNTYkIwN1czbFp0bnBZTkVZdmcwR29Hc0J1L2tSamxINHZ6dkdBV01MNktmNGd1YlQzRnJsK2UrSGdnYXl2ckVDaHRWV3d2am1LWDdKNTNOeFRJam9oYjBOYVNpeUYiLCJtYWMiOiIyZjk3ZDdiYzM4MGZmZmVhMDZlOWZiZGQzOGJlMTk5Nzg0MDdiY2Y5NTNkZWIwNTc3YTU5YmQzYThhYzFhMTM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:31:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxldkorSFg2MDU4M3V4enFsMzFDY3c9PSIsInZhbHVlIjoiUE9uOVlCam1DVkVQTWlCQ01EaUFCbyt3MDNOYzFENnl1bXNyTjJWdTNTWGhQOHArcnl2cEtndENZUEYvcHBzVTZEWXZ5cUtRVmZ6Z1hKWUFNS085dmpzOEg1Zm4rY2tJRzFpZmFPQ1cyUkUyVXlpcG05dXZnV3l4QmVhSXZ4UE5JT1ZlajJkZmNwNnljaXBYaVFvQ0ltc1pOT3BmSWl0cEtXZFNyRWlUd2xJcHF1T0M0cWE3eitzWG1TalAxbldEN3c5RnBNOVY2VEdTK01BT1RVd2NNZWhZcTlSQk5EVnh6VEpCbjNPZkc4OFFKSmJ4VkxNbEw2L0NZdy9OV25NR3NQQTNvTlc4dzZnc2o3VXVTbmIyNVg5SG05QUdhRmdQc2NFbXBQcW1GQnZOS3ZnTXVnREVrRXZiN2p4ZzRBRU9yTnd6V3JJQ2w5bzhaRnEyak1BY1lwR29Sa2pzaHNZN2ZESCt1S0h6SldxNHJ1VnFtS3JGTlVCbVptNU5MNjVNeHFkRnFqSXR1T3lTUVRTNjdKMjcxNUl0cmRGU2VjUlg4SXFEbDJQWllGakpPa29HT0pCd0dEaVRtVTZhcTJFN3EzYWdqZ24za2ROQUFoMDhhK3R4QlhOc0ZIME1zMEZ1NCt6TS9ZRDB4MWNUU2VaL3JRbjR0QjBNdkc2MnBhNTUiLCJtYWMiOiJkNDU2MzA2YmM0MTliMTMxMTJiYWEzZDdhMjUyNmFiZTI1Y2RjOWRjOTc0ZDljMjBkNGI1YWVmNmU1ZTRkZjFjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:31:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlAvcFRMSW5nYVZBWi81R2p2UUdyTnc9PSIsInZhbHVlIjoiVklvZHBOT0J0VE5MeFp1TFYvN2NtQUppTytMNTB6K2hCSGJVNVZkSHlLNzBOdCtPRThFU3k4WjFWMkx0V1c4TWw3M0ZWYXNQa1NrckJGaFhaR1lTSDFpVmg5ZHQxSStWNTFCNlhjSzlIbGFZMFRCc3NJL0JXL2xrVU11Zjh6NDAwaDlLZEpOYVlOeUpFQTdxdlRpQ3hFRWgvbTh6S1NwOHNrdFUzSk91b2RuZnZZZVB6bWp4QU9oTTc5cGsyUGtCMmF4VXhaeVVZZjhyK25mMTM3Qkh1MUVWQm0zRGVwWkR1WnNiZGQyTkFJbnJxU003b1FOMlJKT0VkZkgxVUdrSmJqMXVVc0hIVU94YS9MSmhqVVlCUHRwT2tRalRTRkNsTHZCbnpObjVBdHNNOW5uMTZWWmtLMnA0UTAvZC8zeHE5eHBhNzlTeWtEKzIrbFc4QlZaL1VHNXpkb2R2ZlREZFJ3MjZ4WWFTZWo3aUI1UG1GSWRmN0lyTVRyWGw3a2ZQTXpKTS9UTnZJQzU5UVVVVkZIZUVFVFhlYUZ0MDlWKzNTZFNTYkIwN1czbFp0bnBZTkVZdmcwR29Hc0J1L2tSamxINHZ6dkdBV01MNktmNGd1YlQzRnJsK2UrSGdnYXl2ckVDaHRWV3d2am1LWDdKNTNOeFRJam9oYjBOYVNpeUYiLCJtYWMiOiIyZjk3ZDdiYzM4MGZmZmVhMDZlOWZiZGQzOGJlMTk5Nzg0MDdiY2Y5NTNkZWIwNTc3YTU5YmQzYThhYzFhMTM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:31:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348986226\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2129756874 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EIFnnt1chln6WsGfcBeJQtCWpqL2UMTTs1iT2z29</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129756874\", {\"maxDepth\":0})</script>\n"}}