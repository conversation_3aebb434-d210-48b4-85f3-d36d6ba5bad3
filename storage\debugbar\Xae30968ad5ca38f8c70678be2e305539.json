{"__meta": {"id": "Xae30968ad5ca38f8c70678be2e305539", "datetime": "2025-07-31 16:52:38", "utime": **********.446969, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980753.920284, "end": **********.447051, "duration": 4.526767015457153, "duration_str": "4.53s", "measures": [{"label": "Booting", "start": 1753980753.920284, "relative_start": 0, "end": 1753980757.940396, "relative_end": 1753980757.940396, "duration": 4.020112037658691, "duration_str": "4.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753980757.940432, "relative_start": 4.020148038864136, "end": **********.447055, "relative_end": 4.0531158447265625e-06, "duration": 0.5066230297088623, "duration_str": "507ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.07339, "accumulated_duration_str": "73.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.130832, "duration": 0.04001, "duration_str": "40.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 54.517}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.280427, "duration": 0.01887, "duration_str": "18.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 54.517, "width_percent": 25.712}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.359095, "duration": 0.01451, "duration_str": "14.51ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 80.229, "width_percent": 19.771}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1478372398 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1478372398\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1216511466 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1216511466\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-263244374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-263244374\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1693404746 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5pN042cVo1UXhIdEdpcWZNdkdCVFE9PSIsInZhbHVlIjoiY083YmNoYmFVNzZHMFNreXMxSUVUUVc4bi81RS9MTTdseFZjVUNQTmNYbWlySmlNWTE0aWl6ZXpLVVFacSsydlZWMmxNQnVyQjNGYXRPeWd4NzVoam93dm5MbnEySllEVjZwTk1KWFBSNERBSURjcllqT0x2VkFVZVZ0dlYzZnYrSnR2QWxGM2Q4VGl0bWY1TTU4d2xNeHdvWjdqaWltMlFEK3JTNnJSb3Y5YVVtQ3JjUDQxOGNzSCs3aHJ2Q0thcm5lcjFlRzFqcVBVT2Q4V1FzUXdwOWNGUVgwSG1PNHdNNWpTMHhlTEYwSWJqZWZZYnhLZXNTeitwMjU5S1VmWlM1SXVwaEo1RUd4SWNsQjgyZGJPUlVROVhJZUUwQm5hT292R0lTczg2a2RGUzVpU2RNTEpnYTZjang4eXlxY2Y4M2dHNU0wWFhFZW9lZVVkNnFmWTcvblBGTXBWaU12em8xZmRWRTF6MWZEeTVLQVpuWDcrZ050SzVvSUh1eTJGa0tFN0dGRWNTRmtVRjJmK25CTjFaYXpxdTM1NEZ3RG9YSGhrRWdlQjdVYnZpOC9MUmsxSTlnVmUwb3NzaFZKNko2Y0pFYWpoZytkdlhXNldJNWV0YTNiZmhCTDhkYjdPM1FTb2QrS3p5cG1WREd2S0s5a1d3MHpYUXQzMUZhbnMiLCJtYWMiOiJkNTg4NGNkMGZmMGY0ODU1MzgyMmNjOTI2OWU1MjA2NTVmZTg3NzgwOWI0ODU2YjA1YzRhNTcwMDQxYTk3Y2I3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpGMGh2bnREaVBuVW4xVjNnSHlYL2c9PSIsInZhbHVlIjoiWjhJaDh3bjVqRDRwMFNqa0dkbnNYWFE5RTNBM29HNzF4cG1xcWF4bHEwVHlWb0NsRlRuUkV6N2k1dHRoSFcvYk4zSGlsaTQzb2VMVTlKb0tqMmxYdW5zbXdXeUY3WHpDekVaUm1qdXFMMjdWRXI4UHJmdVlJOVNZYVgyZ0N4emFzQ2w0Ymh6bXEzd1UzQTRPczZwbm05Nis1d0VrTmczZ203YUZPOVNXNGxSbzRNMWpDTCt4YXM5YmpqZWJ0NzErVmYzMkdMbnFBd1FqSkUyUmw4clIwM3paMnNJOS9TcnpKR1lmSmtyUHVUWlpJV2RDSjRBc3NJZ0pLV0MrNHk2ZGlIbklCTTk1QXV3VGtKY1hjNkljQkVkT21kcWNlT09JeTRpZGFyWldFblcxRWhFemhsVkIrWUNueUwya2VYNlQ4MVN3TVh2eCtsZllrRytlNDllNFFKanl1NW9wK1h5ZlhoYWhzTDhxVVlXaFQ4emFVbVcxb0U4TkorMUFLdWJMdnljbTBiYXdPdlhqNlRDODgybzJRSXNhbDlEb2t5dG85ZHdNR0kwcGtBSmVSUExjMlV0dXkvM1BiYVBjWTBMcGhKNWxobWJlYklzVi9rLzhvSDUrYjdwTXJ2NFRTSWo3VUFYVmwzRDZoUlRlQm53Y2xOY0dCM3dXQzY4bVFwQU0iLCJtYWMiOiJhNzVmYTA2ZGNkNDgxMzk3YjQ0NmRmN2M3OWE0YTEzMWFjM2M3ODQ1ZDNjMWU1ZjgzNGE0ZTY3M2U5NWM0YjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693404746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1361028752 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361028752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1566190697 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:52:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im4wVitoU3hHVXFia1hpZ0ZGUGU5U1E9PSIsInZhbHVlIjoiY3JvUjdSOWYzdTc3bXpTcnpzdnMycTBNandzUmhscEI0Qms5R09kM0RXZ0Z5WjN5cndmTVR6d0R4ZWpSSkxhZld4NmxlK3pWZlJyZjRCM2h3eXdSTDlaMFR6ejEvSlhMVWk5MFlQWWh6bjlNczJ1TUJXS0t4akNyS1p1ZVh6ak9Xd20rbkNKS09uYUxuYVFudHorTDNOTUtuN0RIcFp3OUsyQ0pHcDNKMlpSL3Z0SC9Ca21hWW5VeHFuUjBOalBYN0ZqSmkzaW4yYmNWcEE3NGF5Z3poc0JZc2wyVXpBYW9rV2VOMEZ2UUt4R29TRTluK0R0cGlEUkxtUnZ4aE9YM1RWOTFqUHdvaVVPK0RjT3lIY2JXTERjME9MdnJ3cTFwYkVFZE9RcXVUZTBMTHg5dkJWUjNGSlBUOHhNNG16WnlkR1NKbWdhLzB1RURLV3FGdDN5dUNmcXd0TUp2aVFGbzNUU1h4dG9aditYNHE3ZXNNbktzZzRQdVFTOWlQSHRRamswUkNzbEkwNkpZS1lpL3BXakVCMlpkdjFGNkFURVhtdjNHQUZUVUYrTk16cm83czFaTXhseWpWdHdobEkyVFhJYXk1Mzl4NFF4SHZINjNMZXdzU2FSc0cvbHNkdXVuWko1Qm1CdmhaQjJXZ0d4aVREZ252VER3NU14dU5BMXkiLCJtYWMiOiIyMGY1YTJlODc2MWYzNzY2M2I2MzJmYzQyNGQwOGJmYWJjNjdkZjhmOThhNDVmZjEzYmE4YTFlNzA5M2EyYTBjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:52:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZlWFdVNEJSOHMyZlZ1a2VJL0ZVYnc9PSIsInZhbHVlIjoiWEo2N25PQ0hjdEp1Wk5RVEpoWlFYK0x5SHhlWkxseXJ4eXBCeStDb0lyUE9tMnU1N0RIQ1IvcDdRT2E4M2tqWXRSWnhhVHR4ZDlXUXNpODQwYlhEa3JUZG9RL1hpTHlJdXdkWnVuWndKbmtUK0YyeStabTB5bVdDYVVmcnl0RXVhbTVHYVV6b0grRVRzalBzMmxoTHF5Zm9ZMGtZT1JDaU1tZHFiRERwOVR3bWFJQ21LcUdaeERxRnQwRjV0UG5TdFVNN2RaclFaY3VOOEEvWFljRzBicTd6WFNYYU9VeEE0M2JFOUdpaGRiQW1CVmZ0aVlPVEduMGFESTh3SjhBNnhabkphMlBVMEZONEZBZ1BjVHpvRG5mOVhIb2lDZU5iZ2pTaC9PQUpTaENZNkhyZzIzMU5PUUJ5M3BGTlVUVkVyaVVqL1lFUktpRXlBYWJYV1hncXNYVEQ4N3VPQVBaRWVJbTdWbk5GVGQ1VlJuZXBPRGZVMGU5SWlBalFYbDIwTGFrSk9hNE9KNG5xU3oyd3pZY2g5MiswNXJOSnB2YUltRjI4MnhXZjluaWQzcmNYaHNsbjlJS3RBUTVaZldEcnFwSWF3MmJDNmZhSWZxYmpGKzZoZUpoVWhGUnNLUk4ycDdTYU5LbUVmbmJoaStKVG9YTEN3aGs4ckJSQVJ6R1AiLCJtYWMiOiJhYzI2M2E2N2M4ZmQxZDlmMzhmNGEwZmYzMDNmZWMxZjIyNTUyNDQ0YmQ2ZjA1OWE3NmIzZDk0YjFjMDRiMzAyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:52:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im4wVitoU3hHVXFia1hpZ0ZGUGU5U1E9PSIsInZhbHVlIjoiY3JvUjdSOWYzdTc3bXpTcnpzdnMycTBNandzUmhscEI0Qms5R09kM0RXZ0Z5WjN5cndmTVR6d0R4ZWpSSkxhZld4NmxlK3pWZlJyZjRCM2h3eXdSTDlaMFR6ejEvSlhMVWk5MFlQWWh6bjlNczJ1TUJXS0t4akNyS1p1ZVh6ak9Xd20rbkNKS09uYUxuYVFudHorTDNOTUtuN0RIcFp3OUsyQ0pHcDNKMlpSL3Z0SC9Ca21hWW5VeHFuUjBOalBYN0ZqSmkzaW4yYmNWcEE3NGF5Z3poc0JZc2wyVXpBYW9rV2VOMEZ2UUt4R29TRTluK0R0cGlEUkxtUnZ4aE9YM1RWOTFqUHdvaVVPK0RjT3lIY2JXTERjME9MdnJ3cTFwYkVFZE9RcXVUZTBMTHg5dkJWUjNGSlBUOHhNNG16WnlkR1NKbWdhLzB1RURLV3FGdDN5dUNmcXd0TUp2aVFGbzNUU1h4dG9aditYNHE3ZXNNbktzZzRQdVFTOWlQSHRRamswUkNzbEkwNkpZS1lpL3BXakVCMlpkdjFGNkFURVhtdjNHQUZUVUYrTk16cm83czFaTXhseWpWdHdobEkyVFhJYXk1Mzl4NFF4SHZINjNMZXdzU2FSc0cvbHNkdXVuWko1Qm1CdmhaQjJXZ0d4aVREZ252VER3NU14dU5BMXkiLCJtYWMiOiIyMGY1YTJlODc2MWYzNzY2M2I2MzJmYzQyNGQwOGJmYWJjNjdkZjhmOThhNDVmZjEzYmE4YTFlNzA5M2EyYTBjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:52:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZlWFdVNEJSOHMyZlZ1a2VJL0ZVYnc9PSIsInZhbHVlIjoiWEo2N25PQ0hjdEp1Wk5RVEpoWlFYK0x5SHhlWkxseXJ4eXBCeStDb0lyUE9tMnU1N0RIQ1IvcDdRT2E4M2tqWXRSWnhhVHR4ZDlXUXNpODQwYlhEa3JUZG9RL1hpTHlJdXdkWnVuWndKbmtUK0YyeStabTB5bVdDYVVmcnl0RXVhbTVHYVV6b0grRVRzalBzMmxoTHF5Zm9ZMGtZT1JDaU1tZHFiRERwOVR3bWFJQ21LcUdaeERxRnQwRjV0UG5TdFVNN2RaclFaY3VOOEEvWFljRzBicTd6WFNYYU9VeEE0M2JFOUdpaGRiQW1CVmZ0aVlPVEduMGFESTh3SjhBNnhabkphMlBVMEZONEZBZ1BjVHpvRG5mOVhIb2lDZU5iZ2pTaC9PQUpTaENZNkhyZzIzMU5PUUJ5M3BGTlVUVkVyaVVqL1lFUktpRXlBYWJYV1hncXNYVEQ4N3VPQVBaRWVJbTdWbk5GVGQ1VlJuZXBPRGZVMGU5SWlBalFYbDIwTGFrSk9hNE9KNG5xU3oyd3pZY2g5MiswNXJOSnB2YUltRjI4MnhXZjluaWQzcmNYaHNsbjlJS3RBUTVaZldEcnFwSWF3MmJDNmZhSWZxYmpGKzZoZUpoVWhGUnNLUk4ycDdTYU5LbUVmbmJoaStKVG9YTEN3aGs4ckJSQVJ6R1AiLCJtYWMiOiJhYzI2M2E2N2M4ZmQxZDlmMzhmNGEwZmYzMDNmZWMxZjIyNTUyNDQ0YmQ2ZjA1OWE3NmIzZDk0YjFjMDRiMzAyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:52:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566190697\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-123915561 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123915561\", {\"maxDepth\":0})</script>\n"}}