{"__meta": {"id": "X079edd83f2c2bbb93004850b60d2cb62", "datetime": "2025-07-31 15:48:40", "utime": **********.559333, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753976917.557972, "end": **********.559396, "duration": 3.0014240741729736, "duration_str": "3s", "measures": [{"label": "Booting", "start": 1753976917.557972, "relative_start": 0, "end": **********.141291, "relative_end": **********.141291, "duration": 2.5833189487457275, "duration_str": "2.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.141392, "relative_start": 2.5834200382232666, "end": **********.559403, "relative_end": 6.9141387939453125e-06, "duration": 0.418010950088501, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44333968, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=58\" onclick=\"\">app/Http/Controllers/DashboardController.php:58-75</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02582, "accumulated_duration_str": "25.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 65}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.434163, "duration": 0.02582, "duration_str": "25.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TRf3GNBBfDeKjrk0u7TxYnwpnARSS51QzBhar5Bt", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-515889838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-515889838\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-267198265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-267198265\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1812754286 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktpMjNhUmJUUXJORk5NeUZoQzdtVEE9PSIsInZhbHVlIjoiUmtQUmhmMFR5eTY2eWtaa2xLZ1VIRDJoMzI3YkJrc0IwWkc5WkFNOTEzVHI4NVh5TDU0aFQ5QzduaVhkcU4vbjZGM0cxdU51bVM0SEI2bVFnNW4vQnZCMjVCamFmWHJOVElEV2NjYk9CSThjNGh1RExiWUQvSEFWcUhHbk1rRDdsSTFnUm5vU29lNCtFSFJaVnFpZTFJYkVjeElSbGloeDAvZDZxUFU0WmtpdkhxaTFydHFhRlFuTTlwMDdhQnNtWHdyMFlWdmQweC81dUYyNGxSYjIyY1FLSnhSS2ZxaExaZXJpSkVjZk51VUFFNFVUREJrTkFmZ3lqZDZqUk11cVdQQ3hsb1FVYWRsa2hvVjcyOGZYMzlaL0tsRmhHaTlnaEtNRXVOZGlUemZHZWNXdVpTdWo4dytlMWlFbFBLM2wyTnN4Y2J1cmFFYXhoWExVdzJqZ0xqcnEveUNuQXN5QSszbFA4M2pSNytJbGtZV3dvWkU4Y3pnUWhTQ1MwcGMwcTJ0TGhBWi9QcHUxMVVJd1BDckE0QjRhemc0Qjg5Y0FwQWpyK1JDZWF5am5JR2ZXaTB0ZmNPbkk0cHZXcDczWUJRVGR0SUZqenVyd0laRzlUNzRsRzVHWE03dWFvYUVvOERIT1RoNkMvQ0JmdWgrc1p3NDVod2xSUXpmSXgvVGUiLCJtYWMiOiJlZWNiOWVlZDdhYWI4ZDc3OTI5MjRlNmI4NzM5OTk2MTAzZTY2NjU5ZTZhNzVmZWU2YmNkNjI0Nzc5ZjY2NjdhIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im9aTThoRDVGbzB2WjQ5VmdSdEJmakE9PSIsInZhbHVlIjoiV1hodjVwNWpEa2hYbnpnUlRJZksyUTlkMlBxbHlEZmRBWjIrd2lMQmp3S3hzVDV2bE9mMDJUS0VmaXNyQUl4Vk1Xc0M0RjRjRitkaGhETElUSE8xVkFWYlNVMTl6QWFHT25QMWZsY1ZMVHhySnB0VnNSYm9EdUVxYTM5UjE0RHdnOGdVME83TTdOczN6T1dEOFlqK2R5R2lQTTFmaFg5QlM0QlVqbXhDMXEwc0pVMFQwbzNDUjBWMG0vWFNSdkV3UWNGUm1nM0dGaG03T1FNZnVZbSs5Y1ZNYm41Zk5nOUJVOUZKTWhxWTE0dFgzb29UMWVVYnA5UmtidlpPTkhUYjVMSmdOem9hL0N5dkhwMS8rZU1sVWtHQzRBUm82U0ZNV0M2bkhiZzEyZ2llekxSa0dyV0F5QVI2RnI1c0o0dkF0YnZ6dGhQNmFTMEJtNEU5dzdSVjErVlBUYTM4ZEFuWEtrZjM1WkY0Q3pSZXBWVHcrdUloUGtzZGlHS1U0bVYwMVNFRzJXck9qRmhKV3RUOGt0REsrTm4vdFF2cXBFZk1oQThQa2ZTSHZ0ck5iclh5bi9DWGpsaENLdzNjMlRDbGdOZVBkRGcxYUZObmpyMFVuQThxU0FSQURWdkJqRisydjZyeENyR1k1dmhxOGRYbVdLTFJpZzJwd1U2dkRTS0ciLCJtYWMiOiI1OTg3MzE2OWYyYmQ3OTg4NjBiOGExN2E3NmNmOWRlYzg5MTFmMDAzYTA4MmM0NDZlMzQ4NGU5ZGEwYTg4ZTg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812754286\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1868085354 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TRf3GNBBfDeKjrk0u7TxYnwpnARSS51QzBhar5Bt</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868085354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-509649409 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:48:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">https://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlYK2dnWVAzbVFHVU5tMnoyR0M3dHc9PSIsInZhbHVlIjoiM1FhRVZxdVZYRXJGYm5JbWtQc0tCcW16YWdtajUwSlE2UHBMejJSZ0ZHWnRUNC8rcFhDRml0eTdFSWtmbUhhcWpXdTViM2srbVFlMW4wbDViVzJiR3lOY1dXbkxwNm95ZW03bTdmOTBLZUJlTGN5TU1DU0xKTlpHa3ZYUVJvZElra3ZQbU5ZdWczZThwaWVrUkZMZHpGRGhlZW5kektyd0tha05sYzVIeGtsTERKaUErbEVwUjNaeHRRZzQyRnRENlVTbmtHTzcySWowRG1mb2Ruai9hb1NDdENLdkxnL2VYV0lwNmhDYVRtRHZ0dXJUVjUxUzZORXJ6VzFzU0I2YUxyMmEyamRFU2pTSG5PbElxMUNTQWFrMVJ6eGIyRE0xWlVGZjhpQUdxNjFTZ0t5cUxOeitvVGdiQ2diYkUzZ1VMYUJSbzdXdnZ0ZUpvOVpXVjRVQ3h2OFc0Z3RjdEVvdnFtOXlyMlAvU1JSdTh1U3YwN1RnSS9yV3ZkaGRoa1ZLcGxsbVlHazJSMnR2WFV4cUVEOFMzczVlSC83OE9ncG80SHhLcHV2UGhNYjljd0cwbXE0cDAzV1RYK3lGb3llZXVSQi9iVDFNSFJhWURnbmdZaXN3aGZVZHhqaW0zcml3OEErVTRJK0NlUHhsOHJ5K0t0aWsrb3BoMGoxMDFzbjQiLCJtYWMiOiJjOTViMmM3OGYyYmZhYjZhOWQxMzA0NmYyMjg5NmM3MWM2ODZkZjY2ZjQyYmY3OTNjNjFkNjk0OGRkZTkzYjRjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:48:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:48:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlYK2dnWVAzbVFHVU5tMnoyR0M3dHc9PSIsInZhbHVlIjoiM1FhRVZxdVZYRXJGYm5JbWtQc0tCcW16YWdtajUwSlE2UHBMejJSZ0ZHWnRUNC8rcFhDRml0eTdFSWtmbUhhcWpXdTViM2srbVFlMW4wbDViVzJiR3lOY1dXbkxwNm95ZW03bTdmOTBLZUJlTGN5TU1DU0xKTlpHa3ZYUVJvZElra3ZQbU5ZdWczZThwaWVrUkZMZHpGRGhlZW5kektyd0tha05sYzVIeGtsTERKaUErbEVwUjNaeHRRZzQyRnRENlVTbmtHTzcySWowRG1mb2Ruai9hb1NDdENLdkxnL2VYV0lwNmhDYVRtRHZ0dXJUVjUxUzZORXJ6VzFzU0I2YUxyMmEyamRFU2pTSG5PbElxMUNTQWFrMVJ6eGIyRE0xWlVGZjhpQUdxNjFTZ0t5cUxOeitvVGdiQ2diYkUzZ1VMYUJSbzdXdnZ0ZUpvOVpXVjRVQ3h2OFc0Z3RjdEVvdnFtOXlyMlAvU1JSdTh1U3YwN1RnSS9yV3ZkaGRoa1ZLcGxsbVlHazJSMnR2WFV4cUVEOFMzczVlSC83OE9ncG80SHhLcHV2UGhNYjljd0cwbXE0cDAzV1RYK3lGb3llZXVSQi9iVDFNSFJhWURnbmdZaXN3aGZVZHhqaW0zcml3OEErVTRJK0NlUHhsOHJ5K0t0aWsrb3BoMGoxMDFzbjQiLCJtYWMiOiJjOTViMmM3OGYyYmZhYjZhOWQxMzA0NmYyMjg5NmM3MWM2ODZkZjY2ZjQyYmY3OTNjNjFkNjk0OGRkZTkzYjRjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:48:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:48:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509649409\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-627377844 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TRf3GNBBfDeKjrk0u7TxYnwpnARSS51QzBhar5Bt</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627377844\", {\"maxDepth\":0})</script>\n"}}