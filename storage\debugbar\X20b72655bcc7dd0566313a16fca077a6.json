{"__meta": {"id": "X20b72655bcc7dd0566313a16fca077a6", "datetime": "2025-07-31 16:43:58", "utime": **********.919467, "method": "POST", "uri": "/finance/sales/store-subscription", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:43:58] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/finance\\/sales\\/store-subscription\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.896416, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753980234.40802, "end": **********.919569, "duration": 4.51154899597168, "duration_str": "4.51s", "measures": [{"label": "Booting", "start": 1753980234.40802, "relative_start": 0, "end": **********.044397, "relative_end": **********.044397, "duration": 3.6363770961761475, "duration_str": "3.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.044446, "relative_start": 3.6364259719848633, "end": **********.919578, "relative_end": 9.059906005859375e-06, "duration": 0.8751320838928223, "duration_str": "875ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53502560, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST finance/sales/store-subscription", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@storeSubscription", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.store-subscription", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1287\" onclick=\"\">app/Http/Controllers/FinanceController.php:1287-1403</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.06122, "accumulated_duration_str": "61.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180053, "duration": 0.02264, "duration_str": "22.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 36.981}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2471938, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 36.981, "width_percent": 3.577}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.333386, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 40.559, "width_percent": 1.829}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.397606, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 42.388, "width_percent": 2.646}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4979851, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 45.034, "width_percent": 5.946}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.550869, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 50.98, "width_percent": 4.851}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.597456, "duration": 0.027039999999999998, "duration_str": "27.04ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 55.831, "width_percent": 44.169}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/store-subscription", "status_code": "<pre class=sf-dump id=sf-dump-1982767901 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-1982767901\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-295833705 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-295833705\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1313124840 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>customer_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">lead</span>\"\n  \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>customer_phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+916896589568</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-31</span>\"\n  \"<span class=sf-dump-key>product_price</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">offline</span>\"\n  \"<span class=sf-dump-key>down_payment</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>discount_amount</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>total_emis</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>billing_cycle</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313124840\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-85431256 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1631</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryHzK2J9PM6nyjOR5w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImFNUkg1aWJtb0dFb0pkN3F1Y0Fnb3c9PSIsInZhbHVlIjoibEVETmcvMTNPN0Z0dTl4M0dlNFlUL2UwcnhaQmxLOU1ML1ltN2JOdzFLVDFneGdhdmxVTkRhTUE0V0tuTGN6QWZ2UC9hdWQxNHpaRjdyK0I1cFp5YThwTXZucTUyUm1pa1AzRWlTWTVtYzd4blY3UUJiZUhzSjd1MDE3anIrUjB1OFBTOEMvTWt3VUtRMk0rUldoK2owOFlnUjc1N3Qwa0Y2azgzdTZ5NVI3a2pWMVlROGJrUE50d05HUGgreENyZjQ4NTYyazNVV1dLcDJ2emxPU3BHYTFoSlQ0N3FxVDRmaUpOVUFvZ3FHcUd3b3hIVU83SCtsOFhkNERvend5TzErV05oZXl2ZURjTXFaMVd5M0dYOVN0ekttSlB2RWRpUlhqcXN1UUVZZ1lleHYrOVA2V2R1THpSR21QTkVURjBBMks0dStwaG8rK0RuMGZXdGRIT2Nnb2R2N0QxZHNHSXlDL1hBaWFweW91blZxcHUybUFodkx2M2JzUGUvZmVXN3gzNVJDa1phZXVqeXJLVnJobEpPR251c2NMR0c1dkRVQlFmVjZHRTBJcHhEZS9hMDByRTMzVUlvTStLdVJDbnpPV0pWYUxUc0QyOW9nL1pUOFdTRVRPbHBpTUN3ODVQcXhuNkNJV3pPNEt2ZjJtSkszbzh1RmNBTkRCZVpyYXoiLCJtYWMiOiI0MTJiODMyOGFkMDZlNTY3ZjI3ZWIxMTA0YzJmNzg0MzlhYjQ3ZmRjYzI2ZmRmMzU0OTJkZTNkZjNmOWMzODE0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlR2STlzeW94ZkoxT1RFalRVV2V5bFE9PSIsInZhbHVlIjoic1NyRWxBbElSQldCWm4zbXZudkFheVZrRXFNaVlISWZMOHVkckpHOFNDVCtNa3RVaXFON2VXMWMzRWZ1dUU3WHZvekdJVlpGUFpJazZMYWRaWWNhR2FoM21aaWVvUHJ5ZHlmd3Urcm5iTGMwVThXWEl0aE1ybjNmT296a2phN3NIRXo4WjV0bjJCbWFnM2ppb0RrTWJvT0RFaG9lcTdtdmQ0S2Z5VStXZzY3a2U1T096U1J1T1FROFJrQ2hqY1U0aGZlWXdrbTQ3WFRNTHJWTHBsWnUvZFNNS21vZjJEMFVSNk5IUUVmNmRZT1JEaEh6RTZid1hncDY3N1Q2SmZ1VkpkUGpubHhNcHNub3RBSDFCZTltQTE1Z3A2SjI2QUJldHhSQnB2b3ZtRWc2ZlQ2blpHSFZ5cGUxNEpTbXVyL3NvVWVPczVjMVN6eUtYUSt2Vzl5c1haVWg3blNBUDQzQlAxUDV0QTRYUjkySE5ZQlpHMVBsTDFyclZVRFQzbTV3YW1OSk1BbkZTWlRPRXFuZFRoRGNLOUd0TmRPNjd2WVl0TVptTUtKaTVGWWVvaUlpTUFhVlM1eU5RaHl3OGJXRUd2ejRiUWdGclh5UGpNVXdFRk9FTFZEb0hEbVhZZnFOeEtWdUNHSmZKYnFVY3RVam44TVAzWDZYcG1XUk8xMHQiLCJtYWMiOiIxZmM1ZTM3ZDI0YTJmOWI1YTQ3ZTFmMjM2MWVhYjVmOWU0M2JhMTg3MWRlZGQ5ZWVlNjQzZDM5YjFiZGJhMTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85431256\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1420567468 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:43:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1jeCsralY3U2VTTE1JLzMzK21aSUE9PSIsInZhbHVlIjoiTGFtQnNIVlA4NnBQamdSK2FSOEdxSWx5SjlTRk9QR3MwcnlkemgxOGhlTVFhbzNUUVdiTVZKQ2Y2RXBCczVKV0pLbjJlanJHdlBWRysvcGhMMHhDMFRCWi9uZHBZVlpyR2Y1U2hnYUlvYWlWOXVZckQ5bEd2QVkyZUFHOXM4bFBuaEhoZGpycU04NTZOM21RM0ZNSXJSNE9IclNneGJna2w3cFk2QmlYRmJ0Umt5SkVDREVKMVAzckR3dS9Dem9OMzQ1R21WRHNZUkN6aElwL1RwWUNVVkVQN2lZS3NMSzZ0MFRHUzFlbFhiUExEOEwrSlF5UCthM0JPdjVsKzA0cUQyaWc5V21WeURUR2pmeWx6Yk1JSWc1NXlYUTUvcWw0eG5NazVTU3ZJUWdNdk5IMVZTbk1HejhuZVJic0JKelVCai9QOHE3ekJPcjVENmRQRldWMGxaRi9xSXc0cE5JSkNuUHNuVjl2aEYyVEdmVFYwN1lrNk84QTRlTEdxc3pJdUlSZzUwNkRJWk0vL1J1RkxrM25xY0JMU2FRMVVCNlRTQzhTeGh1NDRhV2l4anhDUWVoZ3JsZll4SmJnY1A1WXc4eFlMcHZGZXFnZThUWS9nMzZvczNkVnFsaEFURmpCcTRuditmL3V0eURDSWRUTGdrSlFSbm5xU1c4VXJKMEgiLCJtYWMiOiJmY2EyYzE4MzI5MGM1NzQ2NzY2ZjQ4ZWEzMDczMGNkYjAyNzgwYWNlMWEzMGM2MDllMTYwOGNlNTNhZjU3MmMyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImZLWjF6WEg4TXltd002YkNqZkEwQlE9PSIsInZhbHVlIjoiZ1lJb1BlbmxWNHZqY0Fyb3BQd0xXNTJ3RmZrSHA2SVg2R1pXRVJXU0h0aWVjYnp6Rmw2eGJvd0IvYis4VTlzTTN3SWx4ck5lL0xhVUo1RkVGczI0a1ZMbVEvZlNINm4rajZrZnN5bmozSWtPanBQUU1OaTRIYkQrbWJjeU40KzhmdjRwb3d3VjJBSUhGNExadXRKck9JNGZzN2dua2lhZngrczZ3RG5nQnhCQ0l0R01BcFdRMUZ1cVQyaXQ3TEV0UW1OSTZrNk03TmtuNFc3amVKV3JzdElrM3FFaytNczNmS2pFdHM4MWVVRmgzU1VsL2xBb1lXbW9LcWJvVzZhNUs3TVBGbFhmOGMxTXVtYTYyZ0ZRNjd2T0ROendwZG1OZVNsWnZMSnJ0d3N6VlZWM1JvdTM2SjdhamtoRmlmaEV3dnYrUFhNY2VYRFRoM0l2c1gzdFVUNGpEZ2VFRHRKR0ltMEt1Ri9WdDhqWkdkVGtRYlJlOWk0ZlpwTWN4NTYrWWFUR3g2UVZPdlJha2Q5UzhEeUtaNWpSdlFQTmFXZUxUR28yTXNuNWpXRXNEbjlZUElJK0xwU1FlMjdaKzlSVEtPbDBUYTVacWh1eUdIeXhOWlZ4Ky84QnhGb2VrQ1Q2NWhrUWhnMXYvM05KVjhpb29hNG9LR1crVUdYNGYyanYiLCJtYWMiOiJjMTRlODhiZWQwYWZlMDczZmQ3Y2Q2YmZiZTk3NTc2Njg1MzkyYTBhMzczNGYwYmEwMjZiYmUzMDBiNjcyZDA3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1jeCsralY3U2VTTE1JLzMzK21aSUE9PSIsInZhbHVlIjoiTGFtQnNIVlA4NnBQamdSK2FSOEdxSWx5SjlTRk9QR3MwcnlkemgxOGhlTVFhbzNUUVdiTVZKQ2Y2RXBCczVKV0pLbjJlanJHdlBWRysvcGhMMHhDMFRCWi9uZHBZVlpyR2Y1U2hnYUlvYWlWOXVZckQ5bEd2QVkyZUFHOXM4bFBuaEhoZGpycU04NTZOM21RM0ZNSXJSNE9IclNneGJna2w3cFk2QmlYRmJ0Umt5SkVDREVKMVAzckR3dS9Dem9OMzQ1R21WRHNZUkN6aElwL1RwWUNVVkVQN2lZS3NMSzZ0MFRHUzFlbFhiUExEOEwrSlF5UCthM0JPdjVsKzA0cUQyaWc5V21WeURUR2pmeWx6Yk1JSWc1NXlYUTUvcWw0eG5NazVTU3ZJUWdNdk5IMVZTbk1HejhuZVJic0JKelVCai9QOHE3ekJPcjVENmRQRldWMGxaRi9xSXc0cE5JSkNuUHNuVjl2aEYyVEdmVFYwN1lrNk84QTRlTEdxc3pJdUlSZzUwNkRJWk0vL1J1RkxrM25xY0JMU2FRMVVCNlRTQzhTeGh1NDRhV2l4anhDUWVoZ3JsZll4SmJnY1A1WXc4eFlMcHZGZXFnZThUWS9nMzZvczNkVnFsaEFURmpCcTRuditmL3V0eURDSWRUTGdrSlFSbm5xU1c4VXJKMEgiLCJtYWMiOiJmY2EyYzE4MzI5MGM1NzQ2NzY2ZjQ4ZWEzMDczMGNkYjAyNzgwYWNlMWEzMGM2MDllMTYwOGNlNTNhZjU3MmMyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImZLWjF6WEg4TXltd002YkNqZkEwQlE9PSIsInZhbHVlIjoiZ1lJb1BlbmxWNHZqY0Fyb3BQd0xXNTJ3RmZrSHA2SVg2R1pXRVJXU0h0aWVjYnp6Rmw2eGJvd0IvYis4VTlzTTN3SWx4ck5lL0xhVUo1RkVGczI0a1ZMbVEvZlNINm4rajZrZnN5bmozSWtPanBQUU1OaTRIYkQrbWJjeU40KzhmdjRwb3d3VjJBSUhGNExadXRKck9JNGZzN2dua2lhZngrczZ3RG5nQnhCQ0l0R01BcFdRMUZ1cVQyaXQ3TEV0UW1OSTZrNk03TmtuNFc3amVKV3JzdElrM3FFaytNczNmS2pFdHM4MWVVRmgzU1VsL2xBb1lXbW9LcWJvVzZhNUs3TVBGbFhmOGMxTXVtYTYyZ0ZRNjd2T0ROendwZG1OZVNsWnZMSnJ0d3N6VlZWM1JvdTM2SjdhamtoRmlmaEV3dnYrUFhNY2VYRFRoM0l2c1gzdFVUNGpEZ2VFRHRKR0ltMEt1Ri9WdDhqWkdkVGtRYlJlOWk0ZlpwTWN4NTYrWWFUR3g2UVZPdlJha2Q5UzhEeUtaNWpSdlFQTmFXZUxUR28yTXNuNWpXRXNEbjlZUElJK0xwU1FlMjdaKzlSVEtPbDBUYTVacWh1eUdIeXhOWlZ4Ky84QnhGb2VrQ1Q2NWhrUWhnMXYvM05KVjhpb29hNG9LR1crVUdYNGYyanYiLCJtYWMiOiJjMTRlODhiZWQwYWZlMDczZmQ3Y2Q2YmZiZTk3NTc2Njg1MzkyYTBhMzczNGYwYmEwMjZiYmUzMDBiNjcyZDA3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420567468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-154860771 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154860771\", {\"maxDepth\":0})</script>\n"}}