{"__meta": {"id": "X6b53cd0c5e150b3efb62ca3bd8710cef", "datetime": "2025-07-31 16:58:17", "utime": **********.489608, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981095.945902, "end": **********.489652, "duration": 1.5437498092651367, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1753981095.945902, "relative_start": 0, "end": **********.280526, "relative_end": **********.280526, "duration": 1.3346238136291504, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.280553, "relative_start": 1.334650993347168, "end": **********.489656, "relative_end": 4.0531158447265625e-06, "duration": 0.20910286903381348, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01023, "accumulated_duration_str": "10.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.404908, "duration": 0.006019999999999999, "duration_str": "6.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 58.847}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.444163, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 58.847, "width_percent": 11.535}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4562938, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 70.381, "width_percent": 14.37}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.468137, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 84.751, "width_percent": 15.249}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1140558218 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1140558218\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-988385233 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988385233\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791962534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791962534\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1886620707 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdkenNIRkJWbWRaTFVwSjFYTCtJL0E9PSIsInZhbHVlIjoiUDUvV3VHb2FOVzZ5TEJyL0xIOGkza3NIbXU1MXZva0VvY0F5V01OMlFzMmFkc29ZdmtWNmdUWnpPMGt3anlaWmJ3eVpDTDhvTHc0Ry9BNm1DdlpLOGhROEI5YmhqMTZXN29WT3RzdFZOejVSOXFDbDFvY2ExRmdteXBEa0R0N2syZlpqOUN0M1FGQWJPUDdsaTc1bDZtalIyNkFzMzQxVVdPdmR2K092VHZpQWVvWkhWWkk4dWZzdFBSek8xbnljM3RXWVIzQlNUSjFEWitRNW5aWFRJYVpyRnA1Y2c3UGk4VkJpUUFPL2FNTkR2aGdpWGorSTg0Zkp3NGFidVppcFRFQ3YzZ1N5aEMrcmY2NTVtM1YxRGh1emlncXZtZEp6UFRzMFZUMXYrS09VYmo5V1FOT210MXhKR21yQXoxajFCMXo5SFZYREpHK0ZOamtsRGhRWldNajlHNmo0QlpJdWV3ZFd6OE4zUWRoT0dNdUphSmlFNTdNUEh4Q3gyYndGMW9qVTF5M1VWQlBrL013K0hZckpUbmo0VXpKNVAvOUlUK2JSdnJPWDJyejRmUWdyMENNZ3I1ek9FTkh1R2hubGk1TjlEcHRwbW51d2xLY2RsYjJ3WTJvWUdGbnJmODdFcTFNMDdnbUszeEhIY1JiMEkzSlR6K3NOWEVEV0xEL3QiLCJtYWMiOiIxNTRmZjA3ZGYwYTc3MmQzNzg1YmNkZGMyN2QyNWNhOWE1NTYzMWJmYmIzYjJiNjg2M2Q0NGJlNzUwOTRhYTdjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImQzSnBqUnNUeFhoYmwyUnBKTUU0Z2c9PSIsInZhbHVlIjoiTUlWYi8zYXFab21rYXl1NTRmcEkwYklBR3RTdUlaR3FDYzlydjk3ZGxzcDNkeUtyNUdjRnpLSDNqUFp6UW96QUROYlpmVEx2WVhaVDVsc0ExNVczRmRieHN2M29PMWRwSUxwM1RQMG8ydm4yUjFFQTVaZUFuRlhMVWJ3Q0hGQTBScFkreGdYZnQ4V29QVk5rK3hRa0w4dWZrcTVvWjBSNU01UEc3aklSMVBHUHc5dnprNUtRNFgyNmJ4bkpqU1Rib0NicHBpb1NKbEQ5ZWoycGtISzZBaU0xb1dnUWtyU1A2blZlOUgvSFRrRnlzY0h0cXNzdUQzQ0grdlpPQkVLMFVzTHlSbjVsS0hBSVoxMEplTy9aSGV3NDJEUVovN0RLMHdnSnN5S2NPK2RIM1I0NzFaQTNhbkEyN0RTeFJ4T2FrWUdsR0w0aC83cUR4UlJZaW1kUWhYUGlxSGEvOFBTekpSRjZWOXdzTkVpNTJORzBaSytaalEwc211VEx2cWplU2hpZWVGd1FXMW8zTUhmYk5GZUdkVDZDemQrWURFaXJKTk1oek5qd1FvRms3YVJmcVhtS1FoR1Z6anI4bUJSN21veG5aMzdjOTQ0TTlrdVFiMi9BcHJ4ckdBVUZNUHFLUGNkb0E4MGk1UFpYVWZxOVc1MnZYaXpRblp1YXh0SmciLCJtYWMiOiJlMjA5MDAzNzc4ZWI5N2I1OGM0NWM0NTJkYjY2Mzc1NWVmZjJmZmFlZjk5NzBjZTI0Y2MxNTkyZTkwNGE5YThiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886620707\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-22715322 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22715322\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1813961053 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:58:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd5djEyMC9TVzlDdFRkTEozbE5aNHc9PSIsInZhbHVlIjoiOFNzbFQ3eG9ta1JPeVJkVEdUTkdhQmNDZDFTcHl4bFhuTytlWEQ0MEhlcDlzT3p0aU9iTVQrcFpaZk9ScG5hSmhWRklab0hYSUN5dkM4Z29HTnJhSW1NbHZqczRzOVFkZWlKekVmWVNSdmxZdC9XMjBuWm9RdDVBRkpGZk8rc0dBMnplVmxQeUcybGJZVEIrV1hwcFdlL1VMYWFsVWZTNlQ4M1lDMUhETHFEOEpQWC9MRjN3TkNHelZsbTJQL2hIWUFWdmkwZUVhL1Q0NXNwbVpvZzBwamdocjN2RktISnd5SWd0M1pGMCtEMzlOaCtYeGRSU2huK0VaYnk3VFZpc3RBS3hyTWJkVG50RWZzaXVabDRnQzVsM0k0V1JpVURJMlc3bnIrK3UyTU5xbVdvWVBOZ240UmxmNlJiaGwrTkhuQU4yenFRUEZSekxxTVJaR3V6WGQ0SVpBdWl6OUNWZThRVGd0dWl2WmRjQ3hCdDZRUmVVVWgxczlUU2VkYWMwZUd0QlA1ME5BT3ZCdmtZZVZhSWV2UThUS2FtTnlBWUFtREdzRVRveUl5VmxENFNjZ0gxalJ2M0h4Ni9TeERZVUU0NXVWWGZDQkMzQndmczV4RSt2SEZrdGk0NDdHSjJ6eGRmOWsvVHBRUlVZTGVwZlV5dmVTZWcvWWlEN0lWQWUiLCJtYWMiOiIwYzFjYjM4Mjk2YzZmNGE4NzFkY2Q5NzIxMDkzYjM3YTY3MTZmMzE3NzFjOGQ0MjM5NDlmNjY0ZjdmYTk2MjcyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZTaFBXMlZkTnYxK1dSNmZsdGRDNXc9PSIsInZhbHVlIjoiKzd6aFNsazdHVmVCSTlueCtHQWhwM0VFMy9YMVNXMEZlSHROMmZha3o5clhMV1ZBMjlSdUVsOUJXVjcwcU9Ed2ZzVnRLMUl3bmFZNE5HYTBXNUprdkd0c0VMU1cwZmdFOGEvN0w4U081ZzVsUktnbFNNSTJEaldvRUxmQk90dlpXTFlpdWZTTmtTUEhWZCtGZWdzUE9BZDFEOGZKNmN1ejhPMjFnZVNLWmQ3Y3BTdWg4UEJRSVVRMVdEVjA5d2FwYnNSVU5kSG9LUUREK1dTL2VyMTN3dTJzSW5sekdSNkFqaTVYZXVEU3ZsWnRJbmU4UG1UUFpWbDN0NTM5SC9RYytBb0VLRkFnT0VlZFVkdVFpS0FDelp5S01BYnRFZG5hL3ZxRmg4NTZKbW1Ddmorb0ZoVUFzUVp1MFBvSTI2ZmMrbnM4MXRIMGU4OEhWbnRITHZoYktOb0hsVzJLdENBM2dNbWtwVllHYkFQUkkyc0ZUQTAvdVliMjNxd2hJWUtFYkgzLzhjU20vRUtuYnJhUVg3MnhiOG1rdURkN3kzMWM5KzBlUWZra2NBMG1LWDZFRFVGWjdLbU1CQTFLTXFTeUNPWGhjeXU0RDQxamNTVmw5ekRJb2FwQ0w0MDJLMCtWbnpRMEJDVmtWY21FMTVudkJBdnJZbENzQXhGZXlDRFEiLCJtYWMiOiIxZjFmNjNhODYxOGEwNDYyZTAyYjNhN2VjMDcxM2MwN2ZhNWJjMzE4OTA2Yzk5ZTFkZWFmOWIyNDE3ZGE2ZTJkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd5djEyMC9TVzlDdFRkTEozbE5aNHc9PSIsInZhbHVlIjoiOFNzbFQ3eG9ta1JPeVJkVEdUTkdhQmNDZDFTcHl4bFhuTytlWEQ0MEhlcDlzT3p0aU9iTVQrcFpaZk9ScG5hSmhWRklab0hYSUN5dkM4Z29HTnJhSW1NbHZqczRzOVFkZWlKekVmWVNSdmxZdC9XMjBuWm9RdDVBRkpGZk8rc0dBMnplVmxQeUcybGJZVEIrV1hwcFdlL1VMYWFsVWZTNlQ4M1lDMUhETHFEOEpQWC9MRjN3TkNHelZsbTJQL2hIWUFWdmkwZUVhL1Q0NXNwbVpvZzBwamdocjN2RktISnd5SWd0M1pGMCtEMzlOaCtYeGRSU2huK0VaYnk3VFZpc3RBS3hyTWJkVG50RWZzaXVabDRnQzVsM0k0V1JpVURJMlc3bnIrK3UyTU5xbVdvWVBOZ240UmxmNlJiaGwrTkhuQU4yenFRUEZSekxxTVJaR3V6WGQ0SVpBdWl6OUNWZThRVGd0dWl2WmRjQ3hCdDZRUmVVVWgxczlUU2VkYWMwZUd0QlA1ME5BT3ZCdmtZZVZhSWV2UThUS2FtTnlBWUFtREdzRVRveUl5VmxENFNjZ0gxalJ2M0h4Ni9TeERZVUU0NXVWWGZDQkMzQndmczV4RSt2SEZrdGk0NDdHSjJ6eGRmOWsvVHBRUlVZTGVwZlV5dmVTZWcvWWlEN0lWQWUiLCJtYWMiOiIwYzFjYjM4Mjk2YzZmNGE4NzFkY2Q5NzIxMDkzYjM3YTY3MTZmMzE3NzFjOGQ0MjM5NDlmNjY0ZjdmYTk2MjcyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZTaFBXMlZkTnYxK1dSNmZsdGRDNXc9PSIsInZhbHVlIjoiKzd6aFNsazdHVmVCSTlueCtHQWhwM0VFMy9YMVNXMEZlSHROMmZha3o5clhMV1ZBMjlSdUVsOUJXVjcwcU9Ed2ZzVnRLMUl3bmFZNE5HYTBXNUprdkd0c0VMU1cwZmdFOGEvN0w4U081ZzVsUktnbFNNSTJEaldvRUxmQk90dlpXTFlpdWZTTmtTUEhWZCtGZWdzUE9BZDFEOGZKNmN1ejhPMjFnZVNLWmQ3Y3BTdWg4UEJRSVVRMVdEVjA5d2FwYnNSVU5kSG9LUUREK1dTL2VyMTN3dTJzSW5sekdSNkFqaTVYZXVEU3ZsWnRJbmU4UG1UUFpWbDN0NTM5SC9RYytBb0VLRkFnT0VlZFVkdVFpS0FDelp5S01BYnRFZG5hL3ZxRmg4NTZKbW1Ddmorb0ZoVUFzUVp1MFBvSTI2ZmMrbnM4MXRIMGU4OEhWbnRITHZoYktOb0hsVzJLdENBM2dNbWtwVllHYkFQUkkyc0ZUQTAvdVliMjNxd2hJWUtFYkgzLzhjU20vRUtuYnJhUVg3MnhiOG1rdURkN3kzMWM5KzBlUWZra2NBMG1LWDZFRFVGWjdLbU1CQTFLTXFTeUNPWGhjeXU0RDQxamNTVmw5ekRJb2FwQ0w0MDJLMCtWbnpRMEJDVmtWY21FMTVudkJBdnJZbENzQXhGZXlDRFEiLCJtYWMiOiIxZjFmNjNhODYxOGEwNDYyZTAyYjNhN2VjMDcxM2MwN2ZhNWJjMzE4OTA2Yzk5ZTFkZWFmOWIyNDE3ZGE2ZTJkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813961053\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-89553641 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89553641\", {\"maxDepth\":0})</script>\n"}}