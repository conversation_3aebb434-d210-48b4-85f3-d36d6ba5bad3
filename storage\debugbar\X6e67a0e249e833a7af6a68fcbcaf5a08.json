{"__meta": {"id": "X6e67a0e249e833a7af6a68fcbcaf5a08", "datetime": "2025-07-31 15:50:46", "utime": 1753977046.236275, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977021.779894, "end": 1753977046.236332, "duration": 24.456437826156616, "duration_str": "24.46s", "measures": [{"label": "Booting", "start": 1753977021.779894, "relative_start": 0, "end": 1753977024.475817, "relative_end": 1753977024.475817, "duration": 2.6959228515625, "duration_str": "2.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753977024.475884, "relative_start": 2.6959898471832275, "end": 1753977046.23635, "relative_end": 1.811981201171875e-05, "duration": 21.7604660987854, "duration_str": "21.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50717576, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": 1753977041.944822, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": 1753977043.640737, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1753977045.977324, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 11.54011, "accumulated_duration_str": "11.54s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.716469, "duration": 0.013359999999999999, "duration_str": "13.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 0.116}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7808719, "duration": 11.50601, "duration_str": "11.51s", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 0.116, "width_percent": 99.705}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.35794, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 99.82, "width_percent": 0.018}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753977042.964799, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.839, "width_percent": 0.026}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753977044.1981308, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.865, "width_percent": 0.028}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753977045.603469, "duration": 0.00796, "duration_str": "7.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 99.893, "width_percent": 0.069}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753977045.942363, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 99.962, "width_percent": 0.018}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1753977045.957728, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.98, "width_percent": 0.02}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "B9BuRKi058ZQxSlfLVITxXLQYFh37pw4QWOp4fFD", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1130186986 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1130186986\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-572120941 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-572120941\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-613403028 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-613403028\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-646489921 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkF2QkdqNk43VFJVYk5ud3JpenRLa2c9PSIsInZhbHVlIjoiSWRhVldObVV2RGtpKzd4QXVVVk04aStqYXRmb3BLVXRMOW1yOVl0ejV2eWtrRmwxZWtKNFFUMUpjYjVDTWtFVnUyeU5sVWd1d3FEYnJPL3haSldOYzFIQWR1dXZxbnVEU2ZFK2pyMzZsTXJRbkN0RWxXcExFZG5BSk9YdlVHODRjRloxTVRZaHRUQU5vaGtJTnhIb3IzYmxldkY2b3pwb3M1bDZVb0VmbWhGeS9KWGtEOGt1dmJzUE96YnNCQXk2cXlnOXRKdzhOc0Z4TXVzMDdUK2R0NldDNm11V1NHVFlDL3VwMmpTc3BQZEVKQVEvOEhoVnB3Zzl2ZFIvL3kvZW9DQ0c3TmNIdW4yVGpGb2tDMnRxQThRZUNSWXVQcDV2NHJNdkZsdDNEd3BTQlF5MnlNRjRWcG1Wd2pjeG5rZE9NNmU3c09FU2tHVVoxcVlFSzBZVno5d3ZzRC9QOFRHcTFPTnVGRGhTUVhFRDNhMFlUSU4xYWxoMmMydE81Y1JtNktrTkgrZ3FtQUF5M251Ym5hc05pK3Y1UjUyeW9pZzhNaUEzSTFpK0l5aSsrQXhOZE1sLzBEQzg1bWtqRWxpdkoySkNwNVFld0hNMTQ1dGUzc2FGV0dScjdRRTZmOTc2dU1RcDBRVVhwZ09jc0tGUTNlSFVXV0piYkZoQ1hYSDIiLCJtYWMiOiI0NWY0Y2I5ZWQxOTViNTVjYmNlODNlNzBkODY0ZmYxNzAzZDk3OThkMjU0NGY2ZTI3OTRjODlmNTRkZDQxNThjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkRsZ0tBa0tRRkRIVWV0WU5NVERIY1E9PSIsInZhbHVlIjoiSDRIbmxEYnFCSjBOejdYMWlxZzZMSGtGaUN4QzBHV3YyNllqS3JMTnh5V1NsVDZ6WWRUMXpvVlZ6TFQ1WkgvaU9UaFN6ZW9KMCtpMTJNalBIWC9iMVQxWFZBZG1zbkdGejR2Ujlhbjk2clVGU3h3NFdXdmNZWkthcXM0ZkdjOTVqVXN0bG93cERGeENyNWZKaUlYQUZlRGxyV2xFdGEzeEx6UkxEWWlFR3hsVDVwM0o3T0M4enpzQ0xtVzBxQzJiaHlJRHluaFNUTW1FOHRZNjNldnBSM21OVmJuMFV1U3FHZEM3YnhodWdDN1ZoU01lb3FaQnkreWhEeHJlNHltRExlZFNjR3g5MytVb2t6cFNsVENkYjhSaHQ1azRvYnU4VE1jalFwNEFPeDBxT1hBNXpWNXZxQm50MlNvV2krZGIwbGZRZUQ1RzEyU3RVSGIyQXBvNFJyU3lGMlhZN05tVXRiVWRNMzMrNW9aRHRSUmQvcVNBVzQ1dGFNdUE3QVFvQjAyaHpobmduRW1oMWk1MWxnT1d4bDNoZnVDdnAvK0JBTG5uR2ZCZFF5SnF1MlZWMGUyNWFOTklGeWFHWW4zK0QyblZkSlo4c042a0FtNlQ4SDJDWVkrclk3VjhhRmpGTG1jYnVIQ1ByL0ZzZnd1Zy9YUS96WkUzTEVWVkVKRXoiLCJtYWMiOiJiZjFmYjUzNDQzN2ZjM2Y0MTQzOWNjOWY5ZWVhZGFlMWIwMWQwZDY2ZTQzYzA1Yzc3NjNjMjFhMmFhMGEzYjU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646489921\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B9BuRKi058ZQxSlfLVITxXLQYFh37pw4QWOp4fFD</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lb9j6bxKjdpoZRxg6e5NnIijNL0AY4NFEcGmZ5iU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-609172941 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:50:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlYyS1dNRCtmUkVEV3YzaUx0c1pjc1E9PSIsInZhbHVlIjoiNm1JcVQwYldGV0M0ZFNRRWxtMyt0NlV4YXZscEY3V2pHOURUaEhGaFFzQitYSmFUMGZ5MExUaVhvM3FlWWl1V1hQeWFuRVBVOS9UcERZQmJHMXlaMjdyU1lIdG9XWjc5a0krejBiVjJOc085V2t4YzNRYS90R1I3Z0lPU1Fad3o2emlSNlJyZ2Q3UFRFdXN2N3NESGhQc2hpOVN1amZKSmtWQm1DbUlSWmpWUDI4cEZMdzVaK0NjaXdOTnpsQk9DOWptVUNRMDN1OWtYMlI2YmlJZW15NmlsSmU0VkdXY21CWDUzMW5KeldZakhLWEZtUGFaL0JKUUx3ank2NkZjd25PdENtRFdQK2NFRldmQnl4QzhHVFlVRk5QZFZxSTlNY3RpSm9UN2FValN3K2tiTU1uREFVc0hMbXVFbWMxaER6RkRQN1FZZzJnQ0VneU9zUlE5dkJrRThST2ZwemlOdUNsNkRBNEZVQmhTYzRTZ1FIUnNpN3RMLzdTa1JWMkwzTGJ3ZExyMTZBcW9oK2Zkd3A4MkFPYW1EV3lIcktTcEw1cnNpK092NkFBeDFaNTdMdnR5SmhpREpMdE1XU2ZmZFFETUI2dUxDWVJQMmdsRkt4NkpISkFTWTJwRlN2eGpnVFF1MlVWeHI0OFQ3VkR5ekVTdnY0QVpmQngzWWZ3TDQiLCJtYWMiOiI0MGQ5MWJhYzIyZmU2OWNhMzY5NDI5YWQyNTdlMGYzYWJiNDI3OWI4MmI3NGJlYTFiNTZiM2NiYzNlYzNhNWU2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:50:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkRYbEM3QVpFMEZrTW5kNnlrUE1UbVE9PSIsInZhbHVlIjoibHo2eUp0c0lwZnNYbUxoTVlBQ2l4N1ZGK1cxSVpZSHpTREtITFM0cG9sVlFDd3NpTk1iYmxIeGpiTzYyTXJlQkcrMmk5SnJzWmZkZ3JpQzkrYnlycWdlaFdpWHliTGxFYSsxZGhNWS85bS9yVS9Ld2FRSkFjZ0dWcEZEemg5VHI2WDNMSE94dlNhcUVaeXBiSGJrbmFab3Y0enNQU1lWWjV3U0Q0bmZURWFRaFpVa0h0NnpTTHVMN0xYZllMQU04Y0lsTWNwYUJPV1M0VUN4SEMwMHFDbXBmcFJURXoxZUJMSVRJc3VhWGVOSlBqRC9zMFltelArLzRneExjNTI4bWdhM1lnNU9EazR5aVJ6WGd5dTh3cEhCTlZCaXZjS2NvOXFhSjFkejBBZmRyYkkyVC9lQ1dQYlBXZ3ltVnpMcHRsc2RxakFSVm5KN2o0Ky9TZjZvcVVnQjg1dTZUT1JPdDBQRmNBbkxNVWVwQmxBUkdLMjZzdkllVHNnOVoyK0hpRUdpMWxKQ2NoNmlkbGJQbHpjYjRIK01PWXRubjJySm9EQ1B0Z1Vsb0JXVmV5UFFRSjZ3NmJkZ1NEVXBCUCtyRVVZb21QYkNFL215d0JnMjlDZTM4NVgvSG96WE1vYnl1R0JGZW1zSzB1QXBjUTZxUWdlL2dXdFc0RlZhZ2pYNloiLCJtYWMiOiJhMjU4MTA0ODZiMDdlNTZmMTY0MWEwNjkyMzQ5NGFhN2JlYjU4ODA3YmRlNzkyNTY1NDVmNDAxNDY0MzRmZjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:50:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlYyS1dNRCtmUkVEV3YzaUx0c1pjc1E9PSIsInZhbHVlIjoiNm1JcVQwYldGV0M0ZFNRRWxtMyt0NlV4YXZscEY3V2pHOURUaEhGaFFzQitYSmFUMGZ5MExUaVhvM3FlWWl1V1hQeWFuRVBVOS9UcERZQmJHMXlaMjdyU1lIdG9XWjc5a0krejBiVjJOc085V2t4YzNRYS90R1I3Z0lPU1Fad3o2emlSNlJyZ2Q3UFRFdXN2N3NESGhQc2hpOVN1amZKSmtWQm1DbUlSWmpWUDI4cEZMdzVaK0NjaXdOTnpsQk9DOWptVUNRMDN1OWtYMlI2YmlJZW15NmlsSmU0VkdXY21CWDUzMW5KeldZakhLWEZtUGFaL0JKUUx3ank2NkZjd25PdENtRFdQK2NFRldmQnl4QzhHVFlVRk5QZFZxSTlNY3RpSm9UN2FValN3K2tiTU1uREFVc0hMbXVFbWMxaER6RkRQN1FZZzJnQ0VneU9zUlE5dkJrRThST2ZwemlOdUNsNkRBNEZVQmhTYzRTZ1FIUnNpN3RMLzdTa1JWMkwzTGJ3ZExyMTZBcW9oK2Zkd3A4MkFPYW1EV3lIcktTcEw1cnNpK092NkFBeDFaNTdMdnR5SmhpREpMdE1XU2ZmZFFETUI2dUxDWVJQMmdsRkt4NkpISkFTWTJwRlN2eGpnVFF1MlVWeHI0OFQ3VkR5ekVTdnY0QVpmQngzWWZ3TDQiLCJtYWMiOiI0MGQ5MWJhYzIyZmU2OWNhMzY5NDI5YWQyNTdlMGYzYWJiNDI3OWI4MmI3NGJlYTFiNTZiM2NiYzNlYzNhNWU2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:50:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkRYbEM3QVpFMEZrTW5kNnlrUE1UbVE9PSIsInZhbHVlIjoibHo2eUp0c0lwZnNYbUxoTVlBQ2l4N1ZGK1cxSVpZSHpTREtITFM0cG9sVlFDd3NpTk1iYmxIeGpiTzYyTXJlQkcrMmk5SnJzWmZkZ3JpQzkrYnlycWdlaFdpWHliTGxFYSsxZGhNWS85bS9yVS9Ld2FRSkFjZ0dWcEZEemg5VHI2WDNMSE94dlNhcUVaeXBiSGJrbmFab3Y0enNQU1lWWjV3U0Q0bmZURWFRaFpVa0h0NnpTTHVMN0xYZllMQU04Y0lsTWNwYUJPV1M0VUN4SEMwMHFDbXBmcFJURXoxZUJMSVRJc3VhWGVOSlBqRC9zMFltelArLzRneExjNTI4bWdhM1lnNU9EazR5aVJ6WGd5dTh3cEhCTlZCaXZjS2NvOXFhSjFkejBBZmRyYkkyVC9lQ1dQYlBXZ3ltVnpMcHRsc2RxakFSVm5KN2o0Ky9TZjZvcVVnQjg1dTZUT1JPdDBQRmNBbkxNVWVwQmxBUkdLMjZzdkllVHNnOVoyK0hpRUdpMWxKQ2NoNmlkbGJQbHpjYjRIK01PWXRubjJySm9EQ1B0Z1Vsb0JXVmV5UFFRSjZ3NmJkZ1NEVXBCUCtyRVVZb21QYkNFL215d0JnMjlDZTM4NVgvSG96WE1vYnl1R0JGZW1zSzB1QXBjUTZxUWdlL2dXdFc0RlZhZ2pYNloiLCJtYWMiOiJhMjU4MTA0ODZiMDdlNTZmMTY0MWEwNjkyMzQ5NGFhN2JlYjU4ODA3YmRlNzkyNTY1NDVmNDAxNDY0MzRmZjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:50:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609172941\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-416866674 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B9BuRKi058ZQxSlfLVITxXLQYFh37pw4QWOp4fFD</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416866674\", {\"maxDepth\":0})</script>\n"}}