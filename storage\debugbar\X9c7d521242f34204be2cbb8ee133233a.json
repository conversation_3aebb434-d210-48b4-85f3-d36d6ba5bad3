{"__meta": {"id": "X9c7d521242f34204be2cbb8ee133233a", "datetime": "2025-07-31 16:01:46", "utime": **********.288347, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977703.010611, "end": **********.28842, "duration": 3.277808904647827, "duration_str": "3.28s", "measures": [{"label": "Booting", "start": 1753977703.010611, "relative_start": 0, "end": 1753977705.951127, "relative_end": 1753977705.951127, "duration": 2.9405159950256348, "duration_str": "2.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753977705.951167, "relative_start": 2.940556049346924, "end": **********.288426, "relative_end": 5.9604644775390625e-06, "duration": 0.33725881576538086, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47012640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01528, "accumulated_duration_str": "15.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.17473, "duration": 0.009630000000000001, "duration_str": "9.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 63.024}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.226122, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 63.024, "width_percent": 12.631}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.245094, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 75.654, "width_percent": 13.678}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.262351, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 89.332, "width_percent": 10.668}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1807906042 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1807906042\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1729542554 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729542554\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566035509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1566035509\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-568515411 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjBLWTJScStxWkJFc3RjQ09PVDFSRHc9PSIsInZhbHVlIjoiazlyQk1DYlJybW9tZ2hjUlFoMmxwRjI4cW9uQ09VclJ2MzdNQ2dyQnNKODNPNUVPS25zc1FnSWlyV3FUOHFIam9xU09oSFpHWTYyeHR0a0Q3bUpHNklSbElvN2JlaXVJeHRWd29kb0wvQXVCSTRQbVhVK3B0NU81bmcyTVd0bm1FS0gyMFNSNTlGbkJHOGtCUmNPR3VZYzJXYVoxd2Z5c3crYitLN285S0ZDRk9NQ1NnUU5Nd2dTeGVkMkExRHBaeVZqUGprUGkrUENkUytwWWhHOXUwdllzbzRQK1Z3M1VFUHY4VDB0WnlPNXBYY1dJeEk3K283d0wyTFdESkZNTEwxZG5zR1NMTHg1c0ZCSXNVd0tOMVJDRS9xcHJaRU00b0VQd2JoWXRxS0Fwd1NtcDVhdisvUFNyNjRxMm9lSHRxVzBPemhHdW1zZkJTdkk0TS9NUkJrWnFUdU9NZzZqZE5RQnBSNDFIQlFFUjFvTVFsblBTUHRRcGFQN09JSGYvT21lRzl2YklJVEI0eC9jaU5Zc1NzQ2VxSWJkTHpQampmSW5OU3h6S1ZXK0pSR1lCbFhZS2NUSjJrQlBBYms4a0V3dlBIZ3JxT01pZm9kZGpMWHdFa3FYcDRKbGpKTjhkRnh6YlNNd1p1eExIem1JOEt4QlV2NlNBckFvbEVtM1EiLCJtYWMiOiJlNTlmYjk0MDUyMmJkNTgzOWIzNTM3MDg3ZDlkZDcxNjJjMmU2ZjYxMDk0MzMxZjA5YzkwN2IzZmRkMjVkYWMzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFSL1NwNnpBTE43TnBGakFuaEprMXc9PSIsInZhbHVlIjoiWHpEaFdQSGt5Y0xYNHpaMTM2MnIzY1Y3cEJLZ2NZMHlROXJGUUFHVGRReXNLd1RJeWJKYmpPN0NQYjhXN2Q2RUJiNGlhU3ViRTJiQUlLd2RNNlBVa3JxR0R1RktSbkt6UXJaN2Z6Wnh4ZWRDcDQ2TU5vS2wxQVkyelhwU3FlTHp0RmR5SzdCODFheEs3V0ZoNy9ZTU1rR3dWeWZWY0tjTk1NQm93VDRHOG1ESVR3REYrbHdzdU13U1R1UjZjU29Bc3g0SE5rU3YwQzlxK2hWaDBGWmdWL1VscmxlMHRXUXcvTndOcXBwNGxmbm1QdEZYaXNyKzFmcUtZTDFsMGZEU09rb2hkZDlSVmlEOU9wWXBOL2wweTQxZTZ3YWx2Ukg1M3JEQWtZNFR4VHZxdzFoNS8wdDdLMzBVR2JFY0J4bVYyOHlHUHpjZHRMUXZTZ21VbXdtZGdaSUhScnNva3VUMTcyTnNOb3lKQUE2Nm5FaWRKV3c0K2xuRVdYRDNRbkFBdzdueCsxaHhyeXY5YUdTWGpDTHJrR1dkOG5BNC96NG5DQzdqSTJiTUx4L0pLdEtNbFFFc3pJTDR0NkZlMnhmRVBEVnUwUkVEYmRxNXQxcmFDS2VlZHRoS0E1d0x1ZWtsamswaVpacmtCOGNkcTF6VHZhQytDWExsdFkyVEpnbEgiLCJtYWMiOiI2NWU1NzI4ZGQxZjM5NjQyNGNjMzRjNGExZGE2NzFhZTg4ZWMzOTZjMmU0MGVmYzljNDY2NGM0MDJmMDNjMmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568515411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1491401974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491401974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1251570572 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:01:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImllRjZSY2VvL2RlS2NWWmtMMTgvSXc9PSIsInZhbHVlIjoiTFZFL3I0ZW5MN0ZmblpacHBMQ3NTQUZBVE5UNGJGMmwyT2pzWHdOakdvdGMxY3lmMHNxYVgrQmt5MjRrc2FTTzZpOHlTUnFrczJ6VFZuNkVaeGtqWlMweW9yMUQxMXN4akc5K3UySmdSQkw4OHMxemJ4TkV6WVF3ak1nOHRiQTBOWDJWeElva0NiUE1VcUFHRWZraitxT3V0YmkvVDVQeVp0c24xMzIvOUxQajRZdHptdkYxZis1K3dYNVhQZWhMbDlnbnNpcGZ2Q2pBU3F0WGEzTDljc1hmVHZzR01Gc0lOZHhXZ05vaGRSRnlmUDljNWVRc0tKcGhLdk14VWJ6ZDdvbDVSOTU0Q1JpUTlaTUdCK3p0SXdabUdrNG9tNVFKcXNobVdMVDZTbjRDM2hCZmNmaFpuZ1pVMFhUWDJXMFJBWWhxbmZyYmQ2UzNFZGtLMW9Ld0FGdWR2eXhDY1VkTThwQkk0bThYK2JGVER2VzZOSWNGblBkRWM0aTZrcS85akgzWGpqQkU1ZU45b2YvL1h6ZUpydHp2MitobXEzbGR4aE80SWNhSFluelRkMnN4dzRrb21mTWdxbXZUZXg3a2tEeHFCZjU1OEV6c3A4M0w0QitjZDlUTVQ3dC94MXpoQkwyZjB6VFlsTWpZZ045SHoyRXZmMWpMbjhQUkQ2RDkiLCJtYWMiOiJlMzRhMjEzMmEwOGE0NjZhZTAzNzhjYWIyNGJmMGYyZDkzNjU3ODYwNjM3MWE2MmJjNWYzMzE4OWQ5MGY1ZjBkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:01:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9FeFZOY3VrQ2Nkak9nWnJvOTNEdGc9PSIsInZhbHVlIjoiZUR6eDRlWk1yaEp3T01ud1QzSy9wU3Z5M0VVMzZ1elhMa1pYYngzZ21LeE9FQUlaNkw0TnIyWFhiQ3RNejRSRVRUcThUTkdzY2xpekJuNEVTRnAwMkxiTDdQajhJdC9WVW5WQjVmdGUxN2lYVkV2dUdQd0psOHBEZFpENllWb3VUWHhobG1wcm1XU09JdDJOLzdxUHBPNGx4d2E2MmFGNXZDZzhGVEtBcm15cDZpVU42YVdkRDhiLy8xcFVEdkhsaUFxQTZaUnAvbjNVTEtmR2QrcTFvbHR2VVdJUHNXZE56aWkrRm56TFA3enNOSGdMRTNlK3BtR01lMXZrNE9vQzh2M2wxRGR2QlhVUDNUNk9LdHJiWHYyRmdmaXNyRVhhTlBjTVVHaXhzMVFrR1h2R0RPWjd2OGRmd2hSc0w0bXhpRGdVaUhCd0E2STFZYVdldzdQMlpiblBOVkw3REFTLzlxUklzREQzUTJYMlh1R2JSMS9MQW1wUDVHeUNDd2lIL2Uxc1k5a2t3U2FyampMQmVCZFc3UW1SK1hiREcwS0svN3lmQUhYWHRYNFBLd2gvK05xMnhJUjl3ODRKQWhWRE4wRXhVYVFyV1hrdURQa2RkZzlvL1ZYWnNhejQ2THZhN2FOTXB2WGFyS0MrdXlGLzlLN0tnL0Y1YXVRdC9yd0MiLCJtYWMiOiJiMDQ5ZjRjM2QzYjM0OTk4ZTk1OGRiYmQ0MWIyNzE0MjJiYTdjMmUwMWVjZjc5NTVmMTM2Mjg0ZGE1ZGRlN2U5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:01:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImllRjZSY2VvL2RlS2NWWmtMMTgvSXc9PSIsInZhbHVlIjoiTFZFL3I0ZW5MN0ZmblpacHBMQ3NTQUZBVE5UNGJGMmwyT2pzWHdOakdvdGMxY3lmMHNxYVgrQmt5MjRrc2FTTzZpOHlTUnFrczJ6VFZuNkVaeGtqWlMweW9yMUQxMXN4akc5K3UySmdSQkw4OHMxemJ4TkV6WVF3ak1nOHRiQTBOWDJWeElva0NiUE1VcUFHRWZraitxT3V0YmkvVDVQeVp0c24xMzIvOUxQajRZdHptdkYxZis1K3dYNVhQZWhMbDlnbnNpcGZ2Q2pBU3F0WGEzTDljc1hmVHZzR01Gc0lOZHhXZ05vaGRSRnlmUDljNWVRc0tKcGhLdk14VWJ6ZDdvbDVSOTU0Q1JpUTlaTUdCK3p0SXdabUdrNG9tNVFKcXNobVdMVDZTbjRDM2hCZmNmaFpuZ1pVMFhUWDJXMFJBWWhxbmZyYmQ2UzNFZGtLMW9Ld0FGdWR2eXhDY1VkTThwQkk0bThYK2JGVER2VzZOSWNGblBkRWM0aTZrcS85akgzWGpqQkU1ZU45b2YvL1h6ZUpydHp2MitobXEzbGR4aE80SWNhSFluelRkMnN4dzRrb21mTWdxbXZUZXg3a2tEeHFCZjU1OEV6c3A4M0w0QitjZDlUTVQ3dC94MXpoQkwyZjB6VFlsTWpZZ045SHoyRXZmMWpMbjhQUkQ2RDkiLCJtYWMiOiJlMzRhMjEzMmEwOGE0NjZhZTAzNzhjYWIyNGJmMGYyZDkzNjU3ODYwNjM3MWE2MmJjNWYzMzE4OWQ5MGY1ZjBkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:01:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9FeFZOY3VrQ2Nkak9nWnJvOTNEdGc9PSIsInZhbHVlIjoiZUR6eDRlWk1yaEp3T01ud1QzSy9wU3Z5M0VVMzZ1elhMa1pYYngzZ21LeE9FQUlaNkw0TnIyWFhiQ3RNejRSRVRUcThUTkdzY2xpekJuNEVTRnAwMkxiTDdQajhJdC9WVW5WQjVmdGUxN2lYVkV2dUdQd0psOHBEZFpENllWb3VUWHhobG1wcm1XU09JdDJOLzdxUHBPNGx4d2E2MmFGNXZDZzhGVEtBcm15cDZpVU42YVdkRDhiLy8xcFVEdkhsaUFxQTZaUnAvbjNVTEtmR2QrcTFvbHR2VVdJUHNXZE56aWkrRm56TFA3enNOSGdMRTNlK3BtR01lMXZrNE9vQzh2M2wxRGR2QlhVUDNUNk9LdHJiWHYyRmdmaXNyRVhhTlBjTVVHaXhzMVFrR1h2R0RPWjd2OGRmd2hSc0w0bXhpRGdVaUhCd0E2STFZYVdldzdQMlpiblBOVkw3REFTLzlxUklzREQzUTJYMlh1R2JSMS9MQW1wUDVHeUNDd2lIL2Uxc1k5a2t3U2FyampMQmVCZFc3UW1SK1hiREcwS0svN3lmQUhYWHRYNFBLd2gvK05xMnhJUjl3ODRKQWhWRE4wRXhVYVFyV1hrdURQa2RkZzlvL1ZYWnNhejQ2THZhN2FOTXB2WGFyS0MrdXlGLzlLN0tnL0Y1YXVRdC9yd0MiLCJtYWMiOiJiMDQ5ZjRjM2QzYjM0OTk4ZTk1OGRiYmQ0MWIyNzE0MjJiYTdjMmUwMWVjZjc5NTVmMTM2Mjg0ZGE1ZGRlN2U5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:01:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251570572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1810958134 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810958134\", {\"maxDepth\":0})</script>\n"}}