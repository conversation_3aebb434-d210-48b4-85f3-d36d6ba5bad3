{"__meta": {"id": "Xcb0d6783c6a1c317bc44641967d03538", "datetime": "2025-07-31 16:21:57", "utime": **********.922817, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978916.178022, "end": **********.922857, "duration": 1.7448351383209229, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1753978916.178022, "relative_start": 0, "end": **********.670693, "relative_end": **********.670693, "duration": 1.492671012878418, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670718, "relative_start": 1.4926960468292236, "end": **********.922861, "relative_end": 4.0531158447265625e-06, "duration": 0.25214314460754395, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47018080, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01143, "accumulated_duration_str": "11.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.783803, "duration": 0.00617, "duration_str": "6.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 53.981}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.857231, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 53.981, "width_percent": 18.81}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.880723, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 72.791, "width_percent": 12.861}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.896196, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 85.652, "width_percent": 14.348}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1965753480 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1965753480\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-881994795 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881994795\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-922427352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-922427352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1834646607 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlY5MzhOTldsMmdJNFlVSVBMLzJSWmc9PSIsInZhbHVlIjoibVM2L2dJTUdtcVNKUXczRHJjai91WERVMmE2b2RLS1dWd1ZoYXpSZnlsM242N3hTdnZ5cHJrUmZyMjhPZGlQYW1PSHdFcVZ4WjdvZUxiSC9OZzNDM3NTbWVUWDVxeStZVkxVMkhURnpaT1ZkWStHQ2xzWmZPM3NqS0pWVlk4SFB5UHZkTHBjbVhyclRoSmpyRTMrK1ZhcnFldkM3UmxqYnk1L2g5QWhOZlNJWUNyZHhKWTIycExrN29xY1lGK0VneVByYlc4ZkVxU3BraWo1N1RXeUUrT210T0djSXpuZmRSUEhENUVsM1RCSUZialNMWFlvWE9ReFZySzZqdTJKaklPayt4Z1JiR0RFTTlKU1A2Zmt3VC95cHUwRmR5Q2hJZXlJZi9zb3JJVXBPdVZmOWhsblpnT1IrL1ZqNnpVempsNU1Lc015Z0toL25YZEp3TUtHM0RnWitLSkp0dG0xZ2VoTjNuYnJOdlNPTE5DeVQwWlpGRmZMeHNnZlRjd2NDSmFMMiswWnk4MW4xcGNsV0pEOTN3K2V3T24yOWFDanZtUnI0SGw1R0Z1TWhWQnl6b25JWUxUZkhlVkVWU201TXRveXpuRWJ6b1ozZGNyRG5VaStHUXlYRVMybmFpdkdVRXFvU29JSnh1YmNYMFRxMms1L2JWV0ZDYmFGVU5yNGUiLCJtYWMiOiIwZDJkZjEwZmM5YzVlZTBkZDE5NTQ3MTNmYWFlMDRlMDQ0ZDMxYTU2NmM3ZGFlYmE3ZmJiMWY4Y2ExYTI0OTc2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjVxeFJ3ckZMNkZlTVQ5YkpmWHlCRWc9PSIsInZhbHVlIjoiYUV1NC9nR0QrUFBkNjdpVGZ6QTdpY3pCdUVOb1gxM3NHaEtRbzl4eVhJcmhhVDRQZ3pHUTU0aVAxU2dXRWpvWENaL204MUdjbXl4d1NHZTV5ZXlkNDNnU3Fqc1dleUExdmtyVDR3RXI1bnNvZUhWL3M3Ykw2Sk9oNGhMU3VMSmJ1RE15eCtBbVdaR3pxbFVzTW1hOCs0OGRNd3AxYm0vQUd1QUIxOHI1WTgzZHRsQlpmMlI1S1Rlamh5YXRpWGJuZ0pOc29aRHhQSjUrcEgxd0U4Uml1ZkhLaFYreFkwWSt3TmdPV2ZqdmtNUzFBemxwZW45ZzhmWTFaZ2ZXQUVpVjdWQVM4VU41NFlDSjExNVBVTTNONENZeWE5cTFQcWVjS1dVaHp6NnVHSk8rcEF6bVdZeWFNSXo1aGZZQmx0MDRhV1pndkdGNVRlRk1FMXRIWTJDQVVSQVF0YXRRVzFaczhILzNpdjJMSjRsbTNXbGNxSnlBZ2NpeTlBaGtoa0pxcG90Y25wV0RCS0FHdDFTenJOMG1yYkpkM0ErL2VzelNOWHpYTmJjeW8rTldKTmFwVzRqUmltaVVSMC9hVTh1eXlGTGlRcVNtRDBOWWNzRWU1YTluMHFZRlcrdWVCQzFBdTlDMXBkbGEyQnd1U2E2cXZybkVSVEJZd3RrN0ZFN3AiLCJtYWMiOiJkMjYxMjBjNzQ0ZDRmYjdhMmMyYmE5YWEwZWUwNmMxNWMwMTc5M2FlMDFiYWM4NGNhYWE0YzllMTg2MTE5YWI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834646607\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1685591864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685591864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2115239259 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:21:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1BRWFiVXpLT3Q0cjNNaVgyby94N0E9PSIsInZhbHVlIjoiNXdXRlZ2Q1ZnZEw5Vy9SQkFEajQ0RnRjaVZVbVFsQXhCdFNpNmNBK1VhMnRScVhGOUQ4UW1YWjhTa3gyM09nZTlYbmVvakVDdWNuejlNS3lCaWdzdEh4MVZ4eEVlbEpYRWpzS2swNkYwbm51ZGpRbExHRGNhd2dlWFd3Nk54ZUlMLy9sYThJaGlNWDc0NlYyZVpDMXpDTytoVlJaRVBmK3JsdHl6VVBCdWtJWXV0NXBvd0ZVcmMrc0NMNFJaOUZHaGNtNWMzaUtqeE1sVHZjUlNtVkVpSmFXK3lDa0dIZlBaSjdzWTlxV0U0QUVWMUFpbjFCUVFrZktlVTJDTXVra2VsZnlJT2FyT3prYXBpZmZBVmNWRThrMEFNYXhaY3F1UlBWSmhaanNJQ25aSXhkN2YwSGgwZHZETFFneFV5UWdzZ3h2djdtMHdqSVpiMXR2dXRPNDBuL0R0N0x4b2VtZEhCa01DOGk3ZjhMK21tcTNkUTl1SHpjWnQ2TThaWnlMWmNncnI1UzNITUM5VmRuajlXUEZsRTBCc1RTNFk5akE3Vk5iR1I5eWJSRkl4VS9KY0J4cXhhNklpMysrM1BZd1NSMERhNDlMV1hkbk8wd2JkTGx5QkQwaS9sYkswL2NYc1NudjJFcGRDSi9DbmtvOFg5QXZJWFNkd09FQ0ZFWmMiLCJtYWMiOiJhYWEwMzgwN2E4Nzk5NTYzODE0N2YyN2FlZGQwZjU0NTY0YzBkNGU3MjQwNDE4NDFlMTk5ZDM1MWM2OTUzOTZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBueGZNTkpyZ1FJRjVodWlyYXlJZEE9PSIsInZhbHVlIjoiZ2MvdVBWQnYxQ3RaSjh4bWh5dW0wenhwdktFZFFWWERhU050c05ZSDFiRHFna1pBU2JHcmg0NGhMZ1FUYmluOEo0SG4yS0hMdnRBcG14ZzV1c2FaSFM1Nk1NR0lkV1dCM1V1Z2xzYmtGTENOTXpmZVVDcmJnZTRtSjE3SnRQa3Z0RHJUaVNiK3RSTDZ4Um0yRGlzcTNPeEp0blpLbWVyNmh6a0tNUlZLQ3lxU2VVS1lpRlBBL1BKVlFOckFEMVRSeW0yUWQremNyTm5Wemg3dGhkL3Z6UUMzV09hL3B3blo5a2xZOEl5bXU4SVZiTHkvRUh0SmZpdjZVUGdXYmNheDVIeTdJVmRKS2ZKRi9Uc0twKzduVGx1cExBREFiQVlFK05hNWJqOU5YMSsrVDVmVlFLWmVRTHZxOGFabHIxK2piVm9aZHBTenZqajF1U0tDb2ZEdEJFNS9rSllreXZxUjh5b1dKdStHQmlrbGg0RWswbmdBeTdDeGVlWGlQVFpOclpjUjVpMEFsbFVPUzBTaFc4eHBxVzAvS1FhWFJnc3BndDZnSkVwNFZXUVV4NXg4TEdLTnppKytDZWNxR1dwOUcrVUx5NXBnbDI2bW5TcVBaWFlXMWI5QXpxM2Iwb2xWSXRFb290dUFhdE9wSzJqSUxsRHdVNi8zT2U2ZWN1MzUiLCJtYWMiOiJkMjcyMzk5ZDg1ZGM2ZTlmZWM4NWJlMmI0NjMyOGVkYmMzMTY1NWI3MjViNzI4NDVjNWE3MDEzMTVjZmMwN2Q1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1BRWFiVXpLT3Q0cjNNaVgyby94N0E9PSIsInZhbHVlIjoiNXdXRlZ2Q1ZnZEw5Vy9SQkFEajQ0RnRjaVZVbVFsQXhCdFNpNmNBK1VhMnRScVhGOUQ4UW1YWjhTa3gyM09nZTlYbmVvakVDdWNuejlNS3lCaWdzdEh4MVZ4eEVlbEpYRWpzS2swNkYwbm51ZGpRbExHRGNhd2dlWFd3Nk54ZUlMLy9sYThJaGlNWDc0NlYyZVpDMXpDTytoVlJaRVBmK3JsdHl6VVBCdWtJWXV0NXBvd0ZVcmMrc0NMNFJaOUZHaGNtNWMzaUtqeE1sVHZjUlNtVkVpSmFXK3lDa0dIZlBaSjdzWTlxV0U0QUVWMUFpbjFCUVFrZktlVTJDTXVra2VsZnlJT2FyT3prYXBpZmZBVmNWRThrMEFNYXhaY3F1UlBWSmhaanNJQ25aSXhkN2YwSGgwZHZETFFneFV5UWdzZ3h2djdtMHdqSVpiMXR2dXRPNDBuL0R0N0x4b2VtZEhCa01DOGk3ZjhMK21tcTNkUTl1SHpjWnQ2TThaWnlMWmNncnI1UzNITUM5VmRuajlXUEZsRTBCc1RTNFk5akE3Vk5iR1I5eWJSRkl4VS9KY0J4cXhhNklpMysrM1BZd1NSMERhNDlMV1hkbk8wd2JkTGx5QkQwaS9sYkswL2NYc1NudjJFcGRDSi9DbmtvOFg5QXZJWFNkd09FQ0ZFWmMiLCJtYWMiOiJhYWEwMzgwN2E4Nzk5NTYzODE0N2YyN2FlZGQwZjU0NTY0YzBkNGU3MjQwNDE4NDFlMTk5ZDM1MWM2OTUzOTZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBueGZNTkpyZ1FJRjVodWlyYXlJZEE9PSIsInZhbHVlIjoiZ2MvdVBWQnYxQ3RaSjh4bWh5dW0wenhwdktFZFFWWERhU050c05ZSDFiRHFna1pBU2JHcmg0NGhMZ1FUYmluOEo0SG4yS0hMdnRBcG14ZzV1c2FaSFM1Nk1NR0lkV1dCM1V1Z2xzYmtGTENOTXpmZVVDcmJnZTRtSjE3SnRQa3Z0RHJUaVNiK3RSTDZ4Um0yRGlzcTNPeEp0blpLbWVyNmh6a0tNUlZLQ3lxU2VVS1lpRlBBL1BKVlFOckFEMVRSeW0yUWQremNyTm5Wemg3dGhkL3Z6UUMzV09hL3B3blo5a2xZOEl5bXU4SVZiTHkvRUh0SmZpdjZVUGdXYmNheDVIeTdJVmRKS2ZKRi9Uc0twKzduVGx1cExBREFiQVlFK05hNWJqOU5YMSsrVDVmVlFLWmVRTHZxOGFabHIxK2piVm9aZHBTenZqajF1U0tDb2ZEdEJFNS9rSllreXZxUjh5b1dKdStHQmlrbGg0RWswbmdBeTdDeGVlWGlQVFpOclpjUjVpMEFsbFVPUzBTaFc4eHBxVzAvS1FhWFJnc3BndDZnSkVwNFZXUVV4NXg4TEdLTnppKytDZWNxR1dwOUcrVUx5NXBnbDI2bW5TcVBaWFlXMWI5QXpxM2Iwb2xWSXRFb290dUFhdE9wSzJqSUxsRHdVNi8zT2U2ZWN1MzUiLCJtYWMiOiJkMjcyMzk5ZDg1ZGM2ZTlmZWM4NWJlMmI0NjMyOGVkYmMzMTY1NWI3MjViNzI4NDVjNWE3MDEzMTVjZmMwN2Q1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115239259\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1857029819 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857029819\", {\"maxDepth\":0})</script>\n"}}