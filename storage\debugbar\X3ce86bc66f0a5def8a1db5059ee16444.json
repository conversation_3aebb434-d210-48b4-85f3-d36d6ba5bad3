{"__meta": {"id": "X3ce86bc66f0a5def8a1db5059ee16444", "datetime": "2025-07-31 16:43:40", "utime": **********.547021, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980216.392465, "end": **********.547092, "duration": 4.154626846313477, "duration_str": "4.15s", "measures": [{"label": "Booting", "start": 1753980216.392465, "relative_start": 0, "end": 1753980219.992646, "relative_end": 1753980219.992646, "duration": 3.6001808643341064, "duration_str": "3.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753980219.992691, "relative_start": 3.6002259254455566, "end": **********.547099, "relative_end": 7.152557373046875e-06, "duration": 0.554408073425293, "duration_str": "554ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013616, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01719, "accumulated_duration_str": "17.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.369586, "duration": 0.011550000000000001, "duration_str": "11.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.19}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4693081, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.19, "width_percent": 18.034}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2052}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.500927, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2052", "source": "app/Http/Controllers/FinanceController.php:2052", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2052", "ajax": false, "filename": "FinanceController.php", "line": "2052"}, "connection": "radhe_same", "start_percent": 85.224, "width_percent": 14.776}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1451860531 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1451860531\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-487833741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-487833741\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1241839689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1241839689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-250563392 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjBSK05ybHl2KzNZNllOVE0wTm9SS0E9PSIsInZhbHVlIjoic2EyVVU1NEtkTnJUeFdqeURzK09sYVJlSHlIejJsbHgzTjlFblpTS1hSaUZNSjNUTmlieVFNbmNqSDBneW9HaTlJb0t5T3pIcW5JQW4ybGdScGJsOHNHY3VZK2t4VTFTS1plbVZQMm0vRTJZTHVLd3AvcGNUUEJpcVE5OWd2TzRhejQ5aGxReVU5VHVTSTZ5TXE4T1BlWjVzamI5ZEVBZW9laitaYWI2cVorUnRGd21kZ0h0QzRKU09GeENkYzNNV3FkS2VhUStTUGV2aUZnNXJEVWw2MytadmtiQ05HaWxkeC9ENzNXTGd2TTA4VnBuNk1LMHJsL000RGh5R2hCT1hCNEVZNXo4bTd1bXdZYllna2dqaXhEcUVkQ2FkS2NpSTVBTDUrSU12aVRGV3ZId2VDTTMxaDlsdDRGcmFwcXg1bkRHbkMwT0pobjFRYjJHdkFWb3o1TFFFSVdmUHV2Tjl1TGQycWNHNEVDeUxxMUxPbWhOOGZmVHZxSElLZGs0YmlMYVA1ZXBIaHBiV0R0dEozZE1DMnVJODlSa1BINVNPYjhDNWRDRlVDTUxadlZzZHNRSzZIK05qRkI2bFVwb1pDSTJNU0RUTlpVL3JjNVFSQ0hDYmFpUThnZjgrWmNBOStOQUR1V0ZXYnY2ayswZ25HNzZxODJnWVBNRUpPSFUiLCJtYWMiOiJhOTY1MWM4MmUzZTMwNjI0ODQ1ODI2MDNiYTlkY2ZlNTVjMWJmOTEyZWM4YzJmZDg2YWI0ZWE4MmQ5YzQwN2M5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkdER3E0WTA2ODZGOEtPM1dnS0picnc9PSIsInZhbHVlIjoiZVI5YXNmd0RhemRLMndXZGtzVE1qZENWWmtnSzI3WDZMUzhTVXdCR0NmVGkwQ2o4TmZCVWZybytWRDVwT2w1L1ZXaUhQM0xBWUpTWEhadDRvOHZ1MHBkOFB5WGdkTTYzYWhjMlBtc2hZOUNOWUJMVzlGR05VRlRRTE9QcGp4Nno4cVBKKzFMNFdnRmlNOHFaVXdzaCtQQ0kyNzduTHZTRnFHV0tlYlZNaGJCWHFFckpzSzRkNUtGdC9zL3BNOEZhckdlVEdNWk1JRTBETTcwVjdmcFhOSDJmaUZrMDNxOUloUmxmYWM1clQ3cU82a3hNbk1BTzVRK3Qzb3pydS9SSHVJdWxWemdCYWw1cmhRTjQ2Z2htUkQ5YnpHczZxZ0Jjclc4ZFFERkcyYU9JTWgxTUxSdy9qbGNFekpxM3RxMDU0eUFhY1dJR0c3QzU4NUMxQ3l2bzZMSlc1RElsSUJuRXJBbnNQRnVWY2JtWEl2cVZnUHJ1eEgzUlh2VlFkMXVqanYxeU1IaXBwaFpCTS9XU2JNdks4RDl3M2VISlpGUkpSUUNpN1NwRHpIcHlYNlBJZElsRU56L3ZSZUZRMDg5VFgrSVdNTzJ4VU1XTTRxSmFzb2tLam9KclRFcTl5UkxsdGtHMStQeFV0YXM2RXhBbkRCYUZlVEZVR0RzOTVveFMiLCJtYWMiOiJmNzIzMGEyZTc5ZTlmMDcxZmJkYzkwZDI4ZDE4ODVhZTJjNWRjMjJjMzA2NzNkNDRkZmFiYzAxZTdhM2U0N2I0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250563392\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-883226528 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883226528\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:43:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFNUkg1aWJtb0dFb0pkN3F1Y0Fnb3c9PSIsInZhbHVlIjoibEVETmcvMTNPN0Z0dTl4M0dlNFlUL2UwcnhaQmxLOU1ML1ltN2JOdzFLVDFneGdhdmxVTkRhTUE0V0tuTGN6QWZ2UC9hdWQxNHpaRjdyK0I1cFp5YThwTXZucTUyUm1pa1AzRWlTWTVtYzd4blY3UUJiZUhzSjd1MDE3anIrUjB1OFBTOEMvTWt3VUtRMk0rUldoK2owOFlnUjc1N3Qwa0Y2azgzdTZ5NVI3a2pWMVlROGJrUE50d05HUGgreENyZjQ4NTYyazNVV1dLcDJ2emxPU3BHYTFoSlQ0N3FxVDRmaUpOVUFvZ3FHcUd3b3hIVU83SCtsOFhkNERvend5TzErV05oZXl2ZURjTXFaMVd5M0dYOVN0ekttSlB2RWRpUlhqcXN1UUVZZ1lleHYrOVA2V2R1THpSR21QTkVURjBBMks0dStwaG8rK0RuMGZXdGRIT2Nnb2R2N0QxZHNHSXlDL1hBaWFweW91blZxcHUybUFodkx2M2JzUGUvZmVXN3gzNVJDa1phZXVqeXJLVnJobEpPR251c2NMR0c1dkRVQlFmVjZHRTBJcHhEZS9hMDByRTMzVUlvTStLdVJDbnpPV0pWYUxUc0QyOW9nL1pUOFdTRVRPbHBpTUN3ODVQcXhuNkNJV3pPNEt2ZjJtSkszbzh1RmNBTkRCZVpyYXoiLCJtYWMiOiI0MTJiODMyOGFkMDZlNTY3ZjI3ZWIxMTA0YzJmNzg0MzlhYjQ3ZmRjYzI2ZmRmMzU0OTJkZTNkZjNmOWMzODE0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlR2STlzeW94ZkoxT1RFalRVV2V5bFE9PSIsInZhbHVlIjoic1NyRWxBbElSQldCWm4zbXZudkFheVZrRXFNaVlISWZMOHVkckpHOFNDVCtNa3RVaXFON2VXMWMzRWZ1dUU3WHZvekdJVlpGUFpJazZMYWRaWWNhR2FoM21aaWVvUHJ5ZHlmd3Urcm5iTGMwVThXWEl0aE1ybjNmT296a2phN3NIRXo4WjV0bjJCbWFnM2ppb0RrTWJvT0RFaG9lcTdtdmQ0S2Z5VStXZzY3a2U1T096U1J1T1FROFJrQ2hqY1U0aGZlWXdrbTQ3WFRNTHJWTHBsWnUvZFNNS21vZjJEMFVSNk5IUUVmNmRZT1JEaEh6RTZid1hncDY3N1Q2SmZ1VkpkUGpubHhNcHNub3RBSDFCZTltQTE1Z3A2SjI2QUJldHhSQnB2b3ZtRWc2ZlQ2blpHSFZ5cGUxNEpTbXVyL3NvVWVPczVjMVN6eUtYUSt2Vzl5c1haVWg3blNBUDQzQlAxUDV0QTRYUjkySE5ZQlpHMVBsTDFyclZVRFQzbTV3YW1OSk1BbkZTWlRPRXFuZFRoRGNLOUd0TmRPNjd2WVl0TVptTUtKaTVGWWVvaUlpTUFhVlM1eU5RaHl3OGJXRUd2ejRiUWdGclh5UGpNVXdFRk9FTFZEb0hEbVhZZnFOeEtWdUNHSmZKYnFVY3RVam44TVAzWDZYcG1XUk8xMHQiLCJtYWMiOiIxZmM1ZTM3ZDI0YTJmOWI1YTQ3ZTFmMjM2MWVhYjVmOWU0M2JhMTg3MWRlZGQ5ZWVlNjQzZDM5YjFiZGJhMTJlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFNUkg1aWJtb0dFb0pkN3F1Y0Fnb3c9PSIsInZhbHVlIjoibEVETmcvMTNPN0Z0dTl4M0dlNFlUL2UwcnhaQmxLOU1ML1ltN2JOdzFLVDFneGdhdmxVTkRhTUE0V0tuTGN6QWZ2UC9hdWQxNHpaRjdyK0I1cFp5YThwTXZucTUyUm1pa1AzRWlTWTVtYzd4blY3UUJiZUhzSjd1MDE3anIrUjB1OFBTOEMvTWt3VUtRMk0rUldoK2owOFlnUjc1N3Qwa0Y2azgzdTZ5NVI3a2pWMVlROGJrUE50d05HUGgreENyZjQ4NTYyazNVV1dLcDJ2emxPU3BHYTFoSlQ0N3FxVDRmaUpOVUFvZ3FHcUd3b3hIVU83SCtsOFhkNERvend5TzErV05oZXl2ZURjTXFaMVd5M0dYOVN0ekttSlB2RWRpUlhqcXN1UUVZZ1lleHYrOVA2V2R1THpSR21QTkVURjBBMks0dStwaG8rK0RuMGZXdGRIT2Nnb2R2N0QxZHNHSXlDL1hBaWFweW91blZxcHUybUFodkx2M2JzUGUvZmVXN3gzNVJDa1phZXVqeXJLVnJobEpPR251c2NMR0c1dkRVQlFmVjZHRTBJcHhEZS9hMDByRTMzVUlvTStLdVJDbnpPV0pWYUxUc0QyOW9nL1pUOFdTRVRPbHBpTUN3ODVQcXhuNkNJV3pPNEt2ZjJtSkszbzh1RmNBTkRCZVpyYXoiLCJtYWMiOiI0MTJiODMyOGFkMDZlNTY3ZjI3ZWIxMTA0YzJmNzg0MzlhYjQ3ZmRjYzI2ZmRmMzU0OTJkZTNkZjNmOWMzODE0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlR2STlzeW94ZkoxT1RFalRVV2V5bFE9PSIsInZhbHVlIjoic1NyRWxBbElSQldCWm4zbXZudkFheVZrRXFNaVlISWZMOHVkckpHOFNDVCtNa3RVaXFON2VXMWMzRWZ1dUU3WHZvekdJVlpGUFpJazZMYWRaWWNhR2FoM21aaWVvUHJ5ZHlmd3Urcm5iTGMwVThXWEl0aE1ybjNmT296a2phN3NIRXo4WjV0bjJCbWFnM2ppb0RrTWJvT0RFaG9lcTdtdmQ0S2Z5VStXZzY3a2U1T096U1J1T1FROFJrQ2hqY1U0aGZlWXdrbTQ3WFRNTHJWTHBsWnUvZFNNS21vZjJEMFVSNk5IUUVmNmRZT1JEaEh6RTZid1hncDY3N1Q2SmZ1VkpkUGpubHhNcHNub3RBSDFCZTltQTE1Z3A2SjI2QUJldHhSQnB2b3ZtRWc2ZlQ2blpHSFZ5cGUxNEpTbXVyL3NvVWVPczVjMVN6eUtYUSt2Vzl5c1haVWg3blNBUDQzQlAxUDV0QTRYUjkySE5ZQlpHMVBsTDFyclZVRFQzbTV3YW1OSk1BbkZTWlRPRXFuZFRoRGNLOUd0TmRPNjd2WVl0TVptTUtKaTVGWWVvaUlpTUFhVlM1eU5RaHl3OGJXRUd2ejRiUWdGclh5UGpNVXdFRk9FTFZEb0hEbVhZZnFOeEtWdUNHSmZKYnFVY3RVam44TVAzWDZYcG1XUk8xMHQiLCJtYWMiOiIxZmM1ZTM3ZDI0YTJmOWI1YTQ3ZTFmMjM2MWVhYjVmOWU0M2JhMTg3MWRlZGQ5ZWVlNjQzZDM5YjFiZGJhMTJlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-599095752 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599095752\", {\"maxDepth\":0})</script>\n"}}