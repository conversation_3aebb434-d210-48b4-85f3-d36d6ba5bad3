{"__meta": {"id": "X67e0aadff7391473d43ffdadcd41431c", "datetime": "2025-07-31 17:10:57", "utime": **********.82993, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981855.658555, "end": **********.830018, "duration": 2.1714630126953125, "duration_str": "2.17s", "measures": [{"label": "Booting", "start": 1753981855.658555, "relative_start": 0, "end": **********.567798, "relative_end": **********.567798, "duration": 1.909242868423462, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.567841, "relative_start": 1.****************, "end": **********.830027, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LHr6nJyvP1y3i4pMxPcL6XaPXUIWFhYWeoOm6U0d", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1480705285 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1480705285\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1580796536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1580796536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1207126877 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1207126877\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-476603938 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476603938\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1065064422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1065064422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-390688245 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:10:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRjK1gxeDNMOVFmU2lxc0xBS3FIaUE9PSIsInZhbHVlIjoiS3lFT3p2VVJRc2M4OERKMFVsSVE5c1I4REt0RWJFNkg2Q1hPbEh0QStpMnFiTGhlWThTdGhtMVBmaEdVUVB6aWhaYzlwcE12Znhyd0s4dDBEOFhsYk1jd2xpMTRhdUcwcW9PWUEwRmphaXBwSjhYc0lPTGhvMW9XNFJvdmwxenF0WmsyNEtUZTZPdUVuVzVyckJrVkZiQVA1dmFveU1VUmczaHAyRGtNRGFFTENKa3RFdWhpVVU3MW90L1paUFkvYlA4ZGlTbE9RUjc1VFcvdWQ0K1EvVm1ibGM0cE9xZGRTWEswWHo4M3p0Z0dLS3hwQWUyK3UvYTc4TDVZVzRRMEx2MW50bUJ1TjRDMUxxaW5aZVlvNDBOOW5QdFhDUk1vSkxkc01Zb0F3VFE5NzM3R3NwZzVvbnpPR2IwZkZOT1pZWXdoSjFNM00wZFNHZmVDcVdpcWpza0NoNFNhOGoxeE5rTXNaZ1pCUVNEWXhuSHZGYktHOFJDcEhqSFhuVk1zZ21McVprZWViZUFIaWE5TTRIWnZGMFVmUk5OeDl4SDhPdnQxejFlSDhMZTlacklvOFFQWlFpbWhzdGsyUkI3NnpyWEo2bmxPZ09zT0MyMVZWNExvSDZQYWlMdlJEaVVpRGNjUU1WQWVxQjhWM0RBZWVxSE9oVWRiS2JydXFBRlYiLCJtYWMiOiIwYzM0NTVhYzk2NDRlZmQ4M2Y4MWRmODRhYmZiYzMyZDE4ZGI5NGZjNTRmNzQ0OWZhMThmYWYwNTAxOWNiNTNjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:10:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZldVczTzEySmk2b2FhMkZpa0w1ckE9PSIsInZhbHVlIjoibjNxd09HQ3JGMTZqbTBzVkZ4THBobDhjMjJURG9LeWxLNloyZzAzN3R0alhIVnVTUTMxQmV5emNTbUN2ZG1tYXYzekpFWHEzakxGUWk0QkZ1OWsvZ2pELzhYQjlzRlZEOXFoRkNyMzRLM01VdUdNanJicHZLVVBIS0N4YVF3OTFYNmJpZG5BaUZtT0JlWW54QkRIbzlaQ2FNNWZ5eDdrUmg1R0cva3BsNStuL1JjVXp2M1pVc2NlM1NodkhUSUFDbWFTcWdxU1MvbXNlUzQyanlSdWRFQ0UydGc3d1B1azAydFZiTzVwMGhuT1BmbUNaN0pybnE4N3FFczNnd1E0cjFTUStMaXB0YTh0V0RocFZOVWozSnRvN09YSm5PYy9XejhRRnVCcU1mbkVPclVvL1RTanRPTzBuM2tpRFpGZkVKQnEzaVFwM21uU1VzdVNWeEIyd3JPY1VsUmt6bmwyTEZWb0FFRnMxNHJaWnRHOGtxOE84N0lPamE1NFVncGd6dXhQTi9neUt2bWIycFBrMjRuay9lNGdlM2pEaTZCVU5FZVFJMHNLRU9nbjFRY1o5VmVWRUl1VEpock0yWGExK0p0dUp0MFdtMFFEVXI2TzM1WWVwM2NPc1U4ODBoZXVWVVFOK2VpNCsxODB5VlBhZmVwOHZTQ0FMRkZHUmNiTnMiLCJtYWMiOiI5MDBkZTJiYjk0YTgzYThhMzZlOTU2M2IxZDViNjk2MGVjM2NlOGZlOTI4MWRhNTExMGUwNjVhNGQ4MTFmYTI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:10:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRjK1gxeDNMOVFmU2lxc0xBS3FIaUE9PSIsInZhbHVlIjoiS3lFT3p2VVJRc2M4OERKMFVsSVE5c1I4REt0RWJFNkg2Q1hPbEh0QStpMnFiTGhlWThTdGhtMVBmaEdVUVB6aWhaYzlwcE12Znhyd0s4dDBEOFhsYk1jd2xpMTRhdUcwcW9PWUEwRmphaXBwSjhYc0lPTGhvMW9XNFJvdmwxenF0WmsyNEtUZTZPdUVuVzVyckJrVkZiQVA1dmFveU1VUmczaHAyRGtNRGFFTENKa3RFdWhpVVU3MW90L1paUFkvYlA4ZGlTbE9RUjc1VFcvdWQ0K1EvVm1ibGM0cE9xZGRTWEswWHo4M3p0Z0dLS3hwQWUyK3UvYTc4TDVZVzRRMEx2MW50bUJ1TjRDMUxxaW5aZVlvNDBOOW5QdFhDUk1vSkxkc01Zb0F3VFE5NzM3R3NwZzVvbnpPR2IwZkZOT1pZWXdoSjFNM00wZFNHZmVDcVdpcWpza0NoNFNhOGoxeE5rTXNaZ1pCUVNEWXhuSHZGYktHOFJDcEhqSFhuVk1zZ21McVprZWViZUFIaWE5TTRIWnZGMFVmUk5OeDl4SDhPdnQxejFlSDhMZTlacklvOFFQWlFpbWhzdGsyUkI3NnpyWEo2bmxPZ09zT0MyMVZWNExvSDZQYWlMdlJEaVVpRGNjUU1WQWVxQjhWM0RBZWVxSE9oVWRiS2JydXFBRlYiLCJtYWMiOiIwYzM0NTVhYzk2NDRlZmQ4M2Y4MWRmODRhYmZiYzMyZDE4ZGI5NGZjNTRmNzQ0OWZhMThmYWYwNTAxOWNiNTNjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:10:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZldVczTzEySmk2b2FhMkZpa0w1ckE9PSIsInZhbHVlIjoibjNxd09HQ3JGMTZqbTBzVkZ4THBobDhjMjJURG9LeWxLNloyZzAzN3R0alhIVnVTUTMxQmV5emNTbUN2ZG1tYXYzekpFWHEzakxGUWk0QkZ1OWsvZ2pELzhYQjlzRlZEOXFoRkNyMzRLM01VdUdNanJicHZLVVBIS0N4YVF3OTFYNmJpZG5BaUZtT0JlWW54QkRIbzlaQ2FNNWZ5eDdrUmg1R0cva3BsNStuL1JjVXp2M1pVc2NlM1NodkhUSUFDbWFTcWdxU1MvbXNlUzQyanlSdWRFQ0UydGc3d1B1azAydFZiTzVwMGhuT1BmbUNaN0pybnE4N3FFczNnd1E0cjFTUStMaXB0YTh0V0RocFZOVWozSnRvN09YSm5PYy9XejhRRnVCcU1mbkVPclVvL1RTanRPTzBuM2tpRFpGZkVKQnEzaVFwM21uU1VzdVNWeEIyd3JPY1VsUmt6bmwyTEZWb0FFRnMxNHJaWnRHOGtxOE84N0lPamE1NFVncGd6dXhQTi9neUt2bWIycFBrMjRuay9lNGdlM2pEaTZCVU5FZVFJMHNLRU9nbjFRY1o5VmVWRUl1VEpock0yWGExK0p0dUp0MFdtMFFEVXI2TzM1WWVwM2NPc1U4ODBoZXVWVVFOK2VpNCsxODB5VlBhZmVwOHZTQ0FMRkZHUmNiTnMiLCJtYWMiOiI5MDBkZTJiYjk0YTgzYThhMzZlOTU2M2IxZDViNjk2MGVjM2NlOGZlOTI4MWRhNTExMGUwNjVhNGQ4MTFmYTI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:10:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390688245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-218326533 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LHr6nJyvP1y3i4pMxPcL6XaPXUIWFhYWeoOm6U0d</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218326533\", {\"maxDepth\":0})</script>\n"}}