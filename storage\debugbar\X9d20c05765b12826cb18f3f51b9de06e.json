{"__meta": {"id": "X9d20c05765b12826cb18f3f51b9de06e", "datetime": "2025-07-31 16:31:31", "utime": **********.250916, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979487.613095, "end": **********.250964, "duration": 3.637868881225586, "duration_str": "3.64s", "measures": [{"label": "Booting", "start": 1753979487.613095, "relative_start": 0, "end": 1753979490.843422, "relative_end": 1753979490.843422, "duration": 3.2303268909454346, "duration_str": "3.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979490.843461, "relative_start": 3.****************, "end": **********.25097, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01507, "accumulated_duration_str": "15.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.135282, "duration": 0.01507, "duration_str": "15.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-46848291 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-46848291\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1097743999 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1097743999\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1206359683 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1206359683\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-925850274 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNhWFBzNHI4N2tVak1xaWlyajAvTUE9PSIsInZhbHVlIjoiY3NCa0NXb1JIL0RZMy84N01VMXc1VittdHVKR1l5RGlrOGIzaFZzOWNNRTRqWjRhT3JyOXhwMmZxRDJmTlUvMnZFWlVvRVZTL2RwTXhMalBwWWhzQkx2UmxmY0VNK3NXU09Hd1dLLytSR29Cenl2Sk5ON3RYYTZzaWNDMStVcEtITVdjY1hjcGd0bWt4SFl0dHltQ1V5V0ticWorV0RFcldYRWJ4dTB0SWJiNEt0am1WMzZrc25DcFgxWTZBMjViNEpyYnBBQk5WUnkxSXZBZ25LVHJTZS9UODdKSXNVeUxlQ043cU05QTg0TnFSOVc4azhoNlFzT1k2UXZZUFJ4VDlRaGdFNlAwSFB0dDc1M1FBa0oyOUh5aFlha3dZTE9ZakxtdGpmVmtGc0YwbUJqMFBvT1J4TDF2SEdBZDRVM3QxT2JhaXJjZHpZZEY2U25Scjc4NkZuRHZqUW1kdlZoOHZMS3BiL2QxOTVKYWo4T2R0NTRvWkFuSkdudkY5alZaeHRNTlhkZDFtd1kvTVUvT3N4SzY1TzM4aWhZWlg5WVBRYjJhSjJuN3FjaGJ5MStqa1FuR244VU9DWE5JZzJXREJVWm5aOEM0TjVieElIUzFUNnd6NjI5U3lQZi9Sa1JiT0t2ek5jRXkvdUFxM2NrMlNVSnpHdi9ZekgyM1hGUC8iLCJtYWMiOiI2M2M3ZThhNzZkNjZkMmViNDBmMGNlNTlhZTBkZTJkOTBjNTgwZWZhMWVhZDI2ODU3YjIxMWNjMjM2YzhlMWIwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlV4ekRlR3cwWVZubGN4am1lR2FlcHc9PSIsInZhbHVlIjoiSUdOc1FtZlNFZDJ3QnR2eFh4ZlNsaXlFYXA5Nk5XRjZWdlVBWjB1WEZ0MnBFcjJFSFF1RWF5blQ1WCtGWHRzajhOWHZIeXROSUhwNVNwT05BbkJNMjVka3EvT2pYbFpMQ1UyZkxKS2dnM3ovTEw4OUlDdE80T1N3ZjRYWXJVRnUyMVRiWnJvVWNlTXpFbTZ1STVXUlg4VEhrbG1OYVRFbUJKVzJJRmExRm1HVjVZUUVHZy9NL3g0ZmZDZnZETTBQWFpBSFpOampQMHZYQjA3R0o2UEE1MlhLdjdDeWNURGF5YkEyelpYZjFVZUJvaHI3M3hvRDd2aWc5NTB6bTJ3eHZGbDcxT3BWdm5XdzYvSFlqaXZxcHpnOUNZZHhyb1RySno3UXgzbTc5bjhyc25EbHltakg3cW41Ti9xdUt2V1JHaUEvMVpmTEFWVXRWYjhzZjF0VEVLN2ZPeHB1VUtaUFUya3BmWitQTFpaclZZbFVBSkluMjUyOXhRR29haER4aEY5TWhHTFJvK25ndXFmZjFady8waC9XLy85SEVCRmdGQmFlNFd2SzkzUkM1MGFySk5TNFRpbUkrZVNPdDI1NmdBWmQ2VEVUYi80VFErMk1FOENLQ1ZuUGhFNGxZRTB0OHMyL2l1ZnoxMkl3eW9hdmlleDdKell3UjZSYWt5Wm4iLCJtYWMiOiI0MTk0MjI0NzY0NDM5YzczOGU5Y2FmZTkyZDBhODczMjIxMDI4NDRlNTYwMDY4NmQwZGJhNGNhMjJjYmQzMzBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925850274\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2029813947 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029813947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2058880420 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:31:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5qWDZDNGw3L1pQMTZjV0xXZVJiNVE9PSIsInZhbHVlIjoiODNUMjk4V241VGE2cUMvQ2l2RE9oWXBnWUMxMGw5N1lJRDloME9uMC9BZlBQdCtvYld2UEViY1ZoK0VnYWRpSmxQVzhEVUdCOVA2QkQ5SUFWRTJIZnU3UWhJVE4rUTZ3bkNYejlPT3VwdUxEaXhpOHlEYks3aUFaeFpzWWZIenpNdllKdnpOcC9tbUswTi8vMm93d2lQM1RSVjY3L1l4c29wQ0pmZys4dTBsU29PN3Z1eWQ1QjdlRGtOcXV5MUEwVEN3VVdWN0xGMkhxR1hSRVFnSGZ2VEFnZHFzTFdKb2tmUUNhV2I2Sm5yVmRwUFM2cW04Wit2ek04dVZUeDdVWDNadkdxNXk4ZXpQTzBkY1BJT1dFcEFDY1lpTnJ5Y251dVJ2cXVwWHp0cGFyNHY1Nlp5ZWVhOFVGNGpyTzVuL2hyTW5LYzk4QTRucTFNcGQvQTExUjdTUnJ0QlQ2eDZuZitUaldOSnFvbEpSYmg4MDh1eFFHSElnaEszem5Na1RadkxLWkt0Sjd0WER4WG1reWtMN2xjc3FsN1Z2RGpDMU5xUklNZ2xIS2kwVFI5VlNGSGViM0ZtOGsyTTZ0TnlHb0pTNEpodzRYZ3hWSlMwWVhMUi9mcEZqRHAvZXJOTlp6SXZYSDQvQjI2SW4zNW51aWhSSThoVHlWQ0RLZ3ZSeGwiLCJtYWMiOiIxZDZjMmE4NWVjMWI0OTA1MmYxMDhlOWIzMTRhZTA3MjMzM2U4NGQxOTExN2Q2ZGMwNWZjZGJmZmI1ZGZhYTI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:31:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im9GT0s2YkJTTWMzalVrd25KUU1wZlE9PSIsInZhbHVlIjoiNTIvZTkrbERTZ2dUSHhRRzc0Mjdic3V2b2s2WmoyTGNhK3puWlJhZnl2ZG9vTmUxckhDa294dkd6U0VWVldYeW1SK05qdFJqUDNDUnlpSGd2OE13dGdGQ0xhUjEyQkN6Rk1FWW1FL0xCZ1ExU2haQ3lZWmtIMTIzMWxpRDRDRU1mcVJoN0pZNlIzVVIvOGNlUGRlWjF0YjlpOHVWdnp3SjdlZzd4eUMxYzZXQXFvTmg0SFpjZmh0MllGcWJzVXFaOUNXWFVJYy9DNlRtN1I3MVlWMWFHdG94N2g3allqOUZpZ2Vod2tqZE1ZSDZHQ0hTeFIzcGNRVzJaUGdKei9OM3VxMmxoaTI0VWNQRHRYMHd3S0FXUVRPWWZUeFU1TkxrNEJqRHgzZHhSRUhpTU83ZGFFM0J1ODZGQ25BZ1N4ajkzZnh6bXJkSWtTL2tOOFg2QUdoTDdmZUVnbitEd1VCcVI2SUtDdytITnZjYzBuUlFKWngxSEVtbVBmNGFRTnBiblNSbE9nQWQyL3JPalJHNUdsWUVmTm52a3lyYS9sSG5yOHpXM0dKQ2JOREUwSHBWSjBzeHdPK1I4bVFnbUgxR1pNaVdBeGVZdU56aWN5YzRRaFZZZWlKMGQ5aVI0aXZxUVBuYnNUUm5HRFYydU5lRG8rWk5jQnhFcFJUYkpOYWUiLCJtYWMiOiIyNTAxZWFjZTg1ZWU5Njc4NzliZWM4ZGU5MzYxNGQ5N2U5YmRhMzJjNmI2MjczOGYzMDVmNDM2NjkxMTdmOTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:31:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5qWDZDNGw3L1pQMTZjV0xXZVJiNVE9PSIsInZhbHVlIjoiODNUMjk4V241VGE2cUMvQ2l2RE9oWXBnWUMxMGw5N1lJRDloME9uMC9BZlBQdCtvYld2UEViY1ZoK0VnYWRpSmxQVzhEVUdCOVA2QkQ5SUFWRTJIZnU3UWhJVE4rUTZ3bkNYejlPT3VwdUxEaXhpOHlEYks3aUFaeFpzWWZIenpNdllKdnpOcC9tbUswTi8vMm93d2lQM1RSVjY3L1l4c29wQ0pmZys4dTBsU29PN3Z1eWQ1QjdlRGtOcXV5MUEwVEN3VVdWN0xGMkhxR1hSRVFnSGZ2VEFnZHFzTFdKb2tmUUNhV2I2Sm5yVmRwUFM2cW04Wit2ek04dVZUeDdVWDNadkdxNXk4ZXpQTzBkY1BJT1dFcEFDY1lpTnJ5Y251dVJ2cXVwWHp0cGFyNHY1Nlp5ZWVhOFVGNGpyTzVuL2hyTW5LYzk4QTRucTFNcGQvQTExUjdTUnJ0QlQ2eDZuZitUaldOSnFvbEpSYmg4MDh1eFFHSElnaEszem5Na1RadkxLWkt0Sjd0WER4WG1reWtMN2xjc3FsN1Z2RGpDMU5xUklNZ2xIS2kwVFI5VlNGSGViM0ZtOGsyTTZ0TnlHb0pTNEpodzRYZ3hWSlMwWVhMUi9mcEZqRHAvZXJOTlp6SXZYSDQvQjI2SW4zNW51aWhSSThoVHlWQ0RLZ3ZSeGwiLCJtYWMiOiIxZDZjMmE4NWVjMWI0OTA1MmYxMDhlOWIzMTRhZTA3MjMzM2U4NGQxOTExN2Q2ZGMwNWZjZGJmZmI1ZGZhYTI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:31:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im9GT0s2YkJTTWMzalVrd25KUU1wZlE9PSIsInZhbHVlIjoiNTIvZTkrbERTZ2dUSHhRRzc0Mjdic3V2b2s2WmoyTGNhK3puWlJhZnl2ZG9vTmUxckhDa294dkd6U0VWVldYeW1SK05qdFJqUDNDUnlpSGd2OE13dGdGQ0xhUjEyQkN6Rk1FWW1FL0xCZ1ExU2haQ3lZWmtIMTIzMWxpRDRDRU1mcVJoN0pZNlIzVVIvOGNlUGRlWjF0YjlpOHVWdnp3SjdlZzd4eUMxYzZXQXFvTmg0SFpjZmh0MllGcWJzVXFaOUNXWFVJYy9DNlRtN1I3MVlWMWFHdG94N2g3allqOUZpZ2Vod2tqZE1ZSDZHQ0hTeFIzcGNRVzJaUGdKei9OM3VxMmxoaTI0VWNQRHRYMHd3S0FXUVRPWWZUeFU1TkxrNEJqRHgzZHhSRUhpTU83ZGFFM0J1ODZGQ25BZ1N4ajkzZnh6bXJkSWtTL2tOOFg2QUdoTDdmZUVnbitEd1VCcVI2SUtDdytITnZjYzBuUlFKWngxSEVtbVBmNGFRTnBiblNSbE9nQWQyL3JPalJHNUdsWUVmTm52a3lyYS9sSG5yOHpXM0dKQ2JOREUwSHBWSjBzeHdPK1I4bVFnbUgxR1pNaVdBeGVZdU56aWN5YzRRaFZZZWlKMGQ5aVI0aXZxUVBuYnNUUm5HRFYydU5lRG8rWk5jQnhFcFJUYkpOYWUiLCJtYWMiOiIyNTAxZWFjZTg1ZWU5Njc4NzliZWM4ZGU5MzYxNGQ5N2U5YmRhMzJjNmI2MjczOGYzMDVmNDM2NjkxMTdmOTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:31:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058880420\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}