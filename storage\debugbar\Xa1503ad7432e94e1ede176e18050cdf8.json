{"__meta": {"id": "Xa1503ad7432e94e1ede176e18050cdf8", "datetime": "2025-07-31 16:08:58", "utime": 1753978138.016556, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978134.275639, "end": 1753978138.016654, "duration": 3.7410149574279785, "duration_str": "3.74s", "measures": [{"label": "Booting", "start": 1753978134.275639, "relative_start": 0, "end": **********.429508, "relative_end": **********.429508, "duration": 3.1538689136505127, "duration_str": "3.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429557, "relative_start": 3.1539180278778076, "end": 1753978138.016667, "relative_end": 1.2874603271484375e-05, "duration": 0.5871098041534424, "duration_str": "587ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47416096, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.037770000000000005, "accumulated_duration_str": "37.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.771057, "duration": 0.03221, "duration_str": "32.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.279}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8760319, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.279, "width_percent": 8.525}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.907131, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 93.805, "width_percent": 6.195}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1575683979 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1575683979\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1888214969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888214969\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1793541244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1793541244\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1778727567 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjVFbmlqK3VFaTFsbGtLTkxSeWxlb3c9PSIsInZhbHVlIjoiRTVudEYyUlVvMmhXQjdscXM5MFVPakpjcUtPVjlZTkxyVGpjemY0MUtGMTZsUjcvVmNndm9rMzdyUExlT0FkKzRmRmx6NFYvbVQ1Z2tPN2dXN2pZR0FLdUtpRkhaZ3N1UE5LY2NteTd5L09rWW9ZV3JQcndNcGdTYy9BYWxiMUV1SERBdDZ5Y0dWcDRsTVdRakRoQTBJQW9HS1hkclc3Q2VuOXVtclJpR0pDZTdleitaRW56dlJ2TU5tVlhpZkphTm1OSGNJenNuSENkZ3l0ZDdkQkMyN21rVGd5cXJiTUQ3M2w0dG9yOWsvOGpwWXVlQlZkTmVya0F5VlVXS0xUQ2djMjU2S1dveXVHb201anN1QnN0TjNFMXJzRlVWRWJxbTBpY0Z6Rllvb2ZXK09tT0hOd0EvL0FVSndBL21jVmZ1N2tkeXVPODFtS2k1M1hKQlZDR2Y5T0ZQRk94eS8xd0tVTHlUckxKRlJwM1RKL2lLaTdFRUlKVy9pZlZxa2kvRnFNeGpjbjJwclEvbCtoSXREOGFqcFlDWGdnY1BiOW13WWt2N2VoQk42a3BIYXhWUDRUdVNwMXFhdlhWYVVHOXp4SDhKRDZvTjhFUmlOSjlpMlNzRFN2bWFZTzBDZFV5NTF6eFRJV3k1Z09jUlBHQk1ScEJZOHBsdTZneXNLZ1kiLCJtYWMiOiJiNmI4ZDc2NDM5NzIzYTIxZGRhMzM2NWE2ZmQxMzI4MDkyZTY5NDdkNjVjNDY2NWQxM2Y3MzNjN2FlYmQ4MGY5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InZyazgvc1BjUVpHUjB0NXhBTUF4K2c9PSIsInZhbHVlIjoiN3orL2ZiLzBUWG14TlpnZllnYk5SVmJxaG9DQUJSZ3ZRQVJtWEhTY1hKcHB4T240ZUFSdllHRmttRGpmMFVZY1ZzWTQ0RjAzc2NwMFhQMXByWXFyRkpCbi9nUVNaVXh1eGRMaS9hZGJZa3pOZk1OM01WMFJta0ZiSkRzeHVoMjJQZEEyYlFPeVdkQkhjYWtDczQ0N2NGTm5adVNpa2VQYUZKRkhuTjFyNjdydk5PMnFxaVNkMnVZdm5xaVRUZXNsQ1ZCd0FYMmYvc3p2VDcxdzdpYXhwT0hsdXgvZXgya3IwNjNLN3E1U0l2akdlV2U1OWFzZHQrNkE0VXlCK1FnS01INzgycUVvL25GZXEvQXlxS3lPaWNzb2J1RzNrRmorTUJIYXk4aktFWmx6N0JPZUJxY3lNL242bDVPY3dqbkVHdzI1cVN0Y3hGVmZLRzUzNXJYdXhMODI0Njd5MHdjV1I0SGNyMGV3QXJTZm44SVBTazE3ZkYwVzhKeTEwTEpHa25nSmxOVnIrN1pJZkRmZ1NJeWlnL0JnTEpZaTU0RkVVMk9vUHBxUldXWHVwZ1RXMFczSEV5TXRjRi95dUtuYUV6b0R2YW1GS2JFQkRsc1NKWUZ3aGJiT05UZzdvdHFGU1pRbFN0MzFTUlFTVDlRQm4yNG5zK2d0OHREL2Jrc0EiLCJtYWMiOiIyMGYzMjAwMjY2MjlmYmZjYTllYjQyMzUzZWZkOTgzZTEzMDViMmI5MzFhMjQxMDg4ODE5OGM5ZDc4MDEzNjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778727567\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-402217316 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402217316\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1080260218 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:08:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdVYmpEdVo2dForcGgzSTlKRVRiL0E9PSIsInZhbHVlIjoiY3FZR2xPNmYxbFRzbVF3V3hPeG9zNUVBRUNRZnpTc0tBQmxyR1JySSs5OHB3bGIwYzhIalNGQTV4T1RKQXhCWUFjem9GQm15b3hWbURLaGtNK2psblczb3RJYklXOHNYdjNaaFRiN2lnSG51YXNzZlpqUzFyWEpUMWFsMWtyWnBkdDFna3NiaE8xLzJiMnFNM1NIY1hZTm1zSmVOTWxzSzlRaDAxNGxmWC9ETXJqcFRBU0szKzlTcmdIaVdibVhZbGsybTZXSTF3TkNVWE9Va3pzTFVoaWwrelhodi8veWtGRlJhQUxqdDdEcG9TTGlLTFVLdEpzbXFVS3hDc1hCT3Bkb2JYL0ptUjFJdHlMS0RQbXUzRjFKNS9QSytLUUkybFVtY2I2ODZzYVNxbHRuOXNxbUI2VTJqL2pSY0pUSEhxeWRHeHFLOTV4TmhLa203dXBkeCtJdWpKaEpUWFRreDJ1RVF5SnJPNXZqajEwSm14RWxtWFpiOUFRaUI1a1pyaytENElZamVlU0drUXYzcDdub0VLS1ludmd6ajcrSWhwZzR4ZUExbXBYSTJEd1lVa0pEbXBLR2xMVTFzZ3htYVY1WEIvSWJlK1MrNnZjbGVrcVo1clUvZ2dkM24rbjdrUmIvbmhQcEk3ZEZkS2tISkU2aW1vMzJLQjhrR3hLQzIiLCJtYWMiOiI4Y2RmNzFjNmYyYTZhODE0MjRjZmZhZDBhNDBlYWI1ZDQxYTczYWFiMjRlMDg2Yzk2ZjZmYWNlMTJlZDgwM2I4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:08:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRtbjlia0ROZlp5WHZObjAycFpLVlE9PSIsInZhbHVlIjoiZDlHaFEwb1Blc2JzcG53ell6L0Y2RWc5bGhHd3VSL0NYVHhTS3k5SmpWNXBoKzFlVUM2UmQxbjk1Y3QyUWdoSk5sR1VRNGNGMHVZUXo1NWwxdFVMT0pOYS9jK3BMNHRkVVBiMzhhSW16ZzgybklxL3pvSjJpRGxLTElCWU00QXdqbUhIMnNmS0Q0WmZEM1Z2VmpLMmVBZktUZS9WSHUrZVZkS2NneFMwaUllZ00yMk1uM3dWVGo1UkxkVzRFaWZabEV6aXp3bDVLOGdDekQyYXNFN0ErdEoxSjBlTTh5MFFMOWRrd1cxa2dkQ0tHTkNva3B0YmxIR3laR2JRREdKS2lsdGZjSTF4V2tZcGJFTUtwdzVLVk9zYXlleTlwWitmOWpydnljSDJaQmhnSGppUGlBbDNRaGExRE5xZC9GSDFpcG9EK1E0cFkvczN4blFadkREYTE5RkJsL3lFb3d4ekhFSFBnZkV3bFVwaUZKMy9Oczk1T1lxMng4dG0yUkJPNnN0RG43VEJjbmFQV2RDWTROSVJzeG80MktINzhaOG02Wk1OZU1hMy9naE9aOTNtWXZOV2NQTnVqZWw1SUkrQThkdGxoVFVCTUx5cHhBZ0N3M1RpSmlBejJoNUVlMXQxQUZmY1JEZWtsU09tRzZnOU9rRmdoTnZ1UEJPa24xRXUiLCJtYWMiOiJiZGU2NzE5OTVjNTk5ODkwZTkwNDJkMzQ4ODlhYmQwOWI2MDg2ZDgyODg4ZDkzZTdiNjAyYjBmZWM5NWI2ZjU5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:08:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdVYmpEdVo2dForcGgzSTlKRVRiL0E9PSIsInZhbHVlIjoiY3FZR2xPNmYxbFRzbVF3V3hPeG9zNUVBRUNRZnpTc0tBQmxyR1JySSs5OHB3bGIwYzhIalNGQTV4T1RKQXhCWUFjem9GQm15b3hWbURLaGtNK2psblczb3RJYklXOHNYdjNaaFRiN2lnSG51YXNzZlpqUzFyWEpUMWFsMWtyWnBkdDFna3NiaE8xLzJiMnFNM1NIY1hZTm1zSmVOTWxzSzlRaDAxNGxmWC9ETXJqcFRBU0szKzlTcmdIaVdibVhZbGsybTZXSTF3TkNVWE9Va3pzTFVoaWwrelhodi8veWtGRlJhQUxqdDdEcG9TTGlLTFVLdEpzbXFVS3hDc1hCT3Bkb2JYL0ptUjFJdHlMS0RQbXUzRjFKNS9QSytLUUkybFVtY2I2ODZzYVNxbHRuOXNxbUI2VTJqL2pSY0pUSEhxeWRHeHFLOTV4TmhLa203dXBkeCtJdWpKaEpUWFRreDJ1RVF5SnJPNXZqajEwSm14RWxtWFpiOUFRaUI1a1pyaytENElZamVlU0drUXYzcDdub0VLS1ludmd6ajcrSWhwZzR4ZUExbXBYSTJEd1lVa0pEbXBLR2xMVTFzZ3htYVY1WEIvSWJlK1MrNnZjbGVrcVo1clUvZ2dkM24rbjdrUmIvbmhQcEk3ZEZkS2tISkU2aW1vMzJLQjhrR3hLQzIiLCJtYWMiOiI4Y2RmNzFjNmYyYTZhODE0MjRjZmZhZDBhNDBlYWI1ZDQxYTczYWFiMjRlMDg2Yzk2ZjZmYWNlMTJlZDgwM2I4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:08:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRtbjlia0ROZlp5WHZObjAycFpLVlE9PSIsInZhbHVlIjoiZDlHaFEwb1Blc2JzcG53ell6L0Y2RWc5bGhHd3VSL0NYVHhTS3k5SmpWNXBoKzFlVUM2UmQxbjk1Y3QyUWdoSk5sR1VRNGNGMHVZUXo1NWwxdFVMT0pOYS9jK3BMNHRkVVBiMzhhSW16ZzgybklxL3pvSjJpRGxLTElCWU00QXdqbUhIMnNmS0Q0WmZEM1Z2VmpLMmVBZktUZS9WSHUrZVZkS2NneFMwaUllZ00yMk1uM3dWVGo1UkxkVzRFaWZabEV6aXp3bDVLOGdDekQyYXNFN0ErdEoxSjBlTTh5MFFMOWRrd1cxa2dkQ0tHTkNva3B0YmxIR3laR2JRREdKS2lsdGZjSTF4V2tZcGJFTUtwdzVLVk9zYXlleTlwWitmOWpydnljSDJaQmhnSGppUGlBbDNRaGExRE5xZC9GSDFpcG9EK1E0cFkvczN4blFadkREYTE5RkJsL3lFb3d4ekhFSFBnZkV3bFVwaUZKMy9Oczk1T1lxMng4dG0yUkJPNnN0RG43VEJjbmFQV2RDWTROSVJzeG80MktINzhaOG02Wk1OZU1hMy9naE9aOTNtWXZOV2NQTnVqZWw1SUkrQThkdGxoVFVCTUx5cHhBZ0N3M1RpSmlBejJoNUVlMXQxQUZmY1JEZWtsU09tRzZnOU9rRmdoTnZ1UEJPa24xRXUiLCJtYWMiOiJiZGU2NzE5OTVjNTk5ODkwZTkwNDJkMzQ4ODlhYmQwOWI2MDg2ZDgyODg4ZDkzZTdiNjAyYjBmZWM5NWI2ZjU5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:08:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080260218\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2009889320 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009889320\", {\"maxDepth\":0})</script>\n"}}