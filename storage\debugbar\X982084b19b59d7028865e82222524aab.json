{"__meta": {"id": "X982084b19b59d7028865e82222524aab", "datetime": "2025-07-31 16:46:12", "utime": **********.070296, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980370.07986, "end": **********.070332, "duration": 1.9904720783233643, "duration_str": "1.99s", "measures": [{"label": "Booting", "start": 1753980370.07986, "relative_start": 0, "end": **********.875592, "relative_end": **********.875592, "duration": 1.795732021331787, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875612, "relative_start": 1.7957520484924316, "end": **********.070335, "relative_end": 2.86102294921875e-06, "duration": 0.19472289085388184, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02908, "accumulated_duration_str": "29.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.977906, "duration": 0.02514, "duration_str": "25.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 86.451}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.028213, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 86.451, "width_percent": 4.333}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.039033, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 90.784, "width_percent": 4.333}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.048421, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 95.117, "width_percent": 4.883}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-600453367 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-600453367\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1148524795 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148524795\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1815095533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1815095533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjF5WVdxNS9VWG1HY2VZc2FTQURrRUE9PSIsInZhbHVlIjoiVDlPaVpsdGFYa2UrRzJ2Mncyb3dPL0l2QzMraFovalhQdHVMM3VyejhrakZGZG52VythcXh4dW1sMmtOZGFaL1VwaFE4N0V0cEpWN0NieHZrbDBMQ3V2TDBjQnc5eGdZbUJmN3RVK0RRWUZCNFhZNHhGQiswUlZiREFzRXpEOHVrZjVNbmtMT1Z3R3N1cmoyay9EQWwwdXBpMHdkN0RjN3RLTHJaNEM2L293WXMvalhoNExoOXY0WE02VXRoK2d5MFp2NzdTVUgraTFRUkRoRWIxb0dDbklVN052VEF1WURaUWN0Ry9Jb09ITkpQcmRCclU5ams0MVo3VFVYMldjVnpJQlJBOU16TWwydTNlR3BiUmZGcGwrd0Q3NG01WVhmVEpmd1JDUzFMSDAxM3oyM01QNjVWN0I1TTUyMnhzK1A1Si9OVTBYMENkUHg2d1p1dEpYUW9zcVF2SlZqQStYVXRITGgrY3FQLzNDd1VnTElzWFF5L3JpQ3RRNE5oVHEwek5NdWtuUm5jMTljc0FWOHBrM0ZyN2hXUFV4d0s5NzBRT2FEbVU5QjdKTE1KTnVYaXVVclRTaGllakxjNGk1V0piMHBOWkxkOW9uYmFZQXpYZFpPeWs2OExkL2Y1S3U3ZWlJUndMcUFYNWtsSUl2bEZMdmlhZlc5L1hUTjNlS1EiLCJtYWMiOiI4OWQ0Y2YzYjQ3OGI4YWVkNzdlNDVlNDkyZWNiODY1ZDk0N2RjOTMwNTg5NWJmZDQ3NjdlNDBkNzM1ZTRiNmMxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IklidnBDZHp0N0hid1hrd0xoc0p4THc9PSIsInZhbHVlIjoidFpKTTR3NXNKSC8rdEFpK2FFVUhQY1VpSld0d3pMaEkyN1BBamw5QnBOMkV3UXZYcXhsNkpSd1cwODI0NmZnWnppS3NoWnEzemxRQ3lMNVVXMjlmMjg4ZStQZlVkYVAyZUFzaWw3R1RTeWdhVWp5SWJ6RWJVYkNsSUFRMnF6V3FpVmlCY2NwZThWMHh0TVlCaGpjZ2ozbUkrRTRJbUFyZEIva3VHdmxMWndVSlNmTy83NytTenpJdllnRm1DZ3dBS3B2dWNVc2w3Q1QzTnVJYU1Qdmc2eUw5TjhJbXo4eGFVTmd6QjFvZ3VkbEwxQ2JTVGlhMHMwUmV0NS9lT1lkbFo2UWNiYVhhU2FLbWNhSkFoRit2L2x3aWxvRXI5VjdOU0FSamFvYlBOR2xEMkwvRWxmWjRQdTlZM1h5WWJoc1J0UEZwOE5NdFVheDVRS1MzZ0xUZEs1VHFNOTIveE9TMm5EYlZSMkJDZG9TVWhtanJPSmhEOHRyTHVBUGlLMjh6TW9VdlFSRmxZNjVIcElYdW5LelpxME9nVUNnTzNicHpJaXBJRG1oRkNvZHFWMHdDZmE1RVN4ZmdnRzJNQUIyejR0bUpGNVNiNXZWOHRPaVl0NmhaMU1Kc1NpY3V3UWNaSmpwY3ZRczlQRm8xMVY0bkhBeXVpbXRXYy9jOTFnZWkiLCJtYWMiOiI3ZmZlY2Q2NTlmOGFlYTM1MjIxYmVjOGI0MmJhNTEzNzRjZWRlMjI2M2Y0OGJhZWI3MjJjOTYzZTlmMDE1NjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1102804667 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:46:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InM3RkFKQVByd0oxWDNielBtWmlibXc9PSIsInZhbHVlIjoiTWxaVHdvMU1ISkdZRmFHV3pHbFkwRE9mSk0wbUNLNnN6a05ZMjhidVk5QnhJWUdjTDlrTHY3cFZ2Y21BMTRrSmZ0NEdhcUdKV0RobHlreElldmxid3o0RVZVaVk4eXRyQ3lrK0x3QzRtOFg0UXNsR2ViVFB4RWM2ekNHeCtpZmVFdWJOdnI2Rjd4RnUxb0l4YmxYMXgxOTcxSExkYllzVkNQcjBEK2xOamtiNVBjaEJLeEhLcHpMc0RZVzFWRDVpMkpzaWxnbFNWa2lNcHV0MWxsemhzN0hSRWpQQmx0TXQ1NWpORnd6L2s2SDRJdEhaVjREaEJpeGY5RTlFWFhWYVlhR2wxcG5QUUU5a2FzNWp3L0RtbnhmREhhNXJKYStLVzh1WDRGRlY4QzFjZ0ZFaS9NTEZiT21rb0d0blhwVHVtaDhEYVlqbURqRGJGUjA3VE5hd2pZRlBnSTBoR1ErV3FaeHVjbHBnWU9TMzgrQXJGVEgrRk5zcUc2MnlQMlFmVkszand1UE1Hem9hd2FoRGxLMjR2SElFKzE0d3lSNXlEOEh6SXhBangxeXlPaTRlYmxzazVzWHB5QVNCcXJJbHAyOEpyMk9JQmdJaG1QYlhMM05NMUlNSTU0cWhzNEI2Zko3K3hCQldNdm5lZDRNc1k5SzNQbDFUQ1BCTnd1WjIiLCJtYWMiOiIwY2ZhYjQ5MDFkNTJiMTI1Zjc3N2FhZjc5YzUwMjdiNDk1MjZjODM5YWQ2YzJiMjM4Y2Y3YzA3Y2JjMjg4Y2EyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:46:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZaVHZxUzBTY1dqbktmeTJnQUx6WFE9PSIsInZhbHVlIjoiRnNhWGJIOURMTWJTSFhUZ3NIcGdDZ3JMT1A0MFE2bmNKOEk3M3BkaUpuRXlEdWduUkhLL3ZEZFV1aWNzbDVtUWQvcEV5bTY1cWVJU2F1Qjg0VjFpemdabVVUVm9MMnArKzBwa0UrTytzcDliOVBlamJ1Y0drVzdkbGYvbmxVNDZnVTlwc0JWcERweE53M3hVSFh0eUhYM1g5RVNoR3Viem80ZCtBVjBEVXQ1RjRqN3pDaFFGSDRDMGhPNXZSWnl0WjVacXlTU2ZMWGRSVlQrMFVaVDJpaTdhK1JRWGtFbzBEYnhqTDlJL0FGT1ExZllZL2d0bjJaN0hWUVZ4Nys4dCtpdjJTMWVkejVaTHBtcWZqcWJrY3ZHWFo5eml0bjRPODFqYitNcm8ybUs0ZUNxNExML0wvZTgvaXNhMnpnVFJCdFBRTmhJMzNtanZkMWpSK29MRHBZd0F4WVcwdTRtS1I0ckdEZDNMeUxTS0V3VDhzOXpnamtzaGRmMWRPdm1kVCsxdTVNY0t0b0ZOUDM0WDNJUTVsV0trQWlBQU45QUJQY3MvQ2o5NHZiZlgwWVlNWG9OUWhBS3FJQ25EZ2YxV1J0VHIwTVVnaHJ2OGRrNUwwU0FEb1loM0pFM0tWL1BuTE1Hd2o2K2VKdHZDUXZqT00xSTRhZm0wdzJUUHFaU2QiLCJtYWMiOiI2NWM3ZTJkOWNkYjJkZWFmZTY1OTNkZWRkNjZiZmZlYjVhNWNjYmM4NTQ3OTVlMDgwMjVjYWZkNTAzNzQzOWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:46:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InM3RkFKQVByd0oxWDNielBtWmlibXc9PSIsInZhbHVlIjoiTWxaVHdvMU1ISkdZRmFHV3pHbFkwRE9mSk0wbUNLNnN6a05ZMjhidVk5QnhJWUdjTDlrTHY3cFZ2Y21BMTRrSmZ0NEdhcUdKV0RobHlreElldmxid3o0RVZVaVk4eXRyQ3lrK0x3QzRtOFg0UXNsR2ViVFB4RWM2ekNHeCtpZmVFdWJOdnI2Rjd4RnUxb0l4YmxYMXgxOTcxSExkYllzVkNQcjBEK2xOamtiNVBjaEJLeEhLcHpMc0RZVzFWRDVpMkpzaWxnbFNWa2lNcHV0MWxsemhzN0hSRWpQQmx0TXQ1NWpORnd6L2s2SDRJdEhaVjREaEJpeGY5RTlFWFhWYVlhR2wxcG5QUUU5a2FzNWp3L0RtbnhmREhhNXJKYStLVzh1WDRGRlY4QzFjZ0ZFaS9NTEZiT21rb0d0blhwVHVtaDhEYVlqbURqRGJGUjA3VE5hd2pZRlBnSTBoR1ErV3FaeHVjbHBnWU9TMzgrQXJGVEgrRk5zcUc2MnlQMlFmVkszand1UE1Hem9hd2FoRGxLMjR2SElFKzE0d3lSNXlEOEh6SXhBangxeXlPaTRlYmxzazVzWHB5QVNCcXJJbHAyOEpyMk9JQmdJaG1QYlhMM05NMUlNSTU0cWhzNEI2Zko3K3hCQldNdm5lZDRNc1k5SzNQbDFUQ1BCTnd1WjIiLCJtYWMiOiIwY2ZhYjQ5MDFkNTJiMTI1Zjc3N2FhZjc5YzUwMjdiNDk1MjZjODM5YWQ2YzJiMjM4Y2Y3YzA3Y2JjMjg4Y2EyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:46:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZaVHZxUzBTY1dqbktmeTJnQUx6WFE9PSIsInZhbHVlIjoiRnNhWGJIOURMTWJTSFhUZ3NIcGdDZ3JMT1A0MFE2bmNKOEk3M3BkaUpuRXlEdWduUkhLL3ZEZFV1aWNzbDVtUWQvcEV5bTY1cWVJU2F1Qjg0VjFpemdabVVUVm9MMnArKzBwa0UrTytzcDliOVBlamJ1Y0drVzdkbGYvbmxVNDZnVTlwc0JWcERweE53M3hVSFh0eUhYM1g5RVNoR3Viem80ZCtBVjBEVXQ1RjRqN3pDaFFGSDRDMGhPNXZSWnl0WjVacXlTU2ZMWGRSVlQrMFVaVDJpaTdhK1JRWGtFbzBEYnhqTDlJL0FGT1ExZllZL2d0bjJaN0hWUVZ4Nys4dCtpdjJTMWVkejVaTHBtcWZqcWJrY3ZHWFo5eml0bjRPODFqYitNcm8ybUs0ZUNxNExML0wvZTgvaXNhMnpnVFJCdFBRTmhJMzNtanZkMWpSK29MRHBZd0F4WVcwdTRtS1I0ckdEZDNMeUxTS0V3VDhzOXpnamtzaGRmMWRPdm1kVCsxdTVNY0t0b0ZOUDM0WDNJUTVsV0trQWlBQU45QUJQY3MvQ2o5NHZiZlgwWVlNWG9OUWhBS3FJQ25EZ2YxV1J0VHIwTVVnaHJ2OGRrNUwwU0FEb1loM0pFM0tWL1BuTE1Hd2o2K2VKdHZDUXZqT00xSTRhZm0wdzJUUHFaU2QiLCJtYWMiOiI2NWM3ZTJkOWNkYjJkZWFmZTY1OTNkZWRkNjZiZmZlYjVhNWNjYmM4NTQ3OTVlMDgwMjVjYWZkNTAzNzQzOWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:46:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102804667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-455437103 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455437103\", {\"maxDepth\":0})</script>\n"}}