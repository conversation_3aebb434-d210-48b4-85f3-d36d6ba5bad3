{"__meta": {"id": "X3a90a7667e82f9a5e636a355391252dc", "datetime": "2025-07-31 16:43:32", "utime": **********.356285, "method": "GET", "uri": "/finance/sales/contacts/search?search=ha", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980208.724642, "end": **********.356333, "duration": 3.6316909790039062, "duration_str": "3.63s", "measures": [{"label": "Booting", "start": 1753980208.724642, "relative_start": 0, "end": 1753980211.876167, "relative_end": 1753980211.876167, "duration": 3.1515250205993652, "duration_str": "3.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753980211.876203, "relative_start": 3.1515610218048096, "end": **********.356338, "relative_end": 5.0067901611328125e-06, "duration": 0.4801349639892578, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02816, "accumulated_duration_str": "28.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.123145, "duration": 0.01678, "duration_str": "16.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 59.588}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.229508, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 59.588, "width_percent": 11.435}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ha%' or `email` like '%ha%' or `contact` like '%ha%')", "type": "query", "params": [], "bindings": ["79", "1", "%ha%", "%ha%", "%ha%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.26458, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 71.023, "width_percent": 19.709}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ha%' or `email` like '%ha%' or `phone` like '%ha%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ha%", "%ha%", "%ha%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.296147, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 90.732, "width_percent": 9.268}]}, "models": {"data": {"App\\Models\\Customer": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1649203742 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1649203742\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-658909284 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ha</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658909284\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1318574568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1318574568\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1872920322 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNFTDlvYnJGVWluWjlyUFNiTVIwSmc9PSIsInZhbHVlIjoiRmlWYUdGdnB0N3hudGtDcVUxdFFGV0NzQ05OSjJMTWFIdXFDamo2Yy9SVFpRbkp1M1RkVk5oS1FURzZOMnpiQkdkMmc4ajFoTW93ZlkySUFuTkwxODhxcHJUN3AydGYwR1dZdHQrNDZyVnJNc1MvMElHbFY4bjA4YXdWekt6b2hQRWFncVBwT2ZoV3RiYWswQkluT25tQ1FWcWFRRHVrcHRyRWhSTGdZRjFFUzhra3RYbEFpZTBsVDRMYXV0NlEwQmpFZ3NwTTRrK1JDdWhZdVpPTHlyRlR4K1hMdFQ3eExKUG5qRks3VXFqZFAyRUZGWDVMdDhmVnUrTzBFc09Ecnk0c0podlhyR2ZIRkZueVZ6Z0JBSTh2cWRzZ29kTWVnVjJuRzRGV0c4RTRoRzZBV2RRVkYrK2ZoazZ0Rko2YlBQa0lMeVFzZVk1WG9RZytnMWNsT2orS2dNRWE5dDBCWldRN3M4VG1HSFZWZ0hYYXJLQ0tLUVc2VlJOU09Gb05QQlpJSjJvMkZqeHBhRG9zL2ZqS09YRFpLOTRFcnFzeVhvNFZYVG5qMXhVdG8xTHJwblNvY0RkQnp5TnU3dHZPZmwyYy9ld0ZMdHJpMzFPZ0ZvWkZaV2FtQkRKb0J6WmplcXRGTVVXUFhibjkwTFF3T2FTWHJ2OStNRW5WeEhXeTMiLCJtYWMiOiJkZWUyY2RmMTgzNDJjOTgxM2MxZWQyZDBkZWFiZTkyYTQ2MDA5NzYyMWIyNWM2NjM2ZWNjMDc0M2EzOThhMGQ4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImdDeCtDa1RGc2NkTlhJMkd4RFYxN3c9PSIsInZhbHVlIjoiSzFKeDh4OWJlL1ArelhTSi9hdDU5dkRJUlB0Z2srbmx1UWxhaEkyMElKV3BETnpFd1c4S1oxNDM4U0FUTUI4ZEREM1hXcDY0N0l2alFhYWRQTWxPdDZMajkzczFncXozWE5TdGtiSzhHZTJEd2dQeHZnSXpHRlZuU3NnUzhubFl4QThZSzdldStRMXZjbVRlV21pSDVSci9YRWFQNEdvQVZGVVdhT285WUlDcjQrVE1HclE1NFNIYmdJTEhmamZURDh4TDJud1NWbmlJeVJNS0xTOVNFVzgyY0haWjJxTGNraEttQ1NoNkNNOTdBbW9FQ1hpR0N2Nk01cEVuNDMzZEFwK2Nvb05ITytqY0gwOE54dnF1dThTak0vcTloS1IrbGVjVDVrZXlEd25FZy9qYjUyZ083dFptOERxOWZaM1NGNTRYQ1ZzNTMraktRWmxJQ3hFRHJmQzBUSTVxbWZGdGh0aHhlb0lEMjM5TmJBTE1OMGxUdVhZZTVVMWl1OFk3cnI3ZmVZWG83bENnTm9HMUJiS3d4RGdPcnkvdStVK1A2OXRNMkJ3N2ZSNTB4OVNCMEppRDJ5Uy91Nks2NGRVd2YyL3E2ZGROVzU0K0x3aFRpd3FjbFlleFNLbFhZQStaL2drRG5lTkFhVHZhZW1YNmRBNjhLTXBsSVh3ek1pcXIiLCJtYWMiOiI5MGQ5ZTNiOGM0YmNhNjkxYjA2MzIxZmZkN2YzNjMwZjgzYTdlYTA2N2MyZDFlZTg0MDZhNmU2ODJkNWQ2MGEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872920322\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1654414109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654414109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-663787179 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:43:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBSK05ybHl2KzNZNllOVE0wTm9SS0E9PSIsInZhbHVlIjoic2EyVVU1NEtkTnJUeFdqeURzK09sYVJlSHlIejJsbHgzTjlFblpTS1hSaUZNSjNUTmlieVFNbmNqSDBneW9HaTlJb0t5T3pIcW5JQW4ybGdScGJsOHNHY3VZK2t4VTFTS1plbVZQMm0vRTJZTHVLd3AvcGNUUEJpcVE5OWd2TzRhejQ5aGxReVU5VHVTSTZ5TXE4T1BlWjVzamI5ZEVBZW9laitaYWI2cVorUnRGd21kZ0h0QzRKU09GeENkYzNNV3FkS2VhUStTUGV2aUZnNXJEVWw2MytadmtiQ05HaWxkeC9ENzNXTGd2TTA4VnBuNk1LMHJsL000RGh5R2hCT1hCNEVZNXo4bTd1bXdZYllna2dqaXhEcUVkQ2FkS2NpSTVBTDUrSU12aVRGV3ZId2VDTTMxaDlsdDRGcmFwcXg1bkRHbkMwT0pobjFRYjJHdkFWb3o1TFFFSVdmUHV2Tjl1TGQycWNHNEVDeUxxMUxPbWhOOGZmVHZxSElLZGs0YmlMYVA1ZXBIaHBiV0R0dEozZE1DMnVJODlSa1BINVNPYjhDNWRDRlVDTUxadlZzZHNRSzZIK05qRkI2bFVwb1pDSTJNU0RUTlpVL3JjNVFSQ0hDYmFpUThnZjgrWmNBOStOQUR1V0ZXYnY2ayswZ25HNzZxODJnWVBNRUpPSFUiLCJtYWMiOiJhOTY1MWM4MmUzZTMwNjI0ODQ1ODI2MDNiYTlkY2ZlNTVjMWJmOTEyZWM4YzJmZDg2YWI0ZWE4MmQ5YzQwN2M5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdER3E0WTA2ODZGOEtPM1dnS0picnc9PSIsInZhbHVlIjoiZVI5YXNmd0RhemRLMndXZGtzVE1qZENWWmtnSzI3WDZMUzhTVXdCR0NmVGkwQ2o4TmZCVWZybytWRDVwT2w1L1ZXaUhQM0xBWUpTWEhadDRvOHZ1MHBkOFB5WGdkTTYzYWhjMlBtc2hZOUNOWUJMVzlGR05VRlRRTE9QcGp4Nno4cVBKKzFMNFdnRmlNOHFaVXdzaCtQQ0kyNzduTHZTRnFHV0tlYlZNaGJCWHFFckpzSzRkNUtGdC9zL3BNOEZhckdlVEdNWk1JRTBETTcwVjdmcFhOSDJmaUZrMDNxOUloUmxmYWM1clQ3cU82a3hNbk1BTzVRK3Qzb3pydS9SSHVJdWxWemdCYWw1cmhRTjQ2Z2htUkQ5YnpHczZxZ0Jjclc4ZFFERkcyYU9JTWgxTUxSdy9qbGNFekpxM3RxMDU0eUFhY1dJR0c3QzU4NUMxQ3l2bzZMSlc1RElsSUJuRXJBbnNQRnVWY2JtWEl2cVZnUHJ1eEgzUlh2VlFkMXVqanYxeU1IaXBwaFpCTS9XU2JNdks4RDl3M2VISlpGUkpSUUNpN1NwRHpIcHlYNlBJZElsRU56L3ZSZUZRMDg5VFgrSVdNTzJ4VU1XTTRxSmFzb2tLam9KclRFcTl5UkxsdGtHMStQeFV0YXM2RXhBbkRCYUZlVEZVR0RzOTVveFMiLCJtYWMiOiJmNzIzMGEyZTc5ZTlmMDcxZmJkYzkwZDI4ZDE4ODVhZTJjNWRjMjJjMzA2NzNkNDRkZmFiYzAxZTdhM2U0N2I0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:43:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBSK05ybHl2KzNZNllOVE0wTm9SS0E9PSIsInZhbHVlIjoic2EyVVU1NEtkTnJUeFdqeURzK09sYVJlSHlIejJsbHgzTjlFblpTS1hSaUZNSjNUTmlieVFNbmNqSDBneW9HaTlJb0t5T3pIcW5JQW4ybGdScGJsOHNHY3VZK2t4VTFTS1plbVZQMm0vRTJZTHVLd3AvcGNUUEJpcVE5OWd2TzRhejQ5aGxReVU5VHVTSTZ5TXE4T1BlWjVzamI5ZEVBZW9laitaYWI2cVorUnRGd21kZ0h0QzRKU09GeENkYzNNV3FkS2VhUStTUGV2aUZnNXJEVWw2MytadmtiQ05HaWxkeC9ENzNXTGd2TTA4VnBuNk1LMHJsL000RGh5R2hCT1hCNEVZNXo4bTd1bXdZYllna2dqaXhEcUVkQ2FkS2NpSTVBTDUrSU12aVRGV3ZId2VDTTMxaDlsdDRGcmFwcXg1bkRHbkMwT0pobjFRYjJHdkFWb3o1TFFFSVdmUHV2Tjl1TGQycWNHNEVDeUxxMUxPbWhOOGZmVHZxSElLZGs0YmlMYVA1ZXBIaHBiV0R0dEozZE1DMnVJODlSa1BINVNPYjhDNWRDRlVDTUxadlZzZHNRSzZIK05qRkI2bFVwb1pDSTJNU0RUTlpVL3JjNVFSQ0hDYmFpUThnZjgrWmNBOStOQUR1V0ZXYnY2ayswZ25HNzZxODJnWVBNRUpPSFUiLCJtYWMiOiJhOTY1MWM4MmUzZTMwNjI0ODQ1ODI2MDNiYTlkY2ZlNTVjMWJmOTEyZWM4YzJmZDg2YWI0ZWE4MmQ5YzQwN2M5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdER3E0WTA2ODZGOEtPM1dnS0picnc9PSIsInZhbHVlIjoiZVI5YXNmd0RhemRLMndXZGtzVE1qZENWWmtnSzI3WDZMUzhTVXdCR0NmVGkwQ2o4TmZCVWZybytWRDVwT2w1L1ZXaUhQM0xBWUpTWEhadDRvOHZ1MHBkOFB5WGdkTTYzYWhjMlBtc2hZOUNOWUJMVzlGR05VRlRRTE9QcGp4Nno4cVBKKzFMNFdnRmlNOHFaVXdzaCtQQ0kyNzduTHZTRnFHV0tlYlZNaGJCWHFFckpzSzRkNUtGdC9zL3BNOEZhckdlVEdNWk1JRTBETTcwVjdmcFhOSDJmaUZrMDNxOUloUmxmYWM1clQ3cU82a3hNbk1BTzVRK3Qzb3pydS9SSHVJdWxWemdCYWw1cmhRTjQ2Z2htUkQ5YnpHczZxZ0Jjclc4ZFFERkcyYU9JTWgxTUxSdy9qbGNFekpxM3RxMDU0eUFhY1dJR0c3QzU4NUMxQ3l2bzZMSlc1RElsSUJuRXJBbnNQRnVWY2JtWEl2cVZnUHJ1eEgzUlh2VlFkMXVqanYxeU1IaXBwaFpCTS9XU2JNdks4RDl3M2VISlpGUkpSUUNpN1NwRHpIcHlYNlBJZElsRU56L3ZSZUZRMDg5VFgrSVdNTzJ4VU1XTTRxSmFzb2tLam9KclRFcTl5UkxsdGtHMStQeFV0YXM2RXhBbkRCYUZlVEZVR0RzOTVveFMiLCJtYWMiOiJmNzIzMGEyZTc5ZTlmMDcxZmJkYzkwZDI4ZDE4ODVhZTJjNWRjMjJjMzA2NzNkNDRkZmFiYzAxZTdhM2U0N2I0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:43:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663787179\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1056522938 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056522938\", {\"maxDepth\":0})</script>\n"}}