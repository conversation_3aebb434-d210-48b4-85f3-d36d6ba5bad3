{"__meta": {"id": "X08de4e3462824c301690f1416bc01cc1", "datetime": "2025-07-31 16:23:39", "utime": **********.392162, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979016.84161, "end": **********.392282, "duration": 2.5506720542907715, "duration_str": "2.55s", "measures": [{"label": "Booting", "start": 1753979016.84161, "relative_start": 0, "end": **********.108308, "relative_end": **********.108308, "duration": 2.266698122024536, "duration_str": "2.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.108354, "relative_start": 2.****************, "end": **********.392292, "relative_end": 1.0013580322265625e-05, "duration": 0.***************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QDhk0Cc4TBCX9Gp0X27WXbWjInnblTnZwI7tw4PB", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1643490466 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1643490466\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1328007767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1328007767\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1940093992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1940093992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-205235102 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205235102\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-442546382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-442546382\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-485710836 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:23:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpYTURybG5tRjN6WXNCTmV5dWdhTHc9PSIsInZhbHVlIjoiU29Xd3VvMWpRMmVia20vR2h1RU1LTVhTWFN0KzdQRFN3Zk9SNytFNnZzYlExMURJV0dWeHFXWVp4cmhmZW0zdC9uQjRZY2w3TTU1YjVFaTRnOHR2VGNySDFQM2VlRXpnUjErV00zS3RpaDdwckc5WXNDNVR6clJFZGRqNW1RRXVDMXVIZ1dQMGhvRjNzdVJPbTdBa3FHbnVhZTM0NENqTW8wT3diQVJsRXV6L2xwMVdiWWhBd3gvNVZwbFBUVkNQYnUzMXE1NWllL1dmWWdlQmMwbVRSb3N2ZzhBSTZuaGg5UGZpNG91Mi9lalBsdC8zbmlBc1AxWDAyMDg1RGtWMlRIL0wwMXRSbU9qL29GREtZRyt4ZFVOQkhnNWFZZ01ZQXRhaWJYWVdJR0dCanc4OWt6ditWU3ZCejk2TWV4S1R2Q24wRFR3WDVQYUJHTUlJZFlUYlMyQjlBVFJIaTQwZWhwREF4bEVkeEpHcldpY3hYU3QzNnhRcHFMaXJjNWNpSENZekkwTG9ZOERuZ2ZiREVhb1F1a3BXT0RTNURheFFwZDZlZjNpK3k4TTljVENCMmU3SWkyd29aMU9qNlBTVDlENXdCRUJGcVkvU3J4WFVZVS82Q2Q1TWFUVXB6WEtpR3JSTHU3Rk9MdjY5UTBjandHa3pzSnpibjltL0M2REYiLCJtYWMiOiI1NGQ5NDg4MmQxOGYwN2FmNmZmOWNiOGYxYjM2ZTJjNjExMTk4OGMwMmRiMzM5YmEyNDE3ZjJhNjk1ZDVhNDI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9udUkxc05mRnpncTJDS0RDWE5lamc9PSIsInZhbHVlIjoicTYvMEsydkpvd2dkVFE1SCtXQ3BWRkR6Q05kQ2pnc29BQkxqN0NrWTdMNSs0bzkzWjFKMVVJdVZNSERwdUlLV1Z4dHZLYURiSEovRmQvaGl3YmZZUDFVTi9WcVhEaXZtK1dLQmYzY20vWVNaMHJWeEZaRDRJWFpqbzl2NlA2ZWI1cEl5RG9EZEdON3dTZjlYcHZtbWsvTWo5akI3ajBpT2FjbEMzc2xta2dUc2Foa0c1cjV4clZXRERVWXVOYVRMaHVvR0xlZ01lRnYwdldjR2J1M0p5ZS9MZzVyUHcxdE93eWcrNjNKc2tzUVAwc2JYN2RlV0dGL0JpdWozcFJQMUlFTFRhVWxyd0RCTHAvcUVIUmNTUGxlWXV1S2xnQm9Nd2xVV3VDSUxYOEh4WnRPejBhTEJoWlVjaW91cGdNd3hWVnRUdWJiR0tDSTdyUVNVNWp1a1c3SzUzdEE3aC8xQmRHUWsrUnJFbytOT1hpMU12eElsVWJkTnhWai8rbUh6UDEwV3M1c0NXQjhHdkVjbkwrb1JlUXIxT0VVUHF2Y3ZwYkhNUGxGZ3V3SlhOUzdjRm4yancyWjFMSjloQVE4WjlHUjJNNW1KV1lIY2NnWS9oOHdHVlA3Vkh4WFM2ZExzc1Z0NlhBaHBETTNNTjV3TGxqN2xOR0xKQXZzZm96bFMiLCJtYWMiOiJmY2U2MTExYWNkOWIwNGJmNWMzMjdhMTZiOTFkNTMyNjVjNjdiNGM0MTMwOTE4YjE1NzNmMWRlZDQ5MWI4OTM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpYTURybG5tRjN6WXNCTmV5dWdhTHc9PSIsInZhbHVlIjoiU29Xd3VvMWpRMmVia20vR2h1RU1LTVhTWFN0KzdQRFN3Zk9SNytFNnZzYlExMURJV0dWeHFXWVp4cmhmZW0zdC9uQjRZY2w3TTU1YjVFaTRnOHR2VGNySDFQM2VlRXpnUjErV00zS3RpaDdwckc5WXNDNVR6clJFZGRqNW1RRXVDMXVIZ1dQMGhvRjNzdVJPbTdBa3FHbnVhZTM0NENqTW8wT3diQVJsRXV6L2xwMVdiWWhBd3gvNVZwbFBUVkNQYnUzMXE1NWllL1dmWWdlQmMwbVRSb3N2ZzhBSTZuaGg5UGZpNG91Mi9lalBsdC8zbmlBc1AxWDAyMDg1RGtWMlRIL0wwMXRSbU9qL29GREtZRyt4ZFVOQkhnNWFZZ01ZQXRhaWJYWVdJR0dCanc4OWt6ditWU3ZCejk2TWV4S1R2Q24wRFR3WDVQYUJHTUlJZFlUYlMyQjlBVFJIaTQwZWhwREF4bEVkeEpHcldpY3hYU3QzNnhRcHFMaXJjNWNpSENZekkwTG9ZOERuZ2ZiREVhb1F1a3BXT0RTNURheFFwZDZlZjNpK3k4TTljVENCMmU3SWkyd29aMU9qNlBTVDlENXdCRUJGcVkvU3J4WFVZVS82Q2Q1TWFUVXB6WEtpR3JSTHU3Rk9MdjY5UTBjandHa3pzSnpibjltL0M2REYiLCJtYWMiOiI1NGQ5NDg4MmQxOGYwN2FmNmZmOWNiOGYxYjM2ZTJjNjExMTk4OGMwMmRiMzM5YmEyNDE3ZjJhNjk1ZDVhNDI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9udUkxc05mRnpncTJDS0RDWE5lamc9PSIsInZhbHVlIjoicTYvMEsydkpvd2dkVFE1SCtXQ3BWRkR6Q05kQ2pnc29BQkxqN0NrWTdMNSs0bzkzWjFKMVVJdVZNSERwdUlLV1Z4dHZLYURiSEovRmQvaGl3YmZZUDFVTi9WcVhEaXZtK1dLQmYzY20vWVNaMHJWeEZaRDRJWFpqbzl2NlA2ZWI1cEl5RG9EZEdON3dTZjlYcHZtbWsvTWo5akI3ajBpT2FjbEMzc2xta2dUc2Foa0c1cjV4clZXRERVWXVOYVRMaHVvR0xlZ01lRnYwdldjR2J1M0p5ZS9MZzVyUHcxdE93eWcrNjNKc2tzUVAwc2JYN2RlV0dGL0JpdWozcFJQMUlFTFRhVWxyd0RCTHAvcUVIUmNTUGxlWXV1S2xnQm9Nd2xVV3VDSUxYOEh4WnRPejBhTEJoWlVjaW91cGdNd3hWVnRUdWJiR0tDSTdyUVNVNWp1a1c3SzUzdEE3aC8xQmRHUWsrUnJFbytOT1hpMU12eElsVWJkTnhWai8rbUh6UDEwV3M1c0NXQjhHdkVjbkwrb1JlUXIxT0VVUHF2Y3ZwYkhNUGxGZ3V3SlhOUzdjRm4yancyWjFMSjloQVE4WjlHUjJNNW1KV1lIY2NnWS9oOHdHVlA3Vkh4WFM2ZExzc1Z0NlhBaHBETTNNTjV3TGxqN2xOR0xKQXZzZm96bFMiLCJtYWMiOiJmY2U2MTExYWNkOWIwNGJmNWMzMjdhMTZiOTFkNTMyNjVjNjdiNGM0MTMwOTE4YjE1NzNmMWRlZDQ5MWI4OTM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485710836\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1856611638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDhk0Cc4TBCX9Gp0X27WXbWjInnblTnZwI7tw4PB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856611638\", {\"maxDepth\":0})</script>\n"}}