{"__meta": {"id": "Xb9c63bf1967488096eeea80822fb260d", "datetime": "2025-07-31 16:24:17", "utime": **********.099008, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979055.71539, "end": **********.099041, "duration": 1.3836510181427002, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1753979055.71539, "relative_start": 0, "end": 1753979056.898326, "relative_end": 1753979056.898326, "duration": 1.1829359531402588, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753979056.898363, "relative_start": 1.1829731464385986, "end": **********.099045, "relative_end": 4.0531158447265625e-06, "duration": 0.2006819248199463, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007040000000000001, "accumulated_duration_str": "7.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.009816, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 70.313}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.054442, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 70.313, "width_percent": 15.625}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.063787, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 85.938, "width_percent": 14.063}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1030664187 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1030664187\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-798799828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-798799828\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1872580176 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872580176\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-672840922 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkYyWjk1b08xN2pRRExrUmVtK0I0Q2c9PSIsInZhbHVlIjoiSnVQZkZlbjlxYTlRZXhtdzBVMzlNeFN3OUxQQTAyN2FZUXdXK2dRWHJOYmhWYW5DdE9jOXRsTDVxY3UvQjVwaUN2T2p4U3EzdUdnd1dZQlptUTcvTFJhWlovajl2Z0t6aEJxNkIvbktvelc2N1NURlNydFpLWTRNcnlWN1JjNHYrZGxTb2FlVGxlejVOU2Q4QWU5aUFpZXVleml6NWJPcm9DcGV0NDdZYTMrSW5kTHdZUU1GSUFqZlRXall1azA4L1hYaWlmT3RYRlhDb01xa2VGZ3lRZUF1VzJsVjlPMWRxc3ZjRnZJWUV3Y0xKVUdydEI0VXM0MW9HM051Wnd1ZFRYUkdSdng1NmRWUGxSLzBReHBhUDZTWUIwSzg2RW9oMWRBbjZwQS9KcmkrZjhwNkw0Y1BlS0pjR0k5VmJIV3loMkpad21UMGFNeTltOG9NOTV0bFBhZExWRWkvUHlBcCsrQVJkVVo4d1hNK2lyVWFHU1A3elNmQ0t3TnJSVnZFOFgwNkM5dTVhbVZhdUVZVGtJUGRKZGdIMjAzbW5PdUJWSUdJaEVlenlmSGgrSHhZSXVPajlzL2pkRkIrSmxCQlVFcC9TcW1mZnRuOXpuOTRNVWtpczFndkQ1aHB3b2NaNUJWcU1UWHBWTmJuZERTeXFnWWprNks4VC9IWUxpeXYiLCJtYWMiOiI4ZTViZmY3NDhhZjBlNTNjM2I3YjJlNmI4Y2IyOWI3ZmRjNzFkYmJkZDA1MjQ0OGFiYWUxNDBjOWRjZWRiMmU4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1aN3gzNlJXY1I3aTBlMkRKSmx4MEE9PSIsInZhbHVlIjoiMzdjYWJ3YUR0N1B3TzZGNkpMUFFYTDNmVXlvbUR4WG1MTEFQa0NDV1V5S25laHo3L05STXVKOU0wVzQzdURyNDg2OFArVmJTK0lheFFJOGt4c05uMGFNK2dHUUtYQmh2M2JWVEQrNW43NnRmRGg1dXBlRVNqekVFRDlJK1JpWmduSzZ3M2lUQ0tZeDhDcmFPU0NpS1UvU1NFb21ZWWtTTTRvKytIYXFBZ3JBeWZYOENXZXhWWXhjZmN1UWdablRESnBDU2dSUmdhUmlqTDlDZ1dmWCtTd3g3Sm1PTnlGaWlGTmc1VStFMVVoanBMMVRab3YyRldrb1EwTXVndng3eWEwYjNKMXlBYW1wOEVuWWlZVUVxbWV0eXI0WkhzRGlWZnFsZWN2TzlVd2pVczRWMHlrcGN5T2hqblZOQVhpMEVZZVVHR3JSd012TllXMElOUWNrMWlDa3YyMlA0Nzc1UmVSUVJPbzhGYWxDcW1rWm9NS09SRHc5YkRWYjJsWkd3dU5GYWlBL1VzLzFtWTEzbEJxYkljdlNwNDcyeno1UnNDTzdQdzE1a0pIR1phcHFURVMvZlBHVW5DWDF4MEE1U1JKd3ozNmhTMzBVbHhVQm0vbnBjeUNjbmxFRkJFL2Z6SDI5dEhWY2plbXpmajdORms2a0YrZDJYc1FFTzJDZk8iLCJtYWMiOiI4MTE0N2RhOWIyYmJjZDE4N2QyZGU1NTJkOWQzY2QxMzA5NTFiZGIwY2EzYjZiOTczZTg1NTk0OWM5ZWRjOWUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672840922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-724260459 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724260459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2107019761 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:24:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjEvbkIxT1F4WmEyR3RWQmYyRiszR3c9PSIsInZhbHVlIjoiNTN2OEwveEdESmlxdXViOHptVEVLY1l4VGpTeUlRREdmeTFsMktTVElVemhta3QrRml4cjB3WkJEWVkzTFpjWnM4NFJ0dTVzblBNVlEzR1dnM25IOUFCeGtCb2hoZGNFR0J1UkdCUTFxNjVhQ2VTbGhOMUliNXNUb1JSaGkwRjYrSHg4aXhFTlh0SlUyRXZjWlhWMG9EUzJtNHBLNVlzc21DRkNsZnkrL2hsTDdBRThBZXVqcWhLL3RhQU9USVNyL090SkYxM2V1SnB1N3hPYlVpR2szZjcwc2F1dDdjSHNja0R3MGMzL2UrdWRFb2RuNnQ2Ni9ERjlhc0ErbHVBSVJib0dWcDY4OXJEcDZIaVRqMkFTRmx4QkU1eUVtZ2Q0TEV4Q3hucTBGeWc5dk5JS1BpSEdhTURCNVFEZlkzbVJlcWpyUHowRXhJMjdBeElnWk54UlpsZENsUTFhcWZobG9wWVRXU3ROenRwaCtkYlZOdnQ0MHlVNnpnU21iRlNsWUczdWJkV0pUYzFvOTAzSmdtTXAzMjh1ZTEzaU8xWS90RU9EaEdBdUJVK0RCeGZ0N09WcHpEUFU1YWRZci9EaE9KNjBOY3poU1QweXdSL1pHckZjS1plNC90ekFObVJ5eGwrMnVkTmdNNjJpaktGbzV1N3pYTmd3Y1dtNUtvZXIiLCJtYWMiOiI0ZDQwZGZjZjRjMjM2ZWU4YmEwZWRiYTdlMjc5ZjM0MThlMmQ0OGE4NWE3NDJjNzgzODJmZjQ3MGJjMDA2MmYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:24:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5lNEMwR0dwZkt1N2pyY0Y0eER5QlE9PSIsInZhbHVlIjoiUnpNRWYybVd5QnBadVp1aWRtNzhFWUliL0tSSG9CdXA4aVQvN2xNckNzTkk1V3phQWtrbHVoaktsVGtpSHlQMFJ1UGo2NnlZVjNTVFFUZjNnUk0wcmR5WVp5b0tTaFZPV2s2aXZKOGVpNUVZUzlCMEdtbFF4N1dCcTM4L21qSXdyRnRNVlFTQ1Q1d0VEZlpjZlVIK0xEa3c3MDllUzBKNjVBbmhRd2lSNHJJZG8vRUs4c0J5aXBsSGNndXBaQXBoRUx4engrMG0yczVta0FEcG5IY2lBVjVNM3RXMVl5RDhNdklNdEplZUNRNjdLdkFZSW1qYnY3MmlWbURpQ3A5K0FIdHBjeHkybWEyR0NOOWJ4VkhjT0JCV2Vrc3NsOVB0VkZTR1hDa1lDcE51dS80MVMxSE5oKy9vVVpkYU40UmFXd0I1aW5GSWMxUytjMzNHY29KMEp2c3R2S3daTWIvMDRLa0NEengyZ3cxV1V4NDBCc2l6V2Z4ak5NbUJxRnpNWnVJUUloNS9KZUtUcnovUnN6OXR5M1J6T29pS0pkenBNSThxSTR3ckhHb0x0WTd0S2FFeFljQVJTT0pTZEtUOWVpNStPcjIrRFAwYlZYM3pISzlSY2M2OEZFTUNoekZHc21LK3MzdjNGZWluNnpVbUovYVdOZFZac3RNclk3a3giLCJtYWMiOiI3MWY2MWEwMzNhYzUyZDU4Yzc5MzQ1NzVmYzgxYzUzZmE2NTYzZmQ5ZDdiZWQ5NTAxMzJmMmI3MTA4Nzc1MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:24:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjEvbkIxT1F4WmEyR3RWQmYyRiszR3c9PSIsInZhbHVlIjoiNTN2OEwveEdESmlxdXViOHptVEVLY1l4VGpTeUlRREdmeTFsMktTVElVemhta3QrRml4cjB3WkJEWVkzTFpjWnM4NFJ0dTVzblBNVlEzR1dnM25IOUFCeGtCb2hoZGNFR0J1UkdCUTFxNjVhQ2VTbGhOMUliNXNUb1JSaGkwRjYrSHg4aXhFTlh0SlUyRXZjWlhWMG9EUzJtNHBLNVlzc21DRkNsZnkrL2hsTDdBRThBZXVqcWhLL3RhQU9USVNyL090SkYxM2V1SnB1N3hPYlVpR2szZjcwc2F1dDdjSHNja0R3MGMzL2UrdWRFb2RuNnQ2Ni9ERjlhc0ErbHVBSVJib0dWcDY4OXJEcDZIaVRqMkFTRmx4QkU1eUVtZ2Q0TEV4Q3hucTBGeWc5dk5JS1BpSEdhTURCNVFEZlkzbVJlcWpyUHowRXhJMjdBeElnWk54UlpsZENsUTFhcWZobG9wWVRXU3ROenRwaCtkYlZOdnQ0MHlVNnpnU21iRlNsWUczdWJkV0pUYzFvOTAzSmdtTXAzMjh1ZTEzaU8xWS90RU9EaEdBdUJVK0RCeGZ0N09WcHpEUFU1YWRZci9EaE9KNjBOY3poU1QweXdSL1pHckZjS1plNC90ekFObVJ5eGwrMnVkTmdNNjJpaktGbzV1N3pYTmd3Y1dtNUtvZXIiLCJtYWMiOiI0ZDQwZGZjZjRjMjM2ZWU4YmEwZWRiYTdlMjc5ZjM0MThlMmQ0OGE4NWE3NDJjNzgzODJmZjQ3MGJjMDA2MmYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:24:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5lNEMwR0dwZkt1N2pyY0Y0eER5QlE9PSIsInZhbHVlIjoiUnpNRWYybVd5QnBadVp1aWRtNzhFWUliL0tSSG9CdXA4aVQvN2xNckNzTkk1V3phQWtrbHVoaktsVGtpSHlQMFJ1UGo2NnlZVjNTVFFUZjNnUk0wcmR5WVp5b0tTaFZPV2s2aXZKOGVpNUVZUzlCMEdtbFF4N1dCcTM4L21qSXdyRnRNVlFTQ1Q1d0VEZlpjZlVIK0xEa3c3MDllUzBKNjVBbmhRd2lSNHJJZG8vRUs4c0J5aXBsSGNndXBaQXBoRUx4engrMG0yczVta0FEcG5IY2lBVjVNM3RXMVl5RDhNdklNdEplZUNRNjdLdkFZSW1qYnY3MmlWbURpQ3A5K0FIdHBjeHkybWEyR0NOOWJ4VkhjT0JCV2Vrc3NsOVB0VkZTR1hDa1lDcE51dS80MVMxSE5oKy9vVVpkYU40UmFXd0I1aW5GSWMxUytjMzNHY29KMEp2c3R2S3daTWIvMDRLa0NEengyZ3cxV1V4NDBCc2l6V2Z4ak5NbUJxRnpNWnVJUUloNS9KZUtUcnovUnN6OXR5M1J6T29pS0pkenBNSThxSTR3ckhHb0x0WTd0S2FFeFljQVJTT0pTZEtUOWVpNStPcjIrRFAwYlZYM3pISzlSY2M2OEZFTUNoekZHc21LK3MzdjNGZWluNnpVbUovYVdOZFZac3RNclk3a3giLCJtYWMiOiI3MWY2MWEwMzNhYzUyZDU4Yzc5MzQ1NzVmYzgxYzUzZmE2NTYzZmQ5ZDdiZWQ5NTAxMzJmMmI3MTA4Nzc1MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:24:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107019761\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1367428565 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367428565\", {\"maxDepth\":0})</script>\n"}}