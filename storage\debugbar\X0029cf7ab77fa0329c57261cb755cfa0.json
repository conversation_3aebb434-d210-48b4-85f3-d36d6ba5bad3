{"__meta": {"id": "X0029cf7ab77fa0329c57261cb755cfa0", "datetime": "2025-07-31 15:59:54", "utime": **********.483607, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977592.952161, "end": **********.483681, "duration": 1.531519889831543, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1753977592.952161, "relative_start": 0, "end": **********.177777, "relative_end": **********.177777, "duration": 1.2256159782409668, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.177815, "relative_start": 1.225653886795044, "end": **********.483689, "relative_end": 8.106231689453125e-06, "duration": 0.3058741092681885, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47012640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03365, "accumulated_duration_str": "33.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.316966, "duration": 0.0245, "duration_str": "24.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 72.808}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3885298, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 72.808, "width_percent": 6.508}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.412592, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 79.316, "width_percent": 9.45}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.43364, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 88.767, "width_percent": 11.233}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1377968553 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1377968553\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-128952316 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128952316\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-322073795 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-322073795\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1340178112 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZZN1FNNU0xVVcybnc2VzYydFlnSkE9PSIsInZhbHVlIjoia2JiVVNOVGEyQUEvVFoyZzY1OWVJcW10MkJZUGNFcU1hRXBhZC9Bb1RaS1B4dlFkZWJJNFRqSEFvalVCMFdDdDFPVUpBRUhSYUVNSzFUL0JMZUpkOEpEVEd4VTUzeHEvcnJpV3lRYkhkd3liWTkzc08zd09xSlY4L05ieElBWFk5NW9vS0tHSGJnSWRoTDA3TTdCbnI2eUNyZDBmUHRVSHhXTVpYVUZDQ0ZmQ1prbGFxTkw1cS9sbE9WdnF2aXUrNjNZUWhVeURvUXFhUHpVSVZTUEIvVHpwWk9WNmR6SXNIaTZ4VXNaVnROWmNtQ1d0TVhNOFk2bjBhVHlDRklLMGsxM2d4LzNXdmhSU09VeXVZN2hSbFpSZFlvQXRxYk5SVjRrT2Viclk2SysvUzgrL1hDZFZ0ZnZYZWM5enhKT0VsREpMOTFOTUpacDNYaUFtbEljME0xNHZrRXlKSko1YjhmY3Q5VUU5QjJQQnorazZKVkZscWVTVWtBYmFTNnpxMTlMdlNNR1hsQVhBbGE0ZlhLRVE3bTJ3TGoxZGxhTnRGeXN2cXR2bUgraGNNbmc4aUQxY25NY1lGbkJzTFVUZ2g5d2RXWStUUVc5YnhkaHg5NWtpT3BoTUNHenZXNDBzZDNUR2F5NXhYdm90ZVVodGpSeDMwWXo1bzNIbm4vMHgiLCJtYWMiOiIzMDBlMGIyNDJmN2ViOGZkYWI1YjEwZDA1YmVhM2VkYzI3OWVkYjQwMmEwOWI0MjM3ZDgxMzA4MjdkMGQ3M2IxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImgrZmxnZDBRYzFxRG1DanY4eGZBNnc9PSIsInZhbHVlIjoiUVNmakNQRHNEeFpUMDNIMi9MUmRseWU1VTRIOFUyWWIzVUdmMnpMbXJaSVNjcStzNXlTSDNnZjRVWWh4MlBUb2E0QVZCU2VpeC9lYzZsNTYzZnJza3RETDlpY2U4aDVQYmdIWVZ6Z3NSUUZsMWVEVXN4Y044YUdJOUJvUHlzdHdiN2hhMjZkb2hROHN2Qm8weXpRYWZXNHNJd0tiYVRleVM2dGNiM3ZEdllaQkNlQml6YWhzbkx5dlBvUXY0Ukk3ZUZlRjRnQ2NOSmgwa3pmSWp3T2xTRGU3TUM3T3JqOUJoQ1BiRWFRaDcyckdDeFFpUFROQ2hETzd0SGcrWlg2UU9KRWJpT0hEUzlJRTc0c1NnZGxFa3pHY2JSdFBGZW1rcVVpWjc3WlN1a1dNVTc3bHlpK3l2SnZmR0FPbWFtUGdmc0dZYkRqSUt5c0JZZVg5ZXltS2FJLzNvVnBHOExDN2lRUnJCTnRPWVM2dU1VVDRoVW83dUk5c3JRa1V1bVZscnptSkQ5U0V1RldEaFdEUmZYQXZ6UGc3UDRQZVpwbUhkUHVKU3o3UXd2RnNjNFR5OEJxdjVqTXhBaFB5R3FiaXErbHNPSTArZmFud0pEbVRWUXhOR2d0YU5zNFV3dHhRVFduK2huWERGeHVrajNubFlobXNORGRRYTg3OUY5MGsiLCJtYWMiOiJiNDFhNGVhMjc0ODFiOWE2MWViYjliOGQyMzdlN2U5NzBhZTIyMDQ1YTgyNDM5ZGVlMTBmZjg3MDRhYTUxMWFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340178112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1755574714 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755574714\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:59:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY1S2hocmZHY0kxbTdXZk1YeFozY2c9PSIsInZhbHVlIjoiZXk0b3BMY0pjbFRHa0RFbDhWS2U0dlc0UHJiWCtaWWUwV1VuQlRVWW5wNWlUdXhFQzBuYWJBZWNVdjZhQzVMbDU0Slk0d0dTRUNWc2lWNDhUL2MzN0VhellPWlFYbzVaRzRjRnREN3JDTVNCT2F1K05zV0phOC9DVmI1d3B3QW1sNzR4NkcydS9sajZwSnE2V1VIaXRrU3RQQ1A0ZjFyNGVUbktLdjZnY0Y2clliY0NEVWFoSU9USHBrTytIaUQzMDdqQklaQzQxenk4V0M4RWd4VGd5VUFZMFQzOVVQRDFuRkhValI5c2RRdUZ0MmwxOE9pVDBUdWo5cWorR1RjSm5VdDBRODJwOUtBTXBHQnpaL2F5dGZWdGNzVEkzbjlMNTM0QUdvYVA5WmZvM1JZNkJoWURDRm50cENZTTVOREZzVjdZc1RWL3E3c3RDb29sK2c3MEg2UkZKWlhPVVhjaytpYmZRWmFVbEQvUU9pSDFkMGxCK2VVb2NiSHI5MHFFVktkZXE0a2prRlZac2UrejA0VlBZMnR0MEY3OExra3ArenhsSXRDN2FEeXBlbGtEMkkvMk1ZQ3lEVkt4ckJuWUJBSVZ3UHY5bms5ZmhkbkVKRTduNmgwSncyRTZUNFA4ZFhiNmRod3VhNlhKQjNPN0ZpallsRmFmYzNMUGUxdHoiLCJtYWMiOiIyYzJiZTNhZTc2YWNjY2YyMjc2MjUzMGJmODllMGIzZTBkY2ZjYmU1ZWFmNGI1NDVmMzgwMWVkMDRiMTg0NzQzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjBGWW9hSVpzaUp4cnpISGpRLzNTWmc9PSIsInZhbHVlIjoiNzFNL0xlc0xlM0M5cDlCUzBuMnU3QXMxWmN0WCt4RUk1dldKSmxMRllVMDI2Z3QwenhOejJFYU1Yc3ViUFI2Njl3SmlnZUpmeGtWdE90SGVXWUtRVHQ1aTdBYmlVT3VMUEJIT1RLaFg4NXlOQU1mSW5xd1dQckFqbldXNUJKWkt3NWJEa2EwMkYvMVorOHA0dGQxTUZQUlVvcjJwdGx2QytEdG92Uys2MXBKbzRJUFpNOUVldjJTSHlLcyt0aWZqejNYWVpiUklENlRFQnFEbjRCRUpqY1NWNUNLN1E5eU5SWDgwN3FBbXlyRW0vQWJCdzAvWVJFaUMrc2s4bmNkNGZZNDYza09IL1FDdDVHRkU1VFhEVThkaTQwVElSK1MvbVRvQmV3T3VLVE1IOTI3WHYzRElXTExyWWlNdGdzdUdoUlBjKzlmQmZ5QUNCT2NRMXd2OWYrUVMrZ3g5eFladzJ2S3VjaUYwZ2xDYk40UU9TblA5TGhPZExJUmVRTHBnSERMN2QyYjhCdEloNUxjcmNpNGxaRVAwYVRuUUZIbjMyMFRYOXpENVRBZ3JwV1IvdmlHQit1Q0J4RTdmVnppMHdKeE93Q3JkYmNacWluUjZSQ3BZcVFpTUhZdEJNSThZckhWL3JtaTlQUmJ6a05UK3lGVVJtVlNaM0NGd2VEaU0iLCJtYWMiOiIwOWY0MjlmZmNlZGI1NjkxMTU4N2VmY2JiYTE3ZjlhODk2OWNhNmFkYmJjZmEwMjc2MTIxNGNhMDQxM2Y1ZDMyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY1S2hocmZHY0kxbTdXZk1YeFozY2c9PSIsInZhbHVlIjoiZXk0b3BMY0pjbFRHa0RFbDhWS2U0dlc0UHJiWCtaWWUwV1VuQlRVWW5wNWlUdXhFQzBuYWJBZWNVdjZhQzVMbDU0Slk0d0dTRUNWc2lWNDhUL2MzN0VhellPWlFYbzVaRzRjRnREN3JDTVNCT2F1K05zV0phOC9DVmI1d3B3QW1sNzR4NkcydS9sajZwSnE2V1VIaXRrU3RQQ1A0ZjFyNGVUbktLdjZnY0Y2clliY0NEVWFoSU9USHBrTytIaUQzMDdqQklaQzQxenk4V0M4RWd4VGd5VUFZMFQzOVVQRDFuRkhValI5c2RRdUZ0MmwxOE9pVDBUdWo5cWorR1RjSm5VdDBRODJwOUtBTXBHQnpaL2F5dGZWdGNzVEkzbjlMNTM0QUdvYVA5WmZvM1JZNkJoWURDRm50cENZTTVOREZzVjdZc1RWL3E3c3RDb29sK2c3MEg2UkZKWlhPVVhjaytpYmZRWmFVbEQvUU9pSDFkMGxCK2VVb2NiSHI5MHFFVktkZXE0a2prRlZac2UrejA0VlBZMnR0MEY3OExra3ArenhsSXRDN2FEeXBlbGtEMkkvMk1ZQ3lEVkt4ckJuWUJBSVZ3UHY5bms5ZmhkbkVKRTduNmgwSncyRTZUNFA4ZFhiNmRod3VhNlhKQjNPN0ZpallsRmFmYzNMUGUxdHoiLCJtYWMiOiIyYzJiZTNhZTc2YWNjY2YyMjc2MjUzMGJmODllMGIzZTBkY2ZjYmU1ZWFmNGI1NDVmMzgwMWVkMDRiMTg0NzQzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjBGWW9hSVpzaUp4cnpISGpRLzNTWmc9PSIsInZhbHVlIjoiNzFNL0xlc0xlM0M5cDlCUzBuMnU3QXMxWmN0WCt4RUk1dldKSmxMRllVMDI2Z3QwenhOejJFYU1Yc3ViUFI2Njl3SmlnZUpmeGtWdE90SGVXWUtRVHQ1aTdBYmlVT3VMUEJIT1RLaFg4NXlOQU1mSW5xd1dQckFqbldXNUJKWkt3NWJEa2EwMkYvMVorOHA0dGQxTUZQUlVvcjJwdGx2QytEdG92Uys2MXBKbzRJUFpNOUVldjJTSHlLcyt0aWZqejNYWVpiUklENlRFQnFEbjRCRUpqY1NWNUNLN1E5eU5SWDgwN3FBbXlyRW0vQWJCdzAvWVJFaUMrc2s4bmNkNGZZNDYza09IL1FDdDVHRkU1VFhEVThkaTQwVElSK1MvbVRvQmV3T3VLVE1IOTI3WHYzRElXTExyWWlNdGdzdUdoUlBjKzlmQmZ5QUNCT2NRMXd2OWYrUVMrZ3g5eFladzJ2S3VjaUYwZ2xDYk40UU9TblA5TGhPZExJUmVRTHBnSERMN2QyYjhCdEloNUxjcmNpNGxaRVAwYVRuUUZIbjMyMFRYOXpENVRBZ3JwV1IvdmlHQit1Q0J4RTdmVnppMHdKeE93Q3JkYmNacWluUjZSQ3BZcVFpTUhZdEJNSThZckhWL3JtaTlQUmJ6a05UK3lGVVJtVlNaM0NGd2VEaU0iLCJtYWMiOiIwOWY0MjlmZmNlZGI1NjkxMTU4N2VmY2JiYTE3ZjlhODk2OWNhNmFkYmJjZmEwMjc2MTIxNGNhMDQxM2Y1ZDMyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1310396915 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310396915\", {\"maxDepth\":0})</script>\n"}}