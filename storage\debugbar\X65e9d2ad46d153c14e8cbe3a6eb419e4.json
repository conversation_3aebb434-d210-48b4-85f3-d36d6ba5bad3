{"__meta": {"id": "X65e9d2ad46d153c14e8cbe3a6eb419e4", "datetime": "2025-07-31 16:23:08", "utime": 1753978988.011671, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978985.735416, "end": 1753978988.011704, "duration": 2.2762880325317383, "duration_str": "2.28s", "measures": [{"label": "Booting", "start": 1753978985.735416, "relative_start": 0, "end": **********.788768, "relative_end": **********.788768, "duration": 2.053352117538452, "duration_str": "2.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.788819, "relative_start": 2.05340313911438, "end": 1753978988.011708, "relative_end": 4.0531158447265625e-06, "duration": 0.22288894653320312, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00943, "accumulated_duration_str": "9.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.940177, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 56.416}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.969577, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 56.416, "width_percent": 15.058}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.980217, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 71.474, "width_percent": 13.574}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.989102, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 85.048, "width_percent": 14.952}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-558082988 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-558082988\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2055320606 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055320606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1694580583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1694580583\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-161210250 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlR0SVpFczdjdHFOTGNqZXhWai9IdEE9PSIsInZhbHVlIjoibWcxTEpNK2tlZCtaVzdhTjNEV1grWnp2d1h6Y3NuY3NOU21BZE1IdHJnZTVYV2tDWXFzL1Y1MkhzeDNNekoxTEp1UytkMWdPMVE0SUF5Q3QwUXM0TFp4SndsSmY3N0YrTDZBQ2hQd1hmc3JFNzd1YTRVOVhQbmlZelN6WGwrd2JGcGNQTks1MldhejZvR1JZOFdaWENhWU9uOVBEdFhYMGZoOHZaWVJ5MDcydlNUSkpnSit6U3VEanRoczBiQ2R1SlJIdnVVRk8yMklpL2ZRTTk2YkN3eW82ZnNyeFF3MjF3RG5DaHIyTHFWMTFGeEtJYW14akJRcGpoa1RQTkhQdllTMlBWTU9HSitHOENweWl4SDQ1Nll3aWZUVzJtRDIvSUtNVldRbW1PbnVPS25KZlF4L3F5b1ROVk9BNmg0NUxIRzdXendPVnpwR1ovRyt5SmlOTmszaURpeTBIOWFFeVdWanN5WjRieTRGRDRmSnlySHhCS1psZ0dMaDZVQ3NkWlpTVHVtbkk0UUlPMkdzS2JMN0J6ZnRiWWp1cWkrczBMU1daUVA1Ni9lSGc1c3Q1OVk5R1d3R25hYVZlUXc4SHUwVDhHVEpBUTlTbEJZVC9wOFVCa0RrQlVPUDhkbzdwQS8wSE5qMnZMR0RpWFY2bUxyVzVGTGc3VUFSaUN0c2QiLCJtYWMiOiJjOGI1MGE2NjVhMzEyM2FjYTU5MTgxODkzNGQ0ODk3OGE4Zjk3MGIwYTNhZDI3MjM5MWQ1ZDRmNWZiMmY4MDU2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik9rSkdWcEJRaGpuOFVMS0dWSTlqUGc9PSIsInZhbHVlIjoiSEFtOWpFdVpoOXpJQ1Z6UElGSHZVTFdCRmtSYWNzV1I0WlJON09mVlZFanlCWlNzVm9vSUlKUGd4ZDRma3RvSmFXUUs5cEUyS2orRXRwdHBuMi8veDV5bmlmOVdzOW1vNlBCcUJQUGdVUmpHUHZuVlc0anU5L05OYkhkQzJRTmtuaWI2MHV6aFhiZVJTSEdlSjFDZWdyamhJV0tMaDZ2dkQ0L3hBcE43ZENpenNBTzlza0NaTXhtQ2tPR3NpSENUV0xlT3RSckcyamVXUkNtWXErSndwWG9jWVRPTlpCUUJPcllxOFB3eG5YNThyVEdjMkdlVGJndUZHeFdUMEVleTVXNkVqVDc5dUhsemd4dGhKRTJoU0JFdlIvQ2lFeDBzZnB4akNnanRoZ293QnhKUjNFemJPRXNZbVF4U0NsU0FrY0ZCMEJaRmlWZmp3TmZvOFBWa2VIbm1qaDl1VVlaZUsrUHB6WWdlakJWTk1JUnU4S2RZS0RpSHVUb2xJUUJsOExOQ1ZvUkNIWFc0cHlwNkhsanVEQStUOVZiMjhQazM2QWxVcSt5TDFzQllyYmNnNitSSmFQT1pwRk82em1GTVYxUFREREl0c0puUlJSdkZ0aERGaUdoelFiSHFweXJNL29OcG9LSFY4Yndwbi9LRFgxMG9FKzRpZzNSdVBqbXoiLCJtYWMiOiI5ZTE4ZWI4N2Q1ZTQzNjdhYWEzNTBhNGRiNTYzYjY5MjI4NDJiNjhhM2I5Y2NjMjhjZGVkNDhjY2Y1MTQ3OTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161210250\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-683340699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683340699\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1596392962 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:23:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii8wRjB5WTFOVmlEZlNOTnJQM1RVR1E9PSIsInZhbHVlIjoibnNEZFdLaUtWMnVFcjZ2REROOERvRVI4czYxcVgzT3JjK1lQelEwNXhKSUdHSEFxOW5lZit5bmtLdmRTWDJVemx5Ly9Vako1NWZnWnl4WlphSFpBWjFDTkxyLzA1UjBSbStYMHR2c20vS3Q0b2pkL0NRZnZyS2Nud2Q1Um9aZ2VEelVDOVZRUjJmS0tBSWFJWmpIb25QV3Z4UFhpN1JreWRVMkFWZU15U2hYeXRsa2tOTHpCWjgxSkxKZURkaDZCcXdhM3M5NWdYSGtvZWNacTB1TS9PdW9iRXNweUdqaGRMOURucG55VWcvb3E2VVFpWTM2TTF4Y09qckw4RUZDMnVYNlQzZ3N3Y3E2dUtXcThuenVWWkRYdjBudTM2SlVlWlA3TFAvQTRyZ0tqbzEwQVhuM1hvS3hWU2x5Ukc5aldHV01zajNyQ2xQR1cyY3kvL3g0MkZ1QXNneHZTeFZhU041aGtQZ0hwUDY4N050NlhLTlRmYVhoWURvOW5JOWRQTE1xNTZOM0w2Y3BzOGhaOUp1ckdUa0trdnR6MHhSWUVkMHRZVzZ2UURSZGpCQUdHZTJtTFJuQjhOMnpWd0RuY0wvQ0RjZTFqaHdCYytHMVg1WGtOVkFaTkNyN2hBS2cwM2FBSFUvQzBydjJmd0N3bS84WnBoYVN5dUhhanZiNkYiLCJtYWMiOiI2Mzg2MjFhMzczM2Q3MzljMGVjMzc1MDY3MDYyOTEzYjkyNjEwNmM1NmQxZWQ2NGYyOTE5Nzg4MWEwYzU0MWU0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik83eDh5QVd6UDhEL1dkSVZHK2w1M2c9PSIsInZhbHVlIjoiNlBmb1o0dzlZM3JEWFIyZ0lQcHlKdGxubnVDSldLaXlnU3hxT21zWnd1MGdNazFDU0VITytVZW5Sc2gvbjJ5eTRaMWlPamNicmR5YURBekFFbnI1Sm1FSnh6K2cvSDZrVTZLM0NURWJxc3NBWms4QlBPVTF0dDNQbkdjQWo1U203SnZiNEVTZERETWd2eU9VbnJ5dm5JZ01kVy9ld3FyclBEWm03cGFEZWtPdDdDNkF6UjlwUmFiRTZpY1FNSFFMeDg3KzArdXlSV09SMDBQbzUySEhLaUlOek1lczhqU3ZtZ2xpSGNXQ1NKa3JEZTdHaThKa2YwRlBFY2dHc1IzbHNnUU1LQXV6OW1Scit1SnZsQ3E2NndRRkdOZllTa0wrVFdXVUhha2ZpbjlNYXNSb25jaG9YQkxNK1pPS0d1NFUvRHJxSEhNbmMyUEtjekF6N3BmSElkSjd2UjV4RWlLZDJQYXJIbmdPdXB2eFFndks4Rjd0K2MxZlFOcjc5UlZvMkpQN0N6QzdGanlLeU1FVm9TdFpnd3lHSTU4a0dTVnkvV0JtMnlmZkVvZzJ4UjY4b0l2Tkora1NKMWMrZ1EvUmppR3djV0NPSXJvQlZVWUMzTXZWaTNORGMrQ1ZGVmZyZUJ4RzF4WW8vWi83UWxKZXhYSTE4dzVMbEZDNHNtQ0wiLCJtYWMiOiIwODhmZWI1YTI3NWJhZWI3MGJjZDA0NWZjMDVjNjllNDgzODkwYTc0YmVjYTExOTMwZmQxNjE0YWU1YzdkODU1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii8wRjB5WTFOVmlEZlNOTnJQM1RVR1E9PSIsInZhbHVlIjoibnNEZFdLaUtWMnVFcjZ2REROOERvRVI4czYxcVgzT3JjK1lQelEwNXhKSUdHSEFxOW5lZit5bmtLdmRTWDJVemx5Ly9Vako1NWZnWnl4WlphSFpBWjFDTkxyLzA1UjBSbStYMHR2c20vS3Q0b2pkL0NRZnZyS2Nud2Q1Um9aZ2VEelVDOVZRUjJmS0tBSWFJWmpIb25QV3Z4UFhpN1JreWRVMkFWZU15U2hYeXRsa2tOTHpCWjgxSkxKZURkaDZCcXdhM3M5NWdYSGtvZWNacTB1TS9PdW9iRXNweUdqaGRMOURucG55VWcvb3E2VVFpWTM2TTF4Y09qckw4RUZDMnVYNlQzZ3N3Y3E2dUtXcThuenVWWkRYdjBudTM2SlVlWlA3TFAvQTRyZ0tqbzEwQVhuM1hvS3hWU2x5Ukc5aldHV01zajNyQ2xQR1cyY3kvL3g0MkZ1QXNneHZTeFZhU041aGtQZ0hwUDY4N050NlhLTlRmYVhoWURvOW5JOWRQTE1xNTZOM0w2Y3BzOGhaOUp1ckdUa0trdnR6MHhSWUVkMHRZVzZ2UURSZGpCQUdHZTJtTFJuQjhOMnpWd0RuY0wvQ0RjZTFqaHdCYytHMVg1WGtOVkFaTkNyN2hBS2cwM2FBSFUvQzBydjJmd0N3bS84WnBoYVN5dUhhanZiNkYiLCJtYWMiOiI2Mzg2MjFhMzczM2Q3MzljMGVjMzc1MDY3MDYyOTEzYjkyNjEwNmM1NmQxZWQ2NGYyOTE5Nzg4MWEwYzU0MWU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik83eDh5QVd6UDhEL1dkSVZHK2w1M2c9PSIsInZhbHVlIjoiNlBmb1o0dzlZM3JEWFIyZ0lQcHlKdGxubnVDSldLaXlnU3hxT21zWnd1MGdNazFDU0VITytVZW5Sc2gvbjJ5eTRaMWlPamNicmR5YURBekFFbnI1Sm1FSnh6K2cvSDZrVTZLM0NURWJxc3NBWms4QlBPVTF0dDNQbkdjQWo1U203SnZiNEVTZERETWd2eU9VbnJ5dm5JZ01kVy9ld3FyclBEWm03cGFEZWtPdDdDNkF6UjlwUmFiRTZpY1FNSFFMeDg3KzArdXlSV09SMDBQbzUySEhLaUlOek1lczhqU3ZtZ2xpSGNXQ1NKa3JEZTdHaThKa2YwRlBFY2dHc1IzbHNnUU1LQXV6OW1Scit1SnZsQ3E2NndRRkdOZllTa0wrVFdXVUhha2ZpbjlNYXNSb25jaG9YQkxNK1pPS0d1NFUvRHJxSEhNbmMyUEtjekF6N3BmSElkSjd2UjV4RWlLZDJQYXJIbmdPdXB2eFFndks4Rjd0K2MxZlFOcjc5UlZvMkpQN0N6QzdGanlLeU1FVm9TdFpnd3lHSTU4a0dTVnkvV0JtMnlmZkVvZzJ4UjY4b0l2Tkora1NKMWMrZ1EvUmppR3djV0NPSXJvQlZVWUMzTXZWaTNORGMrQ1ZGVmZyZUJ4RzF4WW8vWi83UWxKZXhYSTE4dzVMbEZDNHNtQ0wiLCJtYWMiOiIwODhmZWI1YTI3NWJhZWI3MGJjZDA0NWZjMDVjNjllNDgzODkwYTc0YmVjYTExOTMwZmQxNjE0YWU1YzdkODU1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596392962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-228256107 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228256107\", {\"maxDepth\":0})</script>\n"}}