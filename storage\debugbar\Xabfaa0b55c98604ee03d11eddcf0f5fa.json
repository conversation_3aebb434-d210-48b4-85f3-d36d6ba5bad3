{"__meta": {"id": "Xabfaa0b55c98604ee03d11eddcf0f5fa", "datetime": "2025-07-31 16:23:12", "utime": **********.943828, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978991.559449, "end": **********.943865, "duration": 1.384416103363037, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1753978991.559449, "relative_start": 0, "end": **********.727813, "relative_end": **********.727813, "duration": 1.1683640480041504, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.727846, "relative_start": 1.1683969497680664, "end": **********.943868, "relative_end": 2.86102294921875e-06, "duration": 0.21602201461791992, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013616, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026940000000000002, "accumulated_duration_str": "26.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.836541, "duration": 0.023530000000000002, "duration_str": "23.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 87.342}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.89877, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 87.342, "width_percent": 6.347}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2052}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.913456, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2052", "source": "app/Http/Controllers/FinanceController.php:2052", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2052", "ajax": false, "filename": "FinanceController.php", "line": "2052"}, "connection": "radhe_same", "start_percent": 93.69, "width_percent": 6.31}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-1828374999 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1828374999\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-276128726 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-276128726\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1334705952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1334705952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-565698932 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ii8wRjB5WTFOVmlEZlNOTnJQM1RVR1E9PSIsInZhbHVlIjoibnNEZFdLaUtWMnVFcjZ2REROOERvRVI4czYxcVgzT3JjK1lQelEwNXhKSUdHSEFxOW5lZit5bmtLdmRTWDJVemx5Ly9Vako1NWZnWnl4WlphSFpBWjFDTkxyLzA1UjBSbStYMHR2c20vS3Q0b2pkL0NRZnZyS2Nud2Q1Um9aZ2VEelVDOVZRUjJmS0tBSWFJWmpIb25QV3Z4UFhpN1JreWRVMkFWZU15U2hYeXRsa2tOTHpCWjgxSkxKZURkaDZCcXdhM3M5NWdYSGtvZWNacTB1TS9PdW9iRXNweUdqaGRMOURucG55VWcvb3E2VVFpWTM2TTF4Y09qckw4RUZDMnVYNlQzZ3N3Y3E2dUtXcThuenVWWkRYdjBudTM2SlVlWlA3TFAvQTRyZ0tqbzEwQVhuM1hvS3hWU2x5Ukc5aldHV01zajNyQ2xQR1cyY3kvL3g0MkZ1QXNneHZTeFZhU041aGtQZ0hwUDY4N050NlhLTlRmYVhoWURvOW5JOWRQTE1xNTZOM0w2Y3BzOGhaOUp1ckdUa0trdnR6MHhSWUVkMHRZVzZ2UURSZGpCQUdHZTJtTFJuQjhOMnpWd0RuY0wvQ0RjZTFqaHdCYytHMVg1WGtOVkFaTkNyN2hBS2cwM2FBSFUvQzBydjJmd0N3bS84WnBoYVN5dUhhanZiNkYiLCJtYWMiOiI2Mzg2MjFhMzczM2Q3MzljMGVjMzc1MDY3MDYyOTEzYjkyNjEwNmM1NmQxZWQ2NGYyOTE5Nzg4MWEwYzU0MWU0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik83eDh5QVd6UDhEL1dkSVZHK2w1M2c9PSIsInZhbHVlIjoiNlBmb1o0dzlZM3JEWFIyZ0lQcHlKdGxubnVDSldLaXlnU3hxT21zWnd1MGdNazFDU0VITytVZW5Sc2gvbjJ5eTRaMWlPamNicmR5YURBekFFbnI1Sm1FSnh6K2cvSDZrVTZLM0NURWJxc3NBWms4QlBPVTF0dDNQbkdjQWo1U203SnZiNEVTZERETWd2eU9VbnJ5dm5JZ01kVy9ld3FyclBEWm03cGFEZWtPdDdDNkF6UjlwUmFiRTZpY1FNSFFMeDg3KzArdXlSV09SMDBQbzUySEhLaUlOek1lczhqU3ZtZ2xpSGNXQ1NKa3JEZTdHaThKa2YwRlBFY2dHc1IzbHNnUU1LQXV6OW1Scit1SnZsQ3E2NndRRkdOZllTa0wrVFdXVUhha2ZpbjlNYXNSb25jaG9YQkxNK1pPS0d1NFUvRHJxSEhNbmMyUEtjekF6N3BmSElkSjd2UjV4RWlLZDJQYXJIbmdPdXB2eFFndks4Rjd0K2MxZlFOcjc5UlZvMkpQN0N6QzdGanlLeU1FVm9TdFpnd3lHSTU4a0dTVnkvV0JtMnlmZkVvZzJ4UjY4b0l2Tkora1NKMWMrZ1EvUmppR3djV0NPSXJvQlZVWUMzTXZWaTNORGMrQ1ZGVmZyZUJ4RzF4WW8vWi83UWxKZXhYSTE4dzVMbEZDNHNtQ0wiLCJtYWMiOiIwODhmZWI1YTI3NWJhZWI3MGJjZDA0NWZjMDVjNjllNDgzODkwYTc0YmVjYTExOTMwZmQxNjE0YWU1YzdkODU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565698932\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-657404572 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657404572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1054311846 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:23:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktuOUp0OHNZUFk2QXBOMXRlZGUyOUE9PSIsInZhbHVlIjoiSzV0azJaWisrUnRjQUUxL3p1OUdXK1l0WW8vRnVMU2FUYmxyd1BOYzU1ZU5ucHRNZmVQRGJMZmR0SVNEaDZ5VW9vUHNFaklxNkxab0k3Y3BVRUwyL1hHdDZsaVpLUlVrRGRmYThlYWZXVXdlSE1wSjFOOFhuRWQzWWVncXRTNDY1U1BGUE5KYzQ0T3JkUHQxM3plRFhzZFhmb1VxcnFPUWxBclQ2dUVzM1YyOUprcnZaQ0w5RFJDYXBmRmJuRkNVRHpoTWJUN2RqQmlmTmwyZTR4OHVmRGQ5N1hGK0d0emhTZFVHaVhYbVBYUkl6akVGejFLTG5QZFhPTXJJQ3pMYStIMzJicE5Wa2E0d0lEd3lhNHBVT2czcUpqbXU2ck1IOGJqKzdHdVZPYURORXc4M043dURIRTlvb0VSNERvREJMRWhNU0hrOGwweTBqNU5lbFgzenlxVjQvMitxdktZNXRBSVZKekpyMjMxcXdETnlOaTU0YkFteTJNcjFRMmdxelB2WG9ZUFpUY2VjcENrUzZMd3VpY0FKcDhGNHlpT24rb3FoaHlKSlZRKzRQWXdSZituTUI4L2phRE5DdjlXSTdEbXJnQ2kzckh5NzZlV3dNMy9nalp0TVZwVDVQTFVKVU9MVENJaExPWVU1dDYvOGlYQUJGZWEyQ2JkT3VlZ2ciLCJtYWMiOiI3NjM0ZDlhODQ0NWQ0MDcwODVmMzIwMDM5NDBmNjUxYjk2Zjk5YjdkNDljNWE3NzZmODkwNjg5MjA0ZDdmNDA1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkF5aG9XcFBhMmlnTUIrejczNDFnNUE9PSIsInZhbHVlIjoiSk1zZWlDU0hGNGtDRHZhZ0FzbzdMSThKOCsvakRycDd3TGpvb0g3TmozQ0FkeitUNU1MV3NvbFNWdUdqdDJWQ3VIYzlKZTFmbTFhdG82d2JYK24rMWhucCtRd1R0Mmhzd1pxaVRpUzMzRXdpbE1JSm81UzVIWkFVWTJqWk1BYVF4OW9WLzVWMkNoMDliSk84NllraGltWHBXUUJRLzRmSkEvNlovUzZHVWIzQVo3TE9XZzZJQ0FwNngxMmJUQ1g5QVV3SzFPODUzb0drQWdIckY3bUJFcG13dnZ2R3BNSFpMVzRoS2J0cFA5aGhTYTJEQ1FsRFcrTmNZNlJkWDRtVm9GWVI5elRraEg0cGFyMmd4RHFDY3hLbGNmclhBTWxiL3dyUU5KYjF2MFVqWEVVQUpnZVQ5UEVlVlZrTW12c0RGMWdSTWRSZWRhMFBhQURzU3o2WEVqbDkvbzMwL0lZRWtvbjc2dnBtMlpXVDZsVXdxS3A3bDB4R0hkRS9uZzNzalBpSGVEUklEdXZUVE1nVDluWks1YnJiZkY2OHQxdEFZS3pxMzlSNFU2Ykkzd2ErZjhPcS80WnVzcXRYbW9HN0R1NGhaazRxWmQyUWdLcUFkMGRQeCtaeFNFWmorZVNnL0J5UVVIbm9kRExHbWJ2WDRyUEJsS0JRNVBPaGxCeXciLCJtYWMiOiI3NmM5ZDQ5MTdhOGE3N2Y4ZTllNGM5MTIxYmY0NjAxM2IxNzYyODRmODNlZGM3ODRhM2E3NGY3ZWZjMjk1ZmE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:23:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktuOUp0OHNZUFk2QXBOMXRlZGUyOUE9PSIsInZhbHVlIjoiSzV0azJaWisrUnRjQUUxL3p1OUdXK1l0WW8vRnVMU2FUYmxyd1BOYzU1ZU5ucHRNZmVQRGJMZmR0SVNEaDZ5VW9vUHNFaklxNkxab0k3Y3BVRUwyL1hHdDZsaVpLUlVrRGRmYThlYWZXVXdlSE1wSjFOOFhuRWQzWWVncXRTNDY1U1BGUE5KYzQ0T3JkUHQxM3plRFhzZFhmb1VxcnFPUWxBclQ2dUVzM1YyOUprcnZaQ0w5RFJDYXBmRmJuRkNVRHpoTWJUN2RqQmlmTmwyZTR4OHVmRGQ5N1hGK0d0emhTZFVHaVhYbVBYUkl6akVGejFLTG5QZFhPTXJJQ3pMYStIMzJicE5Wa2E0d0lEd3lhNHBVT2czcUpqbXU2ck1IOGJqKzdHdVZPYURORXc4M043dURIRTlvb0VSNERvREJMRWhNU0hrOGwweTBqNU5lbFgzenlxVjQvMitxdktZNXRBSVZKekpyMjMxcXdETnlOaTU0YkFteTJNcjFRMmdxelB2WG9ZUFpUY2VjcENrUzZMd3VpY0FKcDhGNHlpT24rb3FoaHlKSlZRKzRQWXdSZituTUI4L2phRE5DdjlXSTdEbXJnQ2kzckh5NzZlV3dNMy9nalp0TVZwVDVQTFVKVU9MVENJaExPWVU1dDYvOGlYQUJGZWEyQ2JkT3VlZ2ciLCJtYWMiOiI3NjM0ZDlhODQ0NWQ0MDcwODVmMzIwMDM5NDBmNjUxYjk2Zjk5YjdkNDljNWE3NzZmODkwNjg5MjA0ZDdmNDA1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkF5aG9XcFBhMmlnTUIrejczNDFnNUE9PSIsInZhbHVlIjoiSk1zZWlDU0hGNGtDRHZhZ0FzbzdMSThKOCsvakRycDd3TGpvb0g3TmozQ0FkeitUNU1MV3NvbFNWdUdqdDJWQ3VIYzlKZTFmbTFhdG82d2JYK24rMWhucCtRd1R0Mmhzd1pxaVRpUzMzRXdpbE1JSm81UzVIWkFVWTJqWk1BYVF4OW9WLzVWMkNoMDliSk84NllraGltWHBXUUJRLzRmSkEvNlovUzZHVWIzQVo3TE9XZzZJQ0FwNngxMmJUQ1g5QVV3SzFPODUzb0drQWdIckY3bUJFcG13dnZ2R3BNSFpMVzRoS2J0cFA5aGhTYTJEQ1FsRFcrTmNZNlJkWDRtVm9GWVI5elRraEg0cGFyMmd4RHFDY3hLbGNmclhBTWxiL3dyUU5KYjF2MFVqWEVVQUpnZVQ5UEVlVlZrTW12c0RGMWdSTWRSZWRhMFBhQURzU3o2WEVqbDkvbzMwL0lZRWtvbjc2dnBtMlpXVDZsVXdxS3A3bDB4R0hkRS9uZzNzalBpSGVEUklEdXZUVE1nVDluWks1YnJiZkY2OHQxdEFZS3pxMzlSNFU2Ykkzd2ErZjhPcS80WnVzcXRYbW9HN0R1NGhaazRxWmQyUWdLcUFkMGRQeCtaeFNFWmorZVNnL0J5UVVIbm9kRExHbWJ2WDRyUEJsS0JRNVBPaGxCeXciLCJtYWMiOiI3NmM5ZDQ5MTdhOGE3N2Y4ZTllNGM5MTIxYmY0NjAxM2IxNzYyODRmODNlZGM3ODRhM2E3NGY3ZWZjMjk1ZmE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:23:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054311846\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1950134915 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950134915\", {\"maxDepth\":0})</script>\n"}}