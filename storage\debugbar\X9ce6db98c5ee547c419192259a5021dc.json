{"__meta": {"id": "X9ce6db98c5ee547c419192259a5021dc", "datetime": "2025-07-31 16:01:50", "utime": **********.218515, "method": "GET", "uri": "/finance/sales/contacts/customer/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977708.342283, "end": **********.218592, "duration": 1.8763089179992676, "duration_str": "1.88s", "measures": [{"label": "Booting", "start": 1753977708.342283, "relative_start": 0, "end": 1753977709.90652, "relative_end": 1753977709.90652, "duration": 1.5642368793487549, "duration_str": "1.56s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753977709.906558, "relative_start": 1.5642750263214111, "end": **********.2186, "relative_end": 8.106231689453125e-06, "duration": 0.3120419979095459, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013176, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0127, "accumulated_duration_str": "12.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.095654, "duration": 0.00908, "duration_str": "9.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 71.496}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.154103, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 71.496, "width_percent": 14.724}, {"sql": "select * from `customers` where `id` = '4' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["4", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.172028, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 86.22, "width_percent": 13.78}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/4", "status_code": "<pre class=sf-dump id=sf-dump-569628990 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-569628990\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1262756595 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1262756595\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-870851360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-870851360\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1840327778 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZSZk8rcVpXMFRleEZjSEpOOGpBZ2c9PSIsInZhbHVlIjoiYVhtMVpGS0xpSXA4OHBEWkwrYWpTSCt6YThaSi9SSnRHVzNxa2t4UU9KQ1ZPZDhDVVdoc0gzcFN2Z1ROMHJiTGdaaHNxUFdEV2VqamFVM1ZLMWgrakYrUUhNdit3bWlHcUcrYy9WdGJZd0RZak5vOGVuOFN0TjBXLzJHUTNyRTRKcXlxNG52VzNxblg2T2hLVEttT2RubHBtdnZLdXFWYWVNc2xMb1VvWVM0Z0hmRm02UFE4bGZRb3Z0L3d2OTVvS29NdUtpVUg4c28wREtyQ05NZXR3ZTlmN3JIeWZJVkZZMldJakh4TXJsSXdVWXBubytnazhYczlpVWYrVDFFcExVMFRRb3MrY1ZXU1YvUGNKSDlPams5SkJ1VnNRalIzbHk2dWdPQk82OFlXMXNLNG5Ca0Q5USsvczNaL2hvSmNRdjcrMU1LdmdZM1MwRnJyMEYvejlCTzNTdWo3eWlKb01OdmN4QVE2bkQwY240Tll1bDFOdkxEVmVaWXNuSEIvcWw5WTRiaGF5S0pYdWlrK0w5VVBwRGlSYU1OcFpLVXJ2R04zWFlwMVk0UnUyYklQRk1xa1E3T1pSMnpjc1J0SkdzYTE5VUxaa2VzNE5XZXpZUVFKcm8vUll5NUtUMEoxVkw0eDcvc0VnY0xyTWdqNEtjdldSUE1oSG41ekt1TGEiLCJtYWMiOiJjYTZiZmZmODgxY2FmNjQwNzU0NGZjODQxMThhM2ViOGNlOWMxMGJhMTk0MGNjYmNhMjI2YjAwYTRlY2U4ZjVhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjZhVTh1T1dyQ2hFYkE1NXBDS2xKUVE9PSIsInZhbHVlIjoiYTR2dklSZ3prc0pYUkV6SHU1WmpXY3lMMmdENjRiS1poQXJmVmgwOWI3eS95MVBPU1F2VklhUkU2d0loQ0ZJSW9RbTZsd240RnpjM0p0akZydEpqUE8wVk9uaG1BYjdqNFNOQVFCUXNLSklnMjJFTFJRelVGZ3NpWlJSUWlsQlRBNTVDYXQxakVpblBIWEpkcmw4Y2g2anNoekQrcnlRUDdMaEtxTlJsUENjMmtJbDJNbEhtNGdoelhoL1h6TXN4QkdsSDhKcXFZalV0NFJOd1JLc1ZSdTZ4bnlNTzVnQVVFY1dPMy90RG9SRDVNYzIzV0Jub2E2VWVLMERPNytPaHA2TVhNVlRyUnNna1B1aEFMVEF2MTNtcmt4aklZSmRULzNhMFJYMm9ETndtRzhLWExxcVpqMk1NS1RPMHdNdThFK3JDNTdIbE5zMGx6ZExjM1BHRFkrSHN5bFlJc0lnWm1YeVFNTmMycyt5dnBwQkQ5a2lETkRmT2llaUtocERtTzU5RmljYnhMeTZib2NRQkhEelBKbktncmxOZGJESEJsNlRvck13bTA3WkZjbWVQTUJ5T0dyZ3lieU5jd1RHRWcvWUUrWklCZlB1NHh6SG5DWVRKVzBndE90RDZkbTBlZ2ErR3B1YkVablJFTlpOMEd3RFZEdzFNMmlvMWJuYTUiLCJtYWMiOiI4OTRiZjY1NjMwMWQ4NTNjNTAxZjc4ZTU1NjYyYjMxNzhlOTRhMmFhOWZlMDc0ZGU0M2M1ZmRhNDY4Y2QwZmIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840327778\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1177369552 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:01:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtkN2pvclBNcEFTZ1N3bTlFRDVybUE9PSIsInZhbHVlIjoiL2dCOU9kaWxsVE1yL1NNZzhCcjYvUjFYY3d0M0Z6U3hOV21RT05OMnpqZjJhMzhNNElMSlNxSTh3VlNoMXV3NXNrN0hsN2xUSEFvb09Ia0Rza2ZUS1pZZ1ozMEd6cGRNQnk0c0ZST0JZbWx5SWgwNEFNdFVscGRBRzdmT2ptdmlxY2ZtQmhpdnhFdW5sNFg0cm1LUlg1Sk5zKzMyWTRJaTQvSExCaU12Rys3UmFJZXlsVzNiNldBVlZETTlHanlJdjFGQ0d4QmVOS01KcnJqdnVyMWNoSWkvWGkxVjJwUUFRN1krQ05aZ0VEN0pjOVlVL1ljQWd1NnFUR0FBRmx1VFIxZitKTnZuTXduME1PTTB4ZGhGNUx1VkxmblkxYnRLb1B2alVqa0lnM2F6Tm9BNjVhNjJmK2xOL0hjRytNWk5LeVN0OXhrdGtUb21TOXE4WDVLeWRvMThFd3J5R2NiVUYzU0lJQnltUGM2V3hMQzBpMXB2elRsVDJ6MmFNaTRIUUsrNC9jWVdYb1MweEZuSnZjSGhYcnArNkgwQ2JrUE04QThDNUlmRkV4V0haNm4yVTllNEg2NWJzZjBUbVhnYWloQ2V6cTZTbDhLVnFIU0VTRldqeUR6aDY0ckgwSEJLN0JKd2d1SnBiaFI5TVNzajlRODJGdTlwZGlGQVkvWVEiLCJtYWMiOiJlZGI5Nzk4OTA1NWFlM2ZiZWU3Nzg1Yzg1MDY0ODFhZTY4M2RjZDI3M2M5OTZlMDNiYzQ3NjBjMmM1ZWU2MWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:01:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpDTWsyaFdLSGdzdXJ6endaYVR5bUE9PSIsInZhbHVlIjoiNVE1U1Z1OGFiRnNMZkNiT1NIS1ZqeVFaN1k4bEtGSEFRZVB0N1M5dTBNL3kzWEF0cEE1ZjB4Wmh4SlVOTi9NbkJnbkFaUFdlQWRQbm5EQWFMb2VsTk5uVjNSMEgxVkgxL2VXa0M2cldiZmxEZE1lUFh2Q0ZUdmZwQWRwbWErcmxRbm0rempLa2krb01TY09vQ3JRanJycG1DeUVHNDVONUpWbVdZQTJxRURBY0Y2cUd2UldNdzBEeEV0QkZOSWJiSThLTERPdEtMNmtlUkw5Lzl3S2R5YjRXbjRjQXRJK1FqZG9vN2o3VnRRWE1YM2RwakFwSDYvYXBJMmxzK3JZTzI2NHhMOUs1QSsvZDhWSnZMME43bzNGL2hLM3ZaOVlVZ2tHL3dVR0FBbW9meUhvbUNLTVdKRjdqU1hCMXAzYUExeGxHWFhFZ1FUNGVuQzRPMmFtWjZGb0tQRzBtdnQwaWJOcHI0cFkyMW8xbTNObCsweE1PRzBkSWNMditleEZSZW5GM1o4azQ0eXI5a2JCUXpveWRXV0FFbk4zTXAzb0hNN0RLYThPc0hEREkxUzdabk4rejRLSCt4c3BiR1k1bGlHeGZvdnMra2doNTBIRVB1OWtxdXZTaXpoZ0hYbUpVU3ZwS3pMeFhMb3lsaWdxbjQwS1J2YTBISTAxQnFHcGUiLCJtYWMiOiJmN2VjYTAxMDkzN2Q2Yzk2YzJmZjRiOGRlNjY3NGRlZjMxMjBkNDgwOTBiZDAyNmVhMGM4YjI3Y2UyOTE5YzJkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:01:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtkN2pvclBNcEFTZ1N3bTlFRDVybUE9PSIsInZhbHVlIjoiL2dCOU9kaWxsVE1yL1NNZzhCcjYvUjFYY3d0M0Z6U3hOV21RT05OMnpqZjJhMzhNNElMSlNxSTh3VlNoMXV3NXNrN0hsN2xUSEFvb09Ia0Rza2ZUS1pZZ1ozMEd6cGRNQnk0c0ZST0JZbWx5SWgwNEFNdFVscGRBRzdmT2ptdmlxY2ZtQmhpdnhFdW5sNFg0cm1LUlg1Sk5zKzMyWTRJaTQvSExCaU12Rys3UmFJZXlsVzNiNldBVlZETTlHanlJdjFGQ0d4QmVOS01KcnJqdnVyMWNoSWkvWGkxVjJwUUFRN1krQ05aZ0VEN0pjOVlVL1ljQWd1NnFUR0FBRmx1VFIxZitKTnZuTXduME1PTTB4ZGhGNUx1VkxmblkxYnRLb1B2alVqa0lnM2F6Tm9BNjVhNjJmK2xOL0hjRytNWk5LeVN0OXhrdGtUb21TOXE4WDVLeWRvMThFd3J5R2NiVUYzU0lJQnltUGM2V3hMQzBpMXB2elRsVDJ6MmFNaTRIUUsrNC9jWVdYb1MweEZuSnZjSGhYcnArNkgwQ2JrUE04QThDNUlmRkV4V0haNm4yVTllNEg2NWJzZjBUbVhnYWloQ2V6cTZTbDhLVnFIU0VTRldqeUR6aDY0ckgwSEJLN0JKd2d1SnBiaFI5TVNzajlRODJGdTlwZGlGQVkvWVEiLCJtYWMiOiJlZGI5Nzk4OTA1NWFlM2ZiZWU3Nzg1Yzg1MDY0ODFhZTY4M2RjZDI3M2M5OTZlMDNiYzQ3NjBjMmM1ZWU2MWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:01:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpDTWsyaFdLSGdzdXJ6endaYVR5bUE9PSIsInZhbHVlIjoiNVE1U1Z1OGFiRnNMZkNiT1NIS1ZqeVFaN1k4bEtGSEFRZVB0N1M5dTBNL3kzWEF0cEE1ZjB4Wmh4SlVOTi9NbkJnbkFaUFdlQWRQbm5EQWFMb2VsTk5uVjNSMEgxVkgxL2VXa0M2cldiZmxEZE1lUFh2Q0ZUdmZwQWRwbWErcmxRbm0rempLa2krb01TY09vQ3JRanJycG1DeUVHNDVONUpWbVdZQTJxRURBY0Y2cUd2UldNdzBEeEV0QkZOSWJiSThLTERPdEtMNmtlUkw5Lzl3S2R5YjRXbjRjQXRJK1FqZG9vN2o3VnRRWE1YM2RwakFwSDYvYXBJMmxzK3JZTzI2NHhMOUs1QSsvZDhWSnZMME43bzNGL2hLM3ZaOVlVZ2tHL3dVR0FBbW9meUhvbUNLTVdKRjdqU1hCMXAzYUExeGxHWFhFZ1FUNGVuQzRPMmFtWjZGb0tQRzBtdnQwaWJOcHI0cFkyMW8xbTNObCsweE1PRzBkSWNMditleEZSZW5GM1o4azQ0eXI5a2JCUXpveWRXV0FFbk4zTXAzb0hNN0RLYThPc0hEREkxUzdabk4rejRLSCt4c3BiR1k1bGlHeGZvdnMra2doNTBIRVB1OWtxdXZTaXpoZ0hYbUpVU3ZwS3pMeFhMb3lsaWdxbjQwS1J2YTBISTAxQnFHcGUiLCJtYWMiOiJmN2VjYTAxMDkzN2Q2Yzk2YzJmZjRiOGRlNjY3NGRlZjMxMjBkNDgwOTBiZDAyNmVhMGM4YjI3Y2UyOTE5YzJkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:01:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177369552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2048272517 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048272517\", {\"maxDepth\":0})</script>\n"}}