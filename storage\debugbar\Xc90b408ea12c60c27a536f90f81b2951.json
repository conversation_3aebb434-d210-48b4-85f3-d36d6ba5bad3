{"__meta": {"id": "Xc90b408ea12c60c27a536f90f81b2951", "datetime": "2025-07-31 16:26:17", "utime": **********.840803, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979174.770717, "end": **********.840843, "duration": 3.0701260566711426, "duration_str": "3.07s", "measures": [{"label": "Booting", "start": 1753979174.770717, "relative_start": 0, "end": **********.606377, "relative_end": **********.606377, "duration": 2.835659980773926, "duration_str": "2.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.606404, "relative_start": 2.****************, "end": **********.840847, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1Yl7L0yQe4oTyvvmLX8vSyWL2r0ZnYsfIBmn8UZV", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1945033723 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1945033723\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-94344443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-94344443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-571121425 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-571121425\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2080653092 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080653092\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-968470058 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-968470058\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1816026253 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:26:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNINnY3L0RnK21BT2JYRVFTZDdocEE9PSIsInZhbHVlIjoiYlZCMnRacWUzMHJER0o1bXlBN1JPbHFBWTE3RTU4VHJLUjZINjdNeGdnQW8wcGNGNHFRZUdnVlROY3NKeFA4WXdlYnkvWHJKck84MDRzWDl0WlVCZmszVjdGemVNOWhwais3VHpMWFMzTUJYeURIdFA3WTdycUVTc01jTnRFRnVFakFlK3NUUThzQTNOOWdjeHlrNXBPSEpwdEZuc1lUTUdrbFRJZkgxZHhGSGY1ODgrZzdVaXB6N091ZTd5dUpZM09OdFpTYlhBczk3L0F6L2NwSXU3UzhOVmdiT1dvdkhiQ3hUaC92Wi9NUWlnV0Mwbk94S2toNWYvbEJNYnZrRTdlcDlha3U5NXcxN1pOOWZlTDdNQVdmSkI0amZFajRUWUtUU1VNUElVQ290azN1b0RVUkpJcGV2UzdmY1lNL29kdEdYL0xzVnJJVUNQWlE4ZzVOMVliZjhmR0FFR0Nyc2x4NGxUMmRDUGk3Z0JqSWpiaWhTWEpwTlhNU00vaW14empGeXROV1FrLzEvOElySGRZTGlUUk5Dd3F2UjYzUW1saDg3V3MrNHVVbFJUMmk4bUE3WDBrL0VneHhhZGNaRXhOVXE1M1k1cFRYOFBsNm02bUt0bjE3anBpV1E3a1dvK0p0dnVkblFTcDk2L3ZiRjJUUEM2bnlNVzJnQzN5S1oiLCJtYWMiOiI1ODU4OWVlMDNhZTU4NGNiM2I3OTBiYjAzNzBjMDg3NzQ1MTM1Njk0NjAxYTY0MWUxNDg0Zjc4NjI5M2YwNDEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNVOE5xUVhlcGNmMWRGTVV2UjhSblE9PSIsInZhbHVlIjoiY3lyUFNvWVZnNkJqallUMGRGdGhzYlk0STl3U1BEcGExMDBNQmNIUmpiWXgyYWRiWkpLTzUzVjBRZW9kYnBuNlliZXRrTHNkcDA3SEd5VWhjbnJUUW9RUXdIODF5cVZreHZTWG5XWFBQSTJuNit2bTl6cjBQYjByV3FEeTlVTUxYeVY3dFpvd3l3NmkzREo3UWhVbjhwNlpMZEpxZkd2Vy9leFR5ZTQ1REZyRUhsb2lGbThEY2Y4MU12UnhrcHBGTHpUSkI3L250aEpIMUhYbHhaWlhPOUxSdmZwdmMwU1FtS1RUMDJSZkFweXMvRG5xN3Vzc0xvcFB6ejgvWjNITjQ3T3laMXZtbEk0TnFveWFhOG9QQ3JwVkJsNnBPSWwxV09RMGdSQWh1UzhhWCs4Ry9kb3V3MTlsV1p0dlJQbWFVSnBmQ0V6RTh3dGQ4VmpFZHRmMjZ2VjBMcjBjblgzSW5DanVDL1dSdFNPcWtidnQ2NUN1dkRkYU16N1JjTFhKNk5iTDA0bjk0cE9EQmJPbzcwR2MrQUIxYXRoT0pERS94cHdDQnBPSGdxWWpwSVgzN1dPbGkrMmRLbVB1cC90ZEYwZ1c2eWl4dXFYZXk5eHNqZ2F1VnljZXBmRzVNRnlvazdZdXIzaGZJT2FIVkRuS2FxYk4rWW1mdVM2anZmZkMiLCJtYWMiOiIzNTc3MTBjNzNmZjE3MDY4MzJiOTkyZDgzMzdjYTExZDQxZWIwMTk5ZDA3ZTY3NmI4NTk2OGQ0YjJlNzllZWExIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:26:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNINnY3L0RnK21BT2JYRVFTZDdocEE9PSIsInZhbHVlIjoiYlZCMnRacWUzMHJER0o1bXlBN1JPbHFBWTE3RTU4VHJLUjZINjdNeGdnQW8wcGNGNHFRZUdnVlROY3NKeFA4WXdlYnkvWHJKck84MDRzWDl0WlVCZmszVjdGemVNOWhwais3VHpMWFMzTUJYeURIdFA3WTdycUVTc01jTnRFRnVFakFlK3NUUThzQTNOOWdjeHlrNXBPSEpwdEZuc1lUTUdrbFRJZkgxZHhGSGY1ODgrZzdVaXB6N091ZTd5dUpZM09OdFpTYlhBczk3L0F6L2NwSXU3UzhOVmdiT1dvdkhiQ3hUaC92Wi9NUWlnV0Mwbk94S2toNWYvbEJNYnZrRTdlcDlha3U5NXcxN1pOOWZlTDdNQVdmSkI0amZFajRUWUtUU1VNUElVQ290azN1b0RVUkpJcGV2UzdmY1lNL29kdEdYL0xzVnJJVUNQWlE4ZzVOMVliZjhmR0FFR0Nyc2x4NGxUMmRDUGk3Z0JqSWpiaWhTWEpwTlhNU00vaW14empGeXROV1FrLzEvOElySGRZTGlUUk5Dd3F2UjYzUW1saDg3V3MrNHVVbFJUMmk4bUE3WDBrL0VneHhhZGNaRXhOVXE1M1k1cFRYOFBsNm02bUt0bjE3anBpV1E3a1dvK0p0dnVkblFTcDk2L3ZiRjJUUEM2bnlNVzJnQzN5S1oiLCJtYWMiOiI1ODU4OWVlMDNhZTU4NGNiM2I3OTBiYjAzNzBjMDg3NzQ1MTM1Njk0NjAxYTY0MWUxNDg0Zjc4NjI5M2YwNDEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNVOE5xUVhlcGNmMWRGTVV2UjhSblE9PSIsInZhbHVlIjoiY3lyUFNvWVZnNkJqallUMGRGdGhzYlk0STl3U1BEcGExMDBNQmNIUmpiWXgyYWRiWkpLTzUzVjBRZW9kYnBuNlliZXRrTHNkcDA3SEd5VWhjbnJUUW9RUXdIODF5cVZreHZTWG5XWFBQSTJuNit2bTl6cjBQYjByV3FEeTlVTUxYeVY3dFpvd3l3NmkzREo3UWhVbjhwNlpMZEpxZkd2Vy9leFR5ZTQ1REZyRUhsb2lGbThEY2Y4MU12UnhrcHBGTHpUSkI3L250aEpIMUhYbHhaWlhPOUxSdmZwdmMwU1FtS1RUMDJSZkFweXMvRG5xN3Vzc0xvcFB6ejgvWjNITjQ3T3laMXZtbEk0TnFveWFhOG9QQ3JwVkJsNnBPSWwxV09RMGdSQWh1UzhhWCs4Ry9kb3V3MTlsV1p0dlJQbWFVSnBmQ0V6RTh3dGQ4VmpFZHRmMjZ2VjBMcjBjblgzSW5DanVDL1dSdFNPcWtidnQ2NUN1dkRkYU16N1JjTFhKNk5iTDA0bjk0cE9EQmJPbzcwR2MrQUIxYXRoT0pERS94cHdDQnBPSGdxWWpwSVgzN1dPbGkrMmRLbVB1cC90ZEYwZ1c2eWl4dXFYZXk5eHNqZ2F1VnljZXBmRzVNRnlvazdZdXIzaGZJT2FIVkRuS2FxYk4rWW1mdVM2anZmZkMiLCJtYWMiOiIzNTc3MTBjNzNmZjE3MDY4MzJiOTkyZDgzMzdjYTExZDQxZWIwMTk5ZDA3ZTY3NmI4NTk2OGQ0YjJlNzllZWExIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:26:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816026253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1918337533 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1Yl7L0yQe4oTyvvmLX8vSyWL2r0ZnYsfIBmn8UZV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918337533\", {\"maxDepth\":0})</script>\n"}}