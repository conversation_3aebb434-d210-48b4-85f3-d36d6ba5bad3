{"__meta": {"id": "X2524c97989233c2ab762acba767697c1", "datetime": "2025-07-31 16:22:22", "utime": **********.175399, "method": "GET", "uri": "/finance/sales/contacts/customer/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978939.685913, "end": **********.175459, "duration": 2.4895458221435547, "duration_str": "2.49s", "measures": [{"label": "Booting", "start": 1753978939.685913, "relative_start": 0, "end": **********.810997, "relative_end": **********.810997, "duration": 2.1250839233398438, "duration_str": "2.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811029, "relative_start": 2.1251158714294434, "end": **********.175515, "relative_end": 5.602836608886719e-05, "duration": 0.3644859790802002, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02567, "accumulated_duration_str": "25.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9941869, "duration": 0.02044, "duration_str": "20.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 79.626}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.083712, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 79.626, "width_percent": 10.947}, {"sql": "select * from `customers` where `id` = '4' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["4", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.112047, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 90.573, "width_percent": 9.427}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/4", "status_code": "<pre class=sf-dump id=sf-dump-825371916 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-825371916\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-797606829 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-797606829\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-128662712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-128662712\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-61399624 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlWRjZ1Q1c1WUdUUkFjWGVpUks4YUE9PSIsInZhbHVlIjoiUm8wN2c1YVhCSXNRZE94MHVQS05id2NtQ0xyREFKRFdKbTI2WFNjdEhmamU2TThjZWROY3ZnTldnbGM0dzlRei9sMTBFL2U2MUorMmRwa1lsUHZKa2ZkaGlzcFFvRFhUeTJwWEtqK0JIMm5rM2ZwUlBYT2VzUTFTV3lGVVJxTll1TTBFMDc2VGdzMzhGWE0zazBuWFAveEx5d0pUakRtZTZ6NTR3UmVKMHl5Q0d2a1VDdHNUdFlmOEdLQkh5U3I4TmtJeHhWMmdONERvRXpoWnVvd3B1ZjUwV3ZKL0hpTkdQa1JFdldPYzFwQ2lybUduWlVhSG9wSy9EamZ2SGVmRU1sbG1jaW9uZ0RlbG5jQ2pNRnAxUktDb2E0RDVjaG54UXU2UHc5T0dMeTQvamZ5OTV1TmY2a1I1N3kyZWgvQ1FHNlpKRGdZSThzeG5wejdaMytEU0R0ZkJjQlpiMWo4QlpoQmZDNTVLTERMaGVjMy96ZFNlUnovc2xTM1ZFUTBsVjBkM2lzenh4ODUzbDYzY1lLZ0wwY3E5MlFZZWJGb3JhNDRaTVdlQ0tpa0hjWkVtTWRPSm0zTWNBOW5hUXM2TnNIV0poL1hYNlNLRTlkUXloYVF1OG51aGdvNUlsVnljVXNLN1pnMHRBQVp2Q1BrWHNROWVZUnF5TUJ2SWtlZnYiLCJtYWMiOiIxODBlMTQ3ZDYxMzU4ZmM3ZDQyYTQ3ODc2N2ZlMDUyZjYyNDMwNDlkOTFjMTBhYzdkM2EzZGYxYTU3YmU5ZGY1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik5DS0RvTkNxZUcva1Q0U09tUmkxRnc9PSIsInZhbHVlIjoiUmgrQ1ExQnRFT0MrMVd6enJaR1hBMy9rNTNUenk0eUltNmtCTThwc1FiUldMSlcxK3BwTWtwVlZDMlhVNTltektCOVJYOWRmN2VwRENKMmVGSXorVjBGWTRCYnpHZXNSY0N6WDlBZytCUDFQUkI2UzFBRXFwRUlYNzdLV2kweEFJdC8rYXExMjhaNXphWE5UbTBobDlqVTlaT1pNOTRrNW04TmlDVTRhNzdISXlJM0NTUENVRkM4Rm1LQzVHaDhjUDBGWmF0YXdySmU2TU9OalFQcHhaZDc1b0QxdW1wREN0anJObFhYcm45alZkU3k5KzdpNGlGWHRkTjd5WS9TU3RIb3NTVWZtMWh6b2RPbmQzNDJQOG05Ymh0YlBsOGJ6QWRsTG51T0sxY2pwOGQvNXVOSWlGOFFzN0pKOTJFUTlxSlZFckFwMDVEMUlXd1NUNEFTT25ka2tFeTA2SXMwdkpqcTJLRDYxeWMzMUxQRFUvaytneEVxTzdIRm5xbmpmRFZ5djJ1UEtmWElmNCthVzIrTXkvOFdPY3ZsQUx4Yy9IckQwYUlqTzVGZTNTK2Q4UVVCMDdVUzN3VE1sWWY2YnN3U1FMWC9pY1FqZkVWYk1jZ24xbGhqVkNkYU5tZVhQSEo1V1pOalZLQS9TdUEvZVNJUW9IQ21aU3Vielh6SVYiLCJtYWMiOiJmNmJkYWZhNzI3ZDUxNzFhZWMyMDJiNTAzNGVhOWYwNmUzZWMxMzAwMWI4OTViOTNhMDljMjExZjY5OTRlMzRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61399624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767662587 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767662587\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673050247 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:22:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN3S0JNWHJOR0JvZ0lYU3dXLzZ0cEE9PSIsInZhbHVlIjoiNm4wVFNtcnZVcmFqNmw1TjYvWUlYWFh4dlQzOHJwODYwRHA3by9tSElZM1VwTDNrYmxsUFdPS01tbW5wc0tiTUVQcGRDMlp4WXY3bmlOQ1RzUHRaZnVVQW85SjYxQ0RYRDE2aUtub0ZabHc3VklzSWVHWXY1R0dCVlhsaGw4WjYyOGliUHFnSnRlK2k3bHBsUit4aDZ6MmoxR1p1aEJvOWF0eWw3WERKdC8ydEdvdW9UTnhROVVZUXdaN2Y2UXg1MzNOck5lckhSdFV0aldpckp0NW9VSnJxcjBlQUlVVElWV1gxS0cxdWVvRXRvL0pZdFNZaE4wQnQ4TEhuUGNNcnNtMEwwLzdMbmJGVGEvczBkblV6Y2R1cENRbEtZL09kdytCMlAxaUQ5R0taYTBoMmJabkNwWllWVXJEK25BK3B1ZnFYakdTMjVQRTk1dHdHLzNkSGlGdnl5dVBIcnY2KzJoWHYxdm8yaWlvTWF2NFJ3ZXNFLzc2OFROMUI0NnZ2bUtsYUdrdkRlZzFSaXBQbFlZMkNCTUJqeUVseklqN05UZmI0TTVnczdOOThQNXRJSll6ZmlmakNUNXFlVkdKbjlhVFFvWGl5MXBpSVluOWV0VWFleG9KMm9MM2QyTTNYOE41ZUkvV08xdGZFT3JvaU1DUnFyLy9idGc1Qk1nVzIiLCJtYWMiOiI3NWMzYzk3MjE5MTVhMjU1MWE1Y2I2MDMxNTVkNDVhZjQ4Njc4NWRhYmNiMjcyOGNiZWExYmMzMTQ0ZGFkOTcyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:22:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InEwMEc1R01qQS82MUxZdk9jMFhRNXc9PSIsInZhbHVlIjoiYUMvbXZLMnU3N0ZsZEVQTElUSHZsUDNoVFhoQ2ZXNW5KQzdldlZJQjJFOHkxeEN2dkZ6ODN4N1BpcTlsb3ZNbUo2WERnNkMyNVNBY2NYdEt0WjNxZUlXVmlwbnZCekdQOThrTmtYZTZzOFBqWDBVRExFV05JWElKckRHTnpvNk9sTDd3UkgwQ2dlVTRQMmNDc2tHR0FrbHNjS2d5WHVxN1gyMTR2dSswSzIrUGt4L3c1Tmg1TFRYdHhQMzR2Q2FVWjlHOFNCc0d5dGNUcGdlYkNPQWlLZnBmNGJWSUV0dnROanQvVFptdWNLMmcrWXdTQXgrOGNDNVZINElNWWZpTFJtVGJwL0hlQlJiNmlSdlJSUFFCRStsdEsvMm5mOVJnWkVnck9PRHd0OFhqZk1jc2hWa1R5T01McDVsemk2TFc0dGVmYUpwbVdBcDVxQ2lybHN2SE1zVG1FOHY4TXpMQUxJM0dsMUtyWFRlbGNiK2ptOVdKdDA3TW8wWE1UZWxLalg3YkNMN3Z6bU41MHRqSEpSTEFnYWdLNXcrK0E3K0k2WGtWbjJORkNqc3pXcjBGRFROcDdWMjF6TUh1TnMvNWJ3ZmdqczY5TnhCWjgyaUZMVjJ0TlV3U0FlZUlUYXNYdUY2aWtLbGx1S3h1M29ReDVvb3oyekhQdmhxNEQwQm8iLCJtYWMiOiI4YTUzYWZiZDRhNjYwNzQ0ZTkxODJjMWE4NzZmYTExMzY2NTZjNDljYmZiOTIxNDljMDc5MjRiMDgwN2ZlMWQyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:22:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN3S0JNWHJOR0JvZ0lYU3dXLzZ0cEE9PSIsInZhbHVlIjoiNm4wVFNtcnZVcmFqNmw1TjYvWUlYWFh4dlQzOHJwODYwRHA3by9tSElZM1VwTDNrYmxsUFdPS01tbW5wc0tiTUVQcGRDMlp4WXY3bmlOQ1RzUHRaZnVVQW85SjYxQ0RYRDE2aUtub0ZabHc3VklzSWVHWXY1R0dCVlhsaGw4WjYyOGliUHFnSnRlK2k3bHBsUit4aDZ6MmoxR1p1aEJvOWF0eWw3WERKdC8ydEdvdW9UTnhROVVZUXdaN2Y2UXg1MzNOck5lckhSdFV0aldpckp0NW9VSnJxcjBlQUlVVElWV1gxS0cxdWVvRXRvL0pZdFNZaE4wQnQ4TEhuUGNNcnNtMEwwLzdMbmJGVGEvczBkblV6Y2R1cENRbEtZL09kdytCMlAxaUQ5R0taYTBoMmJabkNwWllWVXJEK25BK3B1ZnFYakdTMjVQRTk1dHdHLzNkSGlGdnl5dVBIcnY2KzJoWHYxdm8yaWlvTWF2NFJ3ZXNFLzc2OFROMUI0NnZ2bUtsYUdrdkRlZzFSaXBQbFlZMkNCTUJqeUVseklqN05UZmI0TTVnczdOOThQNXRJSll6ZmlmakNUNXFlVkdKbjlhVFFvWGl5MXBpSVluOWV0VWFleG9KMm9MM2QyTTNYOE41ZUkvV08xdGZFT3JvaU1DUnFyLy9idGc1Qk1nVzIiLCJtYWMiOiI3NWMzYzk3MjE5MTVhMjU1MWE1Y2I2MDMxNTVkNDVhZjQ4Njc4NWRhYmNiMjcyOGNiZWExYmMzMTQ0ZGFkOTcyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:22:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InEwMEc1R01qQS82MUxZdk9jMFhRNXc9PSIsInZhbHVlIjoiYUMvbXZLMnU3N0ZsZEVQTElUSHZsUDNoVFhoQ2ZXNW5KQzdldlZJQjJFOHkxeEN2dkZ6ODN4N1BpcTlsb3ZNbUo2WERnNkMyNVNBY2NYdEt0WjNxZUlXVmlwbnZCekdQOThrTmtYZTZzOFBqWDBVRExFV05JWElKckRHTnpvNk9sTDd3UkgwQ2dlVTRQMmNDc2tHR0FrbHNjS2d5WHVxN1gyMTR2dSswSzIrUGt4L3c1Tmg1TFRYdHhQMzR2Q2FVWjlHOFNCc0d5dGNUcGdlYkNPQWlLZnBmNGJWSUV0dnROanQvVFptdWNLMmcrWXdTQXgrOGNDNVZINElNWWZpTFJtVGJwL0hlQlJiNmlSdlJSUFFCRStsdEsvMm5mOVJnWkVnck9PRHd0OFhqZk1jc2hWa1R5T01McDVsemk2TFc0dGVmYUpwbVdBcDVxQ2lybHN2SE1zVG1FOHY4TXpMQUxJM0dsMUtyWFRlbGNiK2ptOVdKdDA3TW8wWE1UZWxLalg3YkNMN3Z6bU41MHRqSEpSTEFnYWdLNXcrK0E3K0k2WGtWbjJORkNqc3pXcjBGRFROcDdWMjF6TUh1TnMvNWJ3ZmdqczY5TnhCWjgyaUZMVjJ0TlV3U0FlZUlUYXNYdUY2aWtLbGx1S3h1M29ReDVvb3oyekhQdmhxNEQwQm8iLCJtYWMiOiI4YTUzYWZiZDRhNjYwNzQ0ZTkxODJjMWE4NzZmYTExMzY2NTZjNDljYmZiOTIxNDljMDc5MjRiMDgwN2ZlMWQyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:22:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673050247\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-829001788 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829001788\", {\"maxDepth\":0})</script>\n"}}