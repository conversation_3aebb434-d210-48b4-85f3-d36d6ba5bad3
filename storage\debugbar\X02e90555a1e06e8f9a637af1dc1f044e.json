{"__meta": {"id": "X02e90555a1e06e8f9a637af1dc1f044e", "datetime": "2025-07-31 15:53:45", "utime": **********.275067, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977223.84143, "end": **********.275094, "duration": 1.433664083480835, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753977223.84143, "relative_start": 0, "end": **********.087994, "relative_end": **********.087994, "duration": 1.2465641498565674, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.088013, "relative_start": 1.2465829849243164, "end": **********.275097, "relative_end": 2.86102294921875e-06, "duration": 0.18708395957946777, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47416096, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009260000000000001, "accumulated_duration_str": "9.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.201165, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 60.691}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.233459, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 60.691, "width_percent": 12.311}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.241633, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 73.002, "width_percent": 26.998}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1016126406 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1016126406\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-147128355 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-147128355\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-359378327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-359378327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1771696774 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitTSVNLd2tHOVhqem5FUUNiakZvRnc9PSIsInZhbHVlIjoiNzVYaEFLR0xlSjgrM3lJdXc5SXgvaElOQ2FHYXphVXJWV2w1bDMxa0N0b3BQZjE2NWdvSTZoOCtmM2xvTEpVOVl6bjBvQVpneU40djZ0Zks1NTROYk41bkdLdGtoZmd2UnJ2cHVHYno5RE5YRnJ6b2RGRDYvVllpZWQyZ3NHcE5ramNaTW56ZFZNR21TV285OUtqeGQvckhBTlNLbDRSQ0xPV1l1OWlrRm5FOVFJamFhenRocFFBVWxmVEdjTjBLSGJTWDdDN05QU2E2NGszU21oQVh0eE1nTEFkUjdwd3R0VDUwb1orbnB5cnNzRzVYNXBuUWpqY1ArejROWFVzaDNPTWF6ajFoWG4zcEpUdFU1OEx1cjJxVE41eXdWcXAxZENhUE4zWThtSW5maU85NldpeW1UZEZXSjYySVcwZCtWT2hxdDVWWGQyWHJJNGpmKzJMTlJqRHZ2eUJQaDFIL2dNZkhRNWQwKytMTEZjYzV3Zld0MnA0dkpJblRaUGxDczdyUzEvUnJ0ckFOTk1qemRaMEZhSlBMRktyK09QQVo0VnViSEt0WW1WRlhqbjBRWVlHV0JRVGY1VnVtZnJtVnd3Z0pDcklCTmtoY1BnL0FyUk4zSWZnanpwSm5yZ09LeDNIaVNRazJwMmNXbkd5SVl5N1RIVDl5N2h2MXFoSUciLCJtYWMiOiJlZGViZTMzOTEwZmQ4YjkzYTJmNDMzYmRlY2EwMmQ4NzkwYWQwNjlmMThjZTk5MGIzMDQ3Mzk4MGY3NmY0MTExIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1KTG0xc1pQOTBBZ0QrdHdxNHIzanc9PSIsInZhbHVlIjoia1NGU0lYaXk4NzUzVVM1K1BMcEJOc0I3QWVqK1UzWlJXMzU3djIzTDZzczRhd2tXNVZHa1VKVTRvd3FvQmFBVjVISXhrb2hVakdySUZGaVFrQVlSTzZCbGt2TnBlZ0lpc0w3aUJVTG51aUdzK1g0dUt2L1RkZk5MbmhEa1oyVEN6MDR2dEFqSnpJcUZ0dkpwNTZYZVdZakpyTldlY1ZjVVpxWlJMbXhxNCtvMzhZb1dQWnErZVhRdFpPOEc2SUtlL3BHMWxYYlErWnp0WjlWL0VTK21MK29SMnJLZHY0TWl3bmo5SjJrUlp0cGdjMXZJOEt0Z1hJMHNKK0lKbzBKMzlsUXJaQTRjK1NzRjRhWlFHWGJsTEFhejFrK1VySVNyMVlyS0lxczZRV3o0djRFcEdjSG5HUzBYR09IOUVLendtRDRyaDM5OXVKWmNjQitvSnVUekhNWlVrNkUzUzNrYUQxR3c1Y0k5SXY4N2Z4OGRyVHJhamFzWkJEcXFDTmhkWXJVT0tvMGRxQXhGdE45Y216S0pheC9MUm04WUY2aGh1VzcwWDlocXhxcWZLUWJmMmxHci9jYjR6REdzT2h5NzVJM0M4RkpBcENNYTltQlVTS2Z1NnZLSGc0Z3BFWmllOHEzZlRlUTNPN2tnSEVseExsaGhEUHJLZ1pMbERuNzEiLCJtYWMiOiI0ZTFhZGY5ODdiMTNhZGU1NGZlMzI2ZjdkMTE0ZjZmNjYwZDdhODdkOTY2YWQ5NGY1ODM3ZjA0ZDk5MzEzZWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771696774\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1250644212 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250644212\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-736254438 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:53:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJMbWVoWDdaTVUzU0hFYXNZdTBCZUE9PSIsInZhbHVlIjoiM2xhYmc5RjcwMmp2NjFVbFpJVlVMNEdZL1dCM1ZTOXZZeUsrM1BpNGY1SE5wL2pmajdKU2s5MjBQMjk2WVY3aVhJRzdpZTJXbld4T25SdUhrQm1kVTVEalhBSTdIV1pKWkxmWDlPNWV6OC8wNXlIVFNuOE1TemRxLzNJYXJxajJjNC9FR1B0UHNLSlhmRkRSVVBqTi9maHl4Ty85WGNkcTdCWWN5VG9WejZGL3B1RGpyd2dSS3VLaTBvejlyZlZXbXkyYlVUWXZMczNnMmlKcmhYejB6TjBXVTlJQWpaOFlNNkRmK3FrY1NIbXg2dWVRZk1VR2x2a0EraUJNb2hJajlaUkdDaytDZE5jTThGWEJ0TU12bExickN3L1V2VjhxT3VXVUhHMDhhdXJuTXZwY1RIODdLWkMxWE4rTmJMb0dCcUJ4M0NwK1BoN0E1blk1bUE4TE52Y3RuZmJBMXpkMzlGN016V05Uc0Y2ZkFhaWZtTVRQUm1uVVVXclE5aG1wVGlpalBmdlJ0NW1ldlI4YVlyeEh5M2RwcVo0aEtYWm8xaWxoelh2THNLZFVLblhkZVJPVjhMbFNKdlBxN3FGSkhqS2p3QW82ZTluMmo3ZHlXdzNkNWdQUVlHRkNaMWYyUUdyMmk5QXphdGJVdWtleDJWQnhoc1AzK0FkZWdaVjIiLCJtYWMiOiIxODA2N2VkZjg0ODY1ZTg0YWM5ZjYxOThhMThkMDhlYjJmOTY1YjdmYmE1YzlhZDVhNzE4ZGMyZjYzOTA4OTMxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InN5NzN5WFl4Rk5lUWJmZkI5U0lzTEE9PSIsInZhbHVlIjoiMkUvK0tBbGJCU2VKOVlGYTNCREZ6b2VwOVZkM24zSFpjZXhtaktOSURNMTZEeW9QcDNIVHpDNUtmR1JPZnZUQzRydVE1Q3NhbDFuMDljY2QvRUtFbG9FWFd5MWp6SW9sRmFNZGc4bmk1VHh4c2RGWlBhSUVOUCs4ZVE3TzJFZkJIWXAwTmZidWRDaVVRUmxHQ2RwOVdkMmc0UkZ4ekM5WlBFd1JoUlRzeHIzSEd3Tkw2cEdCeVFpOVdBTEpLSWdTQWtwd0c2ZmRrb2w0aGg5NnhHYjNhUDZZT2tudHFFUHFOZllJdkV6V1ZsaEg1bDQ1cEJVYTRDdGFoajJqa3htVlM4b2VXZTExd1lzVU9wZHlnUGhRQ3dJMVk5QU0ySi9EYzFpc2hER0ZHT2NJN2M0M3lOUnd5MlprSnlOaU9KL3ZhWkJESXBFbjRvTXZpMU1rSnJVSVJqdFJPUGY2aUJsK3pJOGJoWGxlcWhsWmhaVDN5RDVxOXM0YWkxd21lOE44clBIdUtWaWNtK0ZhTS9zT1BMM1I5WlFrNFVqYnp5d1NLdEVTejRCWC9tSDNyUkdnVmdxWnFtV2c0bnVnOEJFdWRqSFFYV2lvbVp5K0VBUjBTUWR6YTlYbjVqcWFCdzVoRUtyY0UyZHdVaUxhdkFTRTRPMnM2b0VlYXRTNzF5MmwiLCJtYWMiOiI1NDVjZTI1ZGRhN2Y2ZTg3ZmUwNDc3Y2UwNmI4MzAyN2YzMjA0Y2E3ZWZmZmRhMTNlNzgyM2JmYjcyNGQzMWE3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJMbWVoWDdaTVUzU0hFYXNZdTBCZUE9PSIsInZhbHVlIjoiM2xhYmc5RjcwMmp2NjFVbFpJVlVMNEdZL1dCM1ZTOXZZeUsrM1BpNGY1SE5wL2pmajdKU2s5MjBQMjk2WVY3aVhJRzdpZTJXbld4T25SdUhrQm1kVTVEalhBSTdIV1pKWkxmWDlPNWV6OC8wNXlIVFNuOE1TemRxLzNJYXJxajJjNC9FR1B0UHNLSlhmRkRSVVBqTi9maHl4Ty85WGNkcTdCWWN5VG9WejZGL3B1RGpyd2dSS3VLaTBvejlyZlZXbXkyYlVUWXZMczNnMmlKcmhYejB6TjBXVTlJQWpaOFlNNkRmK3FrY1NIbXg2dWVRZk1VR2x2a0EraUJNb2hJajlaUkdDaytDZE5jTThGWEJ0TU12bExickN3L1V2VjhxT3VXVUhHMDhhdXJuTXZwY1RIODdLWkMxWE4rTmJMb0dCcUJ4M0NwK1BoN0E1blk1bUE4TE52Y3RuZmJBMXpkMzlGN016V05Uc0Y2ZkFhaWZtTVRQUm1uVVVXclE5aG1wVGlpalBmdlJ0NW1ldlI4YVlyeEh5M2RwcVo0aEtYWm8xaWxoelh2THNLZFVLblhkZVJPVjhMbFNKdlBxN3FGSkhqS2p3QW82ZTluMmo3ZHlXdzNkNWdQUVlHRkNaMWYyUUdyMmk5QXphdGJVdWtleDJWQnhoc1AzK0FkZWdaVjIiLCJtYWMiOiIxODA2N2VkZjg0ODY1ZTg0YWM5ZjYxOThhMThkMDhlYjJmOTY1YjdmYmE1YzlhZDVhNzE4ZGMyZjYzOTA4OTMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InN5NzN5WFl4Rk5lUWJmZkI5U0lzTEE9PSIsInZhbHVlIjoiMkUvK0tBbGJCU2VKOVlGYTNCREZ6b2VwOVZkM24zSFpjZXhtaktOSURNMTZEeW9QcDNIVHpDNUtmR1JPZnZUQzRydVE1Q3NhbDFuMDljY2QvRUtFbG9FWFd5MWp6SW9sRmFNZGc4bmk1VHh4c2RGWlBhSUVOUCs4ZVE3TzJFZkJIWXAwTmZidWRDaVVRUmxHQ2RwOVdkMmc0UkZ4ekM5WlBFd1JoUlRzeHIzSEd3Tkw2cEdCeVFpOVdBTEpLSWdTQWtwd0c2ZmRrb2w0aGg5NnhHYjNhUDZZT2tudHFFUHFOZllJdkV6V1ZsaEg1bDQ1cEJVYTRDdGFoajJqa3htVlM4b2VXZTExd1lzVU9wZHlnUGhRQ3dJMVk5QU0ySi9EYzFpc2hER0ZHT2NJN2M0M3lOUnd5MlprSnlOaU9KL3ZhWkJESXBFbjRvTXZpMU1rSnJVSVJqdFJPUGY2aUJsK3pJOGJoWGxlcWhsWmhaVDN5RDVxOXM0YWkxd21lOE44clBIdUtWaWNtK0ZhTS9zT1BMM1I5WlFrNFVqYnp5d1NLdEVTejRCWC9tSDNyUkdnVmdxWnFtV2c0bnVnOEJFdWRqSFFYV2lvbVp5K0VBUjBTUWR6YTlYbjVqcWFCdzVoRUtyY0UyZHdVaUxhdkFTRTRPMnM2b0VlYXRTNzF5MmwiLCJtYWMiOiI1NDVjZTI1ZGRhN2Y2ZTg3ZmUwNDc3Y2UwNmI4MzAyN2YzMjA0Y2E3ZWZmZmRhMTNlNzgyM2JmYjcyNGQzMWE3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736254438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2060769945 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060769945\", {\"maxDepth\":0})</script>\n"}}