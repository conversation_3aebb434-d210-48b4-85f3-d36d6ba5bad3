{"__meta": {"id": "X67203b9041814c6c3b050b8a77e9b202", "datetime": "2025-07-31 16:21:21", "utime": **********.403703, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978878.423717, "end": **********.403759, "duration": 2.980041980743408, "duration_str": "2.98s", "measures": [{"label": "Booting", "start": 1753978878.423717, "relative_start": 0, "end": **********.153158, "relative_end": **********.153158, "duration": 2.729440927505493, "duration_str": "2.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.153209, "relative_start": 2.***************, "end": **********.403764, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "251ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CadYKNDUs4Q9DjRBr16C5VSaPHFiFhk1AIusU6or", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-90086145 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-90086145\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-693070720 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-693070720\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1358020402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1358020402\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-858768299 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858768299\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-822396552 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-822396552\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:21:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNVWEgreE1iM3J1bnZYUjlJeGlyekE9PSIsInZhbHVlIjoiaTZENE55OVZiSzhMMnRmQjlqREZHWHlxMkM5eVByU1JxMFZvQVhSVzZ2ZVZsaU9yWDlRNUkzanZ6OTBKQ09kMk5zSnJDQ2VRYmlTVElMTHUzMHlva2c4RjFkTGN1UWRHaVNCTTVrem8rTG9zMk5EbDI2NGYxQ3lnT0pPZXk0dGl4SU8rOVJlTDNENWI0c0VjZitiSTR2WDRSbzJsZzVtbE9odDlWOWdjMG5oMjRIZ2NiTi9CVEVGb1pFY29LdU1aMy93cUNzdS9LdHAycnJEMHEzN3ZsSVJoOFhUSHNGVWcrS3NjN0FaS3I1K0JSY2YwSnhISmdQdmhkTkE0NkVrYUYwditTSzIydXp6T1JUbGFTa3Axb3J2b3pCOVdNVitUdmYxdEwxUzI3Mk1IcUFsTmFhMnFwdnV1QlVneEM3OEVJenJkeVlwSUQ3SUJHeExSeVJCK2V1Sy9xZ2NOT21VQzR5QmExWDh5Z1lqRUdwY0l1Q2VZQVUvSUpkU3NBNGc5eFVlSTZwOWFCV0kzVkNIRVR3RkY3dXpOUUlyNUVkRWN0cFVramdLRzlRYm9IcE5wYmpiR1c5YXVWMWp0TUgxTlY5cllzNUFWN2ZxRVlzeUF2V1loTHJmdjg0Qlo2S2JpeUtIVTVXb0FPSlM1STVKNUl5cmpYSnduNXBKUjZXbisiLCJtYWMiOiI4MmJkN2Y2NTM4MjBjMzgzYjZmZTNiYzM3YTU1ZDZmMWNjZmVjM2VjY2ExYzNlZTA0MjM3OWFmYTYzNjk0ZTc4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVYWjg4djNYYW04bkMybVJVQnRGTnc9PSIsInZhbHVlIjoidVJoQzZqSzhZcmZMNDI0YUhSdzZ0UkRGWDdCaUdtSUViaE5hK0hwakpna2VEcXhOaEwyK2xYNFFlQXpXNjZndnBKbnBROHNwL2xhUTJHcDdKa251Tm0xbnJXRUpxY2NBWXNwRFRCQTNJR3RIZU54TWNidVRVaHRzVnh6eFJONkVZL2tPNXhuMy9ialdNdHRKZmhVcWM1ZTREUFJoUWt0RXI3eVF5NFZRT2J0VVhJcFNaMVhiRnE0aWp3dVkzWjNQK0JOZ0NpSVQwVFBYRWhIT3VTNWd6OWhuekt3QnRibTlHSDZCamE2Y3BFUllhZkxVTHRlYjA2Y25IQWFGWlVaLzQvZkJRME03Um4yNWppUUxrN2NhSklYcXRheGd1bFFUY2NaSGJrR3hVdDFabEhpNW9wMXZURFAxZGFPK3E5NXJlUWsxZjVlV1VIU2NDMndnb0ViTXdHZWxTY3lXcFhFV0NjKzdGdmYrYUxGTW1KYnVoUmdTMmRJZUFnamdJRDFjbGlZUXJzSC9aeWEvMUNQeDZBczlHVW9DWnNOV0Q3ekhzUHNjWVBxdXc0cHN1RHB6WnBaU3FoUVpETWxwNXdhdGY1Z0xFZkVIUUo3OUtNZUFORXdGbjZ5dDNkN2k1QlNTc3ArUjRNcXlMUXArWHA0U01zdElDL1RpRmFGSjRIVk8iLCJtYWMiOiI5Mjg0YzNkZThhMjYwMDQ4MjRlNzUzMzk0NTA1YmQ1Y2NhYmNkYTMxYmNhNWNmMTY5NDU1MGY5YTRlYmRkMDEzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNVWEgreE1iM3J1bnZYUjlJeGlyekE9PSIsInZhbHVlIjoiaTZENE55OVZiSzhMMnRmQjlqREZHWHlxMkM5eVByU1JxMFZvQVhSVzZ2ZVZsaU9yWDlRNUkzanZ6OTBKQ09kMk5zSnJDQ2VRYmlTVElMTHUzMHlva2c4RjFkTGN1UWRHaVNCTTVrem8rTG9zMk5EbDI2NGYxQ3lnT0pPZXk0dGl4SU8rOVJlTDNENWI0c0VjZitiSTR2WDRSbzJsZzVtbE9odDlWOWdjMG5oMjRIZ2NiTi9CVEVGb1pFY29LdU1aMy93cUNzdS9LdHAycnJEMHEzN3ZsSVJoOFhUSHNGVWcrS3NjN0FaS3I1K0JSY2YwSnhISmdQdmhkTkE0NkVrYUYwditTSzIydXp6T1JUbGFTa3Axb3J2b3pCOVdNVitUdmYxdEwxUzI3Mk1IcUFsTmFhMnFwdnV1QlVneEM3OEVJenJkeVlwSUQ3SUJHeExSeVJCK2V1Sy9xZ2NOT21VQzR5QmExWDh5Z1lqRUdwY0l1Q2VZQVUvSUpkU3NBNGc5eFVlSTZwOWFCV0kzVkNIRVR3RkY3dXpOUUlyNUVkRWN0cFVramdLRzlRYm9IcE5wYmpiR1c5YXVWMWp0TUgxTlY5cllzNUFWN2ZxRVlzeUF2V1loTHJmdjg0Qlo2S2JpeUtIVTVXb0FPSlM1STVKNUl5cmpYSnduNXBKUjZXbisiLCJtYWMiOiI4MmJkN2Y2NTM4MjBjMzgzYjZmZTNiYzM3YTU1ZDZmMWNjZmVjM2VjY2ExYzNlZTA0MjM3OWFmYTYzNjk0ZTc4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVYWjg4djNYYW04bkMybVJVQnRGTnc9PSIsInZhbHVlIjoidVJoQzZqSzhZcmZMNDI0YUhSdzZ0UkRGWDdCaUdtSUViaE5hK0hwakpna2VEcXhOaEwyK2xYNFFlQXpXNjZndnBKbnBROHNwL2xhUTJHcDdKa251Tm0xbnJXRUpxY2NBWXNwRFRCQTNJR3RIZU54TWNidVRVaHRzVnh6eFJONkVZL2tPNXhuMy9ialdNdHRKZmhVcWM1ZTREUFJoUWt0RXI3eVF5NFZRT2J0VVhJcFNaMVhiRnE0aWp3dVkzWjNQK0JOZ0NpSVQwVFBYRWhIT3VTNWd6OWhuekt3QnRibTlHSDZCamE2Y3BFUllhZkxVTHRlYjA2Y25IQWFGWlVaLzQvZkJRME03Um4yNWppUUxrN2NhSklYcXRheGd1bFFUY2NaSGJrR3hVdDFabEhpNW9wMXZURFAxZGFPK3E5NXJlUWsxZjVlV1VIU2NDMndnb0ViTXdHZWxTY3lXcFhFV0NjKzdGdmYrYUxGTW1KYnVoUmdTMmRJZUFnamdJRDFjbGlZUXJzSC9aeWEvMUNQeDZBczlHVW9DWnNOV0Q3ekhzUHNjWVBxdXc0cHN1RHB6WnBaU3FoUVpETWxwNXdhdGY1Z0xFZkVIUUo3OUtNZUFORXdGbjZ5dDNkN2k1QlNTc3ArUjRNcXlMUXArWHA0U01zdElDL1RpRmFGSjRIVk8iLCJtYWMiOiI5Mjg0YzNkZThhMjYwMDQ4MjRlNzUzMzk0NTA1YmQ1Y2NhYmNkYTMxYmNhNWNmMTY5NDU1MGY5YTRlYmRkMDEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CadYKNDUs4Q9DjRBr16C5VSaPHFiFhk1AIusU6or</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}