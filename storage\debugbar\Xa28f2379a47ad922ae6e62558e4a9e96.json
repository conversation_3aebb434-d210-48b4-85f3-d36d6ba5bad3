{"__meta": {"id": "Xa28f2379a47ad922ae6e62558e4a9e96", "datetime": "2025-07-31 16:13:09", "utime": **********.60188, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978387.953915, "end": **********.601936, "duration": 1.6480209827423096, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1753978387.953915, "relative_start": 0, "end": **********.443199, "relative_end": **********.443199, "duration": 1.489283800125122, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.443221, "relative_start": 1.****************, "end": **********.601942, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "waWGspPQkt52A9L0XQfJhxrltDc4t7Gl3qHy74cA", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2075354720 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2075354720\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1298526032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298526032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-465991571 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-465991571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1755528275 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755528275\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-533426831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-533426831\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2134141276 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:13:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdLdTNHbVpyVCtTYUtKZm5TSUgvdVE9PSIsInZhbHVlIjoiZ3JQNnV0SnZKSkh0QnhMWFJVRU1pSk5qRktqTHRJcUN4VkVGaUt4QjJZNnRvUGRTU3N2ZmNxUGV1QmVsS3BLRnR0ekY5Uy8rNEhPUU5YTG50dTdWWXhJWDlwbXduVUgyeWdsY2lCeUk1WTFhVmNyNi9NRjRhUzM2TEdBRitHOFBkNFVPV0h3V1RkTDY1YTY0ZGdueExMNkdsNXZpR1lWOTZRS1gvcytWT1d4NzhYaVpRME82TjI0UWVHMmFyWDczOVhKMXFUeW0rVG50YTduaEtJamFCLzlmcHcvamhLT01wNy9LcUJOTlFrTGRkc3JPQS8rWUpvZWduL1A4ZTlBOGhGd2xHS211SzJuZVF3dGFEaUw1amRUSHFZQ1ZsTjBSQ0tsbEhUVU1IVWJsL3FlY0N2Zi82bXBTQjZ1MTdVbTJ1b2dIZVZSVk1qWjlJVmVZaDBaT2lCN0taaW5ZUklrMi9mbHRoNlJHcnNQODNKZlgvM1JQb01FQlBuWDJHVi81VGhFZ3Jyc1FXeWwvaElzU2RZZ2pNcTAvZzZJdVNLMFpWSGQyMmNLaEo0UzV2VHU0K2NLaHJMOTlJdDF0amprM3BIdUNpQ3drL2xwa01zakxGUjd6cy9VT213M2dRK1QwU1ZjWkJ0Kzd2dVdITTN0c243RU15UEFjZUY5S1F3ajUiLCJtYWMiOiJhNTFhZWM0ZDU3YWYwYTc1NzIzYzQwZjNhOGNmNzM5Zjk4MDhhMmU1OGUxYWY3OWZiOTI3ZTc5ZDJkZjA1YjcxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:13:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhreGRDbVB1NS8yUmxVM25RQXJ1UEE9PSIsInZhbHVlIjoiWksyckJMblZVaDRJMjFtU1FybkF3ZUNqWEZpd1A3Q3JtcmR5Mkw4ZERJZDVLbEZBT3JiMnpKbnJoNDdWLzdHNStZN1IvNnd1LzdiMmhWdlIwdlpCRU41dmNLM3N2a2pmOGFvSGJGbHdhWXhxQTZQQjc2eHFFcFRCbzZKdlJSNks2eUNUSlMvOTg0cWdLWHdvSTFYV1U0cWdoMWFPQ0JWaTJHMmtCRHpXeCtKRDJVemZrSWsyeURJbkdZMlJUQjhpMG81SjA2cmQ5UnBjMFBkUDVoamYydXA1RHhBNU1hOTVhbGNza3JyOGZsaWpoQU9vU3hJU2tWN1g1Z2lvM3NFOEtyVVJBQTl4K0xRZjFkRVNFaHk4Rm82anhNSVRUZFRGdGxyWE9ZYnI2TVZrNEZRVVp3KzA0K3hLL01HOTFGNVVqRjNyMFlvSUNjTmdPK1AxaytmQVRraHcwakRBYUJCY1Nlcmw0Y0F2endaVG5TTW1XZys4c3p3a1gyaHlNYXk3YUFBcE1wZFNzTlF6clFtMzlIZzRDeGdLa1dWZDMxcXRDdklEM2ljRHJsa0VWZnJRUDRPb1ZDUFZ6eEdYNjBGbUU2anhRYXIzN0x3QnZ5eXp5ZWpQVmVDR3lLZlJDaXphNDRCRkoxa2Q2ODVlNkJWL3BlNzc2S3pqUzdPY3ZLaUIiLCJtYWMiOiJjODdkMDA5ODVjMWFlOWYwNjNmYzgyM2Q2ZWZjNDkwOGQ2ZmU5Y2ExZTZmNTM2ZDhhZjAyMTcyNTY2M2M3NWVlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:13:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdLdTNHbVpyVCtTYUtKZm5TSUgvdVE9PSIsInZhbHVlIjoiZ3JQNnV0SnZKSkh0QnhMWFJVRU1pSk5qRktqTHRJcUN4VkVGaUt4QjJZNnRvUGRTU3N2ZmNxUGV1QmVsS3BLRnR0ekY5Uy8rNEhPUU5YTG50dTdWWXhJWDlwbXduVUgyeWdsY2lCeUk1WTFhVmNyNi9NRjRhUzM2TEdBRitHOFBkNFVPV0h3V1RkTDY1YTY0ZGdueExMNkdsNXZpR1lWOTZRS1gvcytWT1d4NzhYaVpRME82TjI0UWVHMmFyWDczOVhKMXFUeW0rVG50YTduaEtJamFCLzlmcHcvamhLT01wNy9LcUJOTlFrTGRkc3JPQS8rWUpvZWduL1A4ZTlBOGhGd2xHS211SzJuZVF3dGFEaUw1amRUSHFZQ1ZsTjBSQ0tsbEhUVU1IVWJsL3FlY0N2Zi82bXBTQjZ1MTdVbTJ1b2dIZVZSVk1qWjlJVmVZaDBaT2lCN0taaW5ZUklrMi9mbHRoNlJHcnNQODNKZlgvM1JQb01FQlBuWDJHVi81VGhFZ3Jyc1FXeWwvaElzU2RZZ2pNcTAvZzZJdVNLMFpWSGQyMmNLaEo0UzV2VHU0K2NLaHJMOTlJdDF0amprM3BIdUNpQ3drL2xwa01zakxGUjd6cy9VT213M2dRK1QwU1ZjWkJ0Kzd2dVdITTN0c243RU15UEFjZUY5S1F3ajUiLCJtYWMiOiJhNTFhZWM0ZDU3YWYwYTc1NzIzYzQwZjNhOGNmNzM5Zjk4MDhhMmU1OGUxYWY3OWZiOTI3ZTc5ZDJkZjA1YjcxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:13:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhreGRDbVB1NS8yUmxVM25RQXJ1UEE9PSIsInZhbHVlIjoiWksyckJMblZVaDRJMjFtU1FybkF3ZUNqWEZpd1A3Q3JtcmR5Mkw4ZERJZDVLbEZBT3JiMnpKbnJoNDdWLzdHNStZN1IvNnd1LzdiMmhWdlIwdlpCRU41dmNLM3N2a2pmOGFvSGJGbHdhWXhxQTZQQjc2eHFFcFRCbzZKdlJSNks2eUNUSlMvOTg0cWdLWHdvSTFYV1U0cWdoMWFPQ0JWaTJHMmtCRHpXeCtKRDJVemZrSWsyeURJbkdZMlJUQjhpMG81SjA2cmQ5UnBjMFBkUDVoamYydXA1RHhBNU1hOTVhbGNza3JyOGZsaWpoQU9vU3hJU2tWN1g1Z2lvM3NFOEtyVVJBQTl4K0xRZjFkRVNFaHk4Rm82anhNSVRUZFRGdGxyWE9ZYnI2TVZrNEZRVVp3KzA0K3hLL01HOTFGNVVqRjNyMFlvSUNjTmdPK1AxaytmQVRraHcwakRBYUJCY1Nlcmw0Y0F2endaVG5TTW1XZys4c3p3a1gyaHlNYXk3YUFBcE1wZFNzTlF6clFtMzlIZzRDeGdLa1dWZDMxcXRDdklEM2ljRHJsa0VWZnJRUDRPb1ZDUFZ6eEdYNjBGbUU2anhRYXIzN0x3QnZ5eXp5ZWpQVmVDR3lLZlJDaXphNDRCRkoxa2Q2ODVlNkJWL3BlNzc2S3pqUzdPY3ZLaUIiLCJtYWMiOiJjODdkMDA5ODVjMWFlOWYwNjNmYzgyM2Q2ZWZjNDkwOGQ2ZmU5Y2ExZTZmNTM2ZDhhZjAyMTcyNTY2M2M3NWVlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:13:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134141276\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-888420140 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">waWGspPQkt52A9L0XQfJhxrltDc4t7Gl3qHy74cA</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888420140\", {\"maxDepth\":0})</script>\n"}}