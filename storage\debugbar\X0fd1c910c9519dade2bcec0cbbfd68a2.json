{"__meta": {"id": "X0fd1c910c9519dade2bcec0cbbfd68a2", "datetime": "2025-07-31 17:14:54", "utime": **********.327281, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753982091.908619, "end": **********.327319, "duration": 2.4186999797821045, "duration_str": "2.42s", "measures": [{"label": "Booting", "start": 1753982091.908619, "relative_start": 0, "end": **********.060957, "relative_end": **********.060957, "duration": 2.1523380279541016, "duration_str": "2.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.06099, "relative_start": 2.1523711681365967, "end": **********.327322, "relative_end": 3.0994415283203125e-06, "duration": 0.26633191108703613, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02292, "accumulated_duration_str": "22.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.235829, "duration": 0.018359999999999998, "duration_str": "18.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.105}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2837899, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.105, "width_percent": 6.501}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2955709, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 86.606, "width_percent": 6.283}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.305039, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 92.888, "width_percent": 7.112}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-413594620 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-413594620\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-950814615 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950814615\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1553100365 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1553100365\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhxS1plUElnanl0SnhCYXViOE5kSHc9PSIsInZhbHVlIjoialQ4QTluTlhQckcvdkZFbnR3YnRzc09CdVBaSzhKSklNUTdGc3hVQ0RMTzduR3JMaUM5RzM0c2prSW4ydEV4dVd6M0l6SDd0QWtnVnBsK1JQTXlQNEJNMHlraEZZVnJmc3lFR1dISTJHL2V1M2YvQkxhdmpQUFYybDVXbGtJamJEWHF6eVd0TFNHdGh4T0dTV0NTVGdCQ1A0dmRUbzdXVEF3ZzBvUEVoR2pRMm9hdnNIcE96ODdSTS9ScnEyNFNSNmNicXNMUXBwcUF0ZFZWYUJWZVFrU2JVRXZJWGlMZTZDQnp5SElkZHgraldVY2lOSklPSG9ESGZMdDZoK09FSFBUL2FGZkdOV0ZzZWN3dzQ5SjV6V0xxT0ZCQ05wekNuUmtLRUlISjR0b3U4bkg4MGZicUcyTVd0bjk1Z2J2RlJEcXlidGhtb1p2ZmZMZzdnWTh5Z0RIamE0S2F0dDRqM1NNLys5VHpaMklENkl3NmJpN3JVS0VwalJFd1gxTU1JeTl4UmVQRERRRkNHb2JRc0w4Njg0VDZ4U0xDUDhuaTJ4MXl6QVNNOHBYY2YwOGI5NWJiRlpUekVWOGdVc2N1OE9wSUFmNXJhb2NkRmI3ZXJJV1FtVTdmeHBVcDI0L1pwZWkxSG1mbzNqRENrbFVJTlgzRWV2aWh3clNQRExlZTIiLCJtYWMiOiI3MmI0NzFjYzcwNjg2OGNhM2ViMmI3NGY2MGE1ZGNiOTY2YzNjZmFhMTNlODU1OWExYTY3NDMxN2JkMjc5N2UzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InVnMTJBNEZEUlQwM2g5eFVVL2Y5NFE9PSIsInZhbHVlIjoiZXAwWXhTV0VNNzNZZDN3cmxuTG1iNnhieTVFZlBHa3VXY2luTEZjakdxSWtFOFZrbnpuSUpqOVgrQUJUMENIdFQ5YmRQYUt4N2czbTZXaUdkTHBEMTVibXZjNmo3T292dS9hdERGc2pKZWtEUDJXKzU0UkZoSWhnZHlRVGxyQldmN1I4Y3ZBN0Q1MkN5dXJQVVdYNWtNSzlnQnlybXRVWDVnM01Bd2g4SFBSTm41cHp4UWtNOXdZRXlObzJReC92dTJ2MW41dDBWQXgybDBENUR3dWRxVFdwbVI5ZzYyRjF1TmVSRzRxaFJobXZmL0NYNEJkZ0FZY0FXcm5EUzc3V0tJdHl6NWdDa2ZBQXRhRitrVDR4TGR3SWZzMFpGTVRrbDMwQTBla0laVUdPZTVuem12VGo2QnNTQ1VJOWtycHJleGdMeHdJZnlaUkE2a0w2ZHhMMHdRbCt2N00rOERtVm5WUEUyeXRQYnU4SHhRZFhDUVpOSndDTk9LNmVaaDh6M3FQQ2tYSnpiRHN6SkNLZTdpd3pJMURSN0pEdFhicFJYWnBtOHF6aHNkVXhEc21mRnMxZjBYVXVBcHR2eE5MYmVpSnhJT1Q1TnFhcTE4VkU0Q3FaZEtzMFRHM0xHQzZsYVpSa2JFb21wTTJiRkNmeTV1WlVMSXFVdFN1OEQ3blgiLCJtYWMiOiJhM2Y3MWZjNmVmZDFmZmJhODcxNjY5YzhkMzU0NDE1Y2EyMjk5ZjJlYTc3ZGVmNzJlMWYyZjc0ZDczM2JmMmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-491820465 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:14:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iit0WEpwQmVGNzhnR1RlWU5uNzZaZmc9PSIsInZhbHVlIjoiUTBSc29uQWp1dTVKQi95bm9QeWRjdDlCK3k2eWlrWVk1cWhvcDc3cnVmdEgxOTRycG94a1NaNFRpTHVSMlJpOHM3ZXQ2RG9zanlrVDhQWXN6WkRsZ1pDSjd6MEwwOWlGcStvQ1lEY0VIZGJCRGU4T2lsWXFyZ2t5b2hQSFJBQ3dSOG1SUmd4OVdPSXc4NDNiNmJFbVdzaHA0aHAwajRqeXloVlgrYXg3ZGZKUi9jSkc2R3pkekhkeG1qb0h6YXNKVGFtVm9NM0swTEh3cmNxRlVJZnFUeklGUlA3ZUwvRklGYmxKa1hXUkgxU3BpeGJ3Rmd1V0p6djE4Zm9qQmZoUTRDVkZxdVBCQWlmQXQvVXZ6WkVYSkN6SWh3cGV1YjVEdXFVNkFPTDdwTXAyaFBlcUwwcjA3SFN3VTQvM3h0RTdTYkl0MHVHTHFvVU5kNkF5OFA4a3h6a3dSRk9DUTZtWjQxbWNCTEd6N0NnbEZmYSs3R3JKd0FPc1NKNUJxWFBJSzd2NkJ0c3VSSmFIYXdPYWF4ODk2QlBadDFER0FUMjhtNnp4QW00SDVCZU9zUnRWWGlqS3UwT29CRDBneXh6cEViM0FLRUZaOUd3dXl2ZDBaMm9nbW90dlF6RlVFdmgxVXFWUHcwWWExS3owcTgwWVJMQlFqL1hNMnVvWEx0VmwiLCJtYWMiOiJiM2ViNzllNzk3MTBiMTViNmNiMWM1ZjRmM2I4MDlhNzc1NDExMTY1ZDg5NzdiYWU2MDM5MjU1Y2MwYjU5NWU3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNvMzk2Z0xjRkZNOUFwWENUakdWbHc9PSIsInZhbHVlIjoiY2l1MkYrR1R4Z2d5cUF1b20vc0hFSEVyUExSRWlYREQ0aVVwQ1NXMW1lVkphQzNoZW5LWThzTDkxenZKNFprSlZSamxmWEszY1ZXZEdhY0hYOS9COU95RnltQkRDUWs3SzR0N0YxOVRheWhFVTR1Vmc2dnJmY0ZRZkpHT0pHczVsVFVzRGZDMi8reWZPcTBXMGxPWTBwZTh2WXd6YjdCKytmL0cwcGpaa0YwcXVycVhWamFXc1o5aVdQaFdlWGVhUkovOGR0bUs1WUN0N1BHa1EzRDJOZk5uL0d5b1VCNnlDam1wUXQyWUlHVkxNN001QlA0MFRIRkw2VlNCSEpJTmM1NnNtK3lORFl5N21aMFFUY1R1T1JEb3RqUUV6N3haSEprSGV4WGpORWhLODlsTmZGM3l2MVJRdForV1h1NCtSUSszSk00M1JNbW1OL05iQTZWQmlKMUtsa3Ixa1VxVGxabGpkU2l1blI1cHordnVaQ0I3Sk1hQTNTYmNpcFJWRG82T0drcysxZWNkY0IvYWlES0Q2MHNKRzZvZHNsWTVKSlZRL2YzTS9iSTRVMjVTby9QNUwvY1FjMkljTU5LL0xWVkw4TTIybktmTTByOEkrWlBodk54cEZuVm1wL0ZGaVBpUkNoSmpVQjB1MFExcFJaanBCcHJJUTRBMHZCRVciLCJtYWMiOiJmYjQxYzA3ODVjMDhjNjAzYjk3ZGQyY2QwZGVkZTFmMDFmYTg2OTY1ZmFmNjE5MDRjYzhhNTdmZjQ4NWZmMGQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iit0WEpwQmVGNzhnR1RlWU5uNzZaZmc9PSIsInZhbHVlIjoiUTBSc29uQWp1dTVKQi95bm9QeWRjdDlCK3k2eWlrWVk1cWhvcDc3cnVmdEgxOTRycG94a1NaNFRpTHVSMlJpOHM3ZXQ2RG9zanlrVDhQWXN6WkRsZ1pDSjd6MEwwOWlGcStvQ1lEY0VIZGJCRGU4T2lsWXFyZ2t5b2hQSFJBQ3dSOG1SUmd4OVdPSXc4NDNiNmJFbVdzaHA0aHAwajRqeXloVlgrYXg3ZGZKUi9jSkc2R3pkekhkeG1qb0h6YXNKVGFtVm9NM0swTEh3cmNxRlVJZnFUeklGUlA3ZUwvRklGYmxKa1hXUkgxU3BpeGJ3Rmd1V0p6djE4Zm9qQmZoUTRDVkZxdVBCQWlmQXQvVXZ6WkVYSkN6SWh3cGV1YjVEdXFVNkFPTDdwTXAyaFBlcUwwcjA3SFN3VTQvM3h0RTdTYkl0MHVHTHFvVU5kNkF5OFA4a3h6a3dSRk9DUTZtWjQxbWNCTEd6N0NnbEZmYSs3R3JKd0FPc1NKNUJxWFBJSzd2NkJ0c3VSSmFIYXdPYWF4ODk2QlBadDFER0FUMjhtNnp4QW00SDVCZU9zUnRWWGlqS3UwT29CRDBneXh6cEViM0FLRUZaOUd3dXl2ZDBaMm9nbW90dlF6RlVFdmgxVXFWUHcwWWExS3owcTgwWVJMQlFqL1hNMnVvWEx0VmwiLCJtYWMiOiJiM2ViNzllNzk3MTBiMTViNmNiMWM1ZjRmM2I4MDlhNzc1NDExMTY1ZDg5NzdiYWU2MDM5MjU1Y2MwYjU5NWU3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNvMzk2Z0xjRkZNOUFwWENUakdWbHc9PSIsInZhbHVlIjoiY2l1MkYrR1R4Z2d5cUF1b20vc0hFSEVyUExSRWlYREQ0aVVwQ1NXMW1lVkphQzNoZW5LWThzTDkxenZKNFprSlZSamxmWEszY1ZXZEdhY0hYOS9COU95RnltQkRDUWs3SzR0N0YxOVRheWhFVTR1Vmc2dnJmY0ZRZkpHT0pHczVsVFVzRGZDMi8reWZPcTBXMGxPWTBwZTh2WXd6YjdCKytmL0cwcGpaa0YwcXVycVhWamFXc1o5aVdQaFdlWGVhUkovOGR0bUs1WUN0N1BHa1EzRDJOZk5uL0d5b1VCNnlDam1wUXQyWUlHVkxNN001QlA0MFRIRkw2VlNCSEpJTmM1NnNtK3lORFl5N21aMFFUY1R1T1JEb3RqUUV6N3haSEprSGV4WGpORWhLODlsTmZGM3l2MVJRdForV1h1NCtSUSszSk00M1JNbW1OL05iQTZWQmlKMUtsa3Ixa1VxVGxabGpkU2l1blI1cHordnVaQ0I3Sk1hQTNTYmNpcFJWRG82T0drcysxZWNkY0IvYWlES0Q2MHNKRzZvZHNsWTVKSlZRL2YzTS9iSTRVMjVTby9QNUwvY1FjMkljTU5LL0xWVkw4TTIybktmTTByOEkrWlBodk54cEZuVm1wL0ZGaVBpUkNoSmpVQjB1MFExcFJaanBCcHJJUTRBMHZCRVciLCJtYWMiOiJmYjQxYzA3ODVjMDhjNjAzYjk3ZGQyY2QwZGVkZTFmMDFmYTg2OTY1ZmFmNjE5MDRjYzhhNTdmZjQ4NWZmMGQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491820465\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-932462116 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932462116\", {\"maxDepth\":0})</script>\n"}}