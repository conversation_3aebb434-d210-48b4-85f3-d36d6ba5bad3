{"__meta": {"id": "Xfc25ae8ea9fc29b56c5ab68908e7337e", "datetime": "2025-07-31 17:14:07", "utime": **********.058026, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753982045.603998, "end": **********.058059, "duration": 1.4540610313415527, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1753982045.603998, "relative_start": 0, "end": **********.83106, "relative_end": **********.83106, "duration": 1.2270619869232178, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.831084, "relative_start": 1.227086067199707, "end": **********.058062, "relative_end": 3.0994415283203125e-06, "duration": 0.22697806358337402, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.028450000000000003, "accumulated_duration_str": "28.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.960476, "duration": 0.023600000000000003, "duration_str": "23.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.953}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.012129, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.953, "width_percent": 5.343}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.023469, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 88.295, "width_percent": 5.343}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.03347, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 93.638, "width_percent": 6.362}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1703703721 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1703703721\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-889679582 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889679582\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-440144529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-440144529\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1499600289 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJ1WlhnYTdxUm5TajVPWVk0YkZ5c1E9PSIsInZhbHVlIjoiL0d2QUV6c3h5Y09TSGJORzdXdzBxcTFyaXc2dUcrdWpqejhLL1dORVlRTWQzOWxzUHNqNHBKenZkbnNQdGlpbE41ekNYVTZpRnNhbUx1L3Q2NzRXbnpVUm5IVjk0YkxGS0VPWE1PYmNPanRhekVqa0VMRklJTFZSQUJ0UG1pOEFPTnVMVmZxelA2MUlDVkEzL0dQWW1YdnlHajhsYytyQ1BUY1ZpSDhyaVY4OHpJZ3pzMTUrbHV2aWN4cFVaTmZ4ZTErd1lWQW15UmZpOHNiRG5nTld3eWhvQ3JlT1NHRUdJTm1CMnhoMWlNRVB0SlVGbHNxZFp5WTRuOFZwbC9UMGNBZWNFaTQ3ZGM2a3RUdnRhSkRSYkJ0TjMrTEFxUmdYZ1pTQWxOY3JoWjB2aEVBc3VBcFJKQ2NmNU1lbFlDL0gxenRhS1BvOGNBZTUxanVrOVRCNGVaVFhUUXhLUHlWbzF1bWozSFlTMWtFUFFYQndiaGFhOStSUWFCTkg1TDh6Z2N6dmJhYTNEUWpoSzk2ZkhneE5pTnVwSVJxK3hUd3JRc29ER0FVaHgvdnorRkpjKzF3L1g5Rkk4b2Z6azlzdVdjM3Fqay82Tk5zNURkUkQ1dXNRckp6NFRpcUQvWnhDN2prR3o5Y0VnRWY5MFJwaEk2YWozUUNwZWpFRFk5WVIiLCJtYWMiOiJlMDAyZDZiOTNhNmE2NzM2MzIzMGVjOGRjNmYwMTJkYjI5YTgzNzU4ZjRlZWQwOGIxZmNmZmY4MGVhZmUxNzhkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjJYTllrSjNsZmt2bjRKM1lYL1ZNY1E9PSIsInZhbHVlIjoicXpaTWthdDJWWktuUUp3bGRHMWIxRXZBczVyRlZoU2VSdlM1eHRTQ2NjMmVPYnErRlhjL3lWUHBlQ1BjaXRkZGVkeFYvZkMwbWlMb1JVZzF4U1B6NGQwaEZnM2c0dzJCL3JPT3h2cWhha0RVbjM5dkhhT1h0R2N2N2U2T1VETnhUQjdqZWcrQTJQRDZISDJsOWJyR016WVBqNkJSN0ltZUluRkdaNFNJV09EQi9WVEh3UUt2TTEzMmJyajAxNWNVWnpoWTcvWU5nSDJGQTdlcnZQQUpnWGlpS3JjeUVNUkI3SXZBcXI4ejBFaFU1WTRtbkNTelJjV1pBWHQ1MVpmVi9yS1ZjRGZkdUNBcU1VTndib3F3b0tGcDRsNmFrWkRIUEhmRzBIangvVXdWYU9mYU1hZHd0a29GOEEyaXl3V1hkQXJpQU9RK3dna1BpcFhoOXFpUTBpaUdSYkozTWpjdnhFSFduRTlwMjhPV2dWZWhWTjdBQUUvaU0rUXVxL2ZaMWZESDFSa3IyQWFodTJhQ2liWG5DRldqeklwMEwxVGVEVXBOeWtCWmsvWWtjeng5Z3Rjdm5qN2pYMCtoekhTRzMzMG4vVUZValowNjNYbWpvVDNDRm5zZTZvcU9zZytnYS9SYmhhVGk0Qmo3UHNCZSsyZVFpUk9NTDdHdzFQanYiLCJtYWMiOiI3NjY0OTgzNzRiN2U0MWNjZDM2N2JmODYwZTZlMzhhMGIwNzBiYTk0ZjU3ZTEwMjI2YTBlMzIzNjM4MzgwNDE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499600289\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1353711276 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353711276\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1901628045 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:14:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ0TmdXWjFQdm85UDIwVlJZNmc0V3c9PSIsInZhbHVlIjoiUWpPSklzVnk1aWlmNjlZeEtNc3VPNjQ3ZW9YdU1JUkEzczg3TklGLzQ0NTBJQWRzbFNBd2hWZ2Q1bEpYY1RWNnA3alI1eXM5N09QajNhNmdTbU5jTG94TUx6UytYVTRydEh6VGt6dWNQbVNFUmxkWDNHL0taZGdmd3diaUlFSEt6SGtUa2Qybk5hK1JsQXF1amVjSWtSZS8wSDVFdmQyWmRWcGlKMTBDZndlMmRkRzJOQis3RnVIbjJ3dnovbXdmcldGbWRoalVsQzlOY0FrKzNBa0VsS0dST0NSMTA1b1hrU2RXNGdVRUpOV0srcUx5UjF0QjBROGVWQTRtWHVKbjh6UWVFMHROUTVTSTZIYzVQNWxLZ3AvbDQzRmVDZmd2QmF4MDMvRytSS2ZSK1pvOEZxODJEdjBSZHkySUJjdldzNzdsZ0wrb21NQ0cvZ2kzVWV0YTh0UDlxZ3FON0dOanRRci9GdW9OMGt2TGxuSW9YcFYwa1ljV1dyV1k2MG1VZGN1SmM1MVgwUzgrQTl0ZnNrSTVnZDFNUXhyZFpPMHlna2hHM2lHb0Z4aTdNdDZCdGYzK05zRlhBT1VjZ2pIMEJKZEJWcCtiN1hsSjVDbkIwUVRKRHM1VzB5QXZ0TER6d3IxL09aenE5UjJNMVo3QVBDbEluUmVpelJldzQvOWsiLCJtYWMiOiJkM2ZjNjYyNmJiZDllZDQ0YWJkMmUzYjViYWY1ZDQ0NDgxZjI3NTJlZTcwZTRiN2VkNGMzYzQzYmFiMDU4ZWRmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ijg0QUM2YXJxTHVRMnNydlRwRDhQT3c9PSIsInZhbHVlIjoicnpVZnd5T0Nkb2N0VTZoeE1vQkVBOXdkV2wwdVViYjJYU0NmZUM5YUVIeTBPb2V2SGNkY0E3bVJ5UXkxVnlCZHYvakRzRklIZlgyNHZhODFWUnFPZXQxdE9ERlZpTHpjaWE1Q1ozNkcvQkUraUJjYVc5anpFN21DZ0pKbU1YZWVQWlhrZ0pmemZOMmQyWXZ4TGRBZC9BaldDMlhwZmJGRm1POWh4RTdoVVdWVlZBVHYxV1JMQUw0SmxlTDZVeDhpZWJxbGZhdVFpeUQ1N1NmcWZybk9HdHFzUG1YcDBZMWpxUmJ3NEorR08xYWlNQTNRWTNMMklCT2ZRbm1KZ205eWZrTXBlTFZmOEV4SS9tUTZ3aVN4VzNxRFNwMFF4U0gzenI5SjFJcnA3UVZ0eTlwUVE2MVV4bWlCdExBOE5SOHRmVWNSK1ZmUC9ycHZTNEFObFFOOHN0VzVnckduYnRobWZvV3lmU09kMnhRZnNUeVJLT3B0VmN3bnJHN2xDQ2l0azFWallTcHVmd2hiN0dlSnZQOE92ZGh1R0pEWVlvV1hwOXRSYnFVeWlLKzJ5NmZmb1ZPa2FLVXNKN01CSmFvenhoalNSZ1JuOHE1aW5VclE3KzZaOWhxTlpkMVRZVXk4V0ozd05FamZOdUJYNVFPYzRuSVlUZUdOa0M3bFB0cHciLCJtYWMiOiIzYzJmOTAxY2E4ZTg5NWQzZTJlZDlkOGU2M2U5YjVlZDc0NzU4YjFlODYyYjgwOTA5Y2UxYTRlMWZlNmUxOWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ0TmdXWjFQdm85UDIwVlJZNmc0V3c9PSIsInZhbHVlIjoiUWpPSklzVnk1aWlmNjlZeEtNc3VPNjQ3ZW9YdU1JUkEzczg3TklGLzQ0NTBJQWRzbFNBd2hWZ2Q1bEpYY1RWNnA3alI1eXM5N09QajNhNmdTbU5jTG94TUx6UytYVTRydEh6VGt6dWNQbVNFUmxkWDNHL0taZGdmd3diaUlFSEt6SGtUa2Qybk5hK1JsQXF1amVjSWtSZS8wSDVFdmQyWmRWcGlKMTBDZndlMmRkRzJOQis3RnVIbjJ3dnovbXdmcldGbWRoalVsQzlOY0FrKzNBa0VsS0dST0NSMTA1b1hrU2RXNGdVRUpOV0srcUx5UjF0QjBROGVWQTRtWHVKbjh6UWVFMHROUTVTSTZIYzVQNWxLZ3AvbDQzRmVDZmd2QmF4MDMvRytSS2ZSK1pvOEZxODJEdjBSZHkySUJjdldzNzdsZ0wrb21NQ0cvZ2kzVWV0YTh0UDlxZ3FON0dOanRRci9GdW9OMGt2TGxuSW9YcFYwa1ljV1dyV1k2MG1VZGN1SmM1MVgwUzgrQTl0ZnNrSTVnZDFNUXhyZFpPMHlna2hHM2lHb0Z4aTdNdDZCdGYzK05zRlhBT1VjZ2pIMEJKZEJWcCtiN1hsSjVDbkIwUVRKRHM1VzB5QXZ0TER6d3IxL09aenE5UjJNMVo3QVBDbEluUmVpelJldzQvOWsiLCJtYWMiOiJkM2ZjNjYyNmJiZDllZDQ0YWJkMmUzYjViYWY1ZDQ0NDgxZjI3NTJlZTcwZTRiN2VkNGMzYzQzYmFiMDU4ZWRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ijg0QUM2YXJxTHVRMnNydlRwRDhQT3c9PSIsInZhbHVlIjoicnpVZnd5T0Nkb2N0VTZoeE1vQkVBOXdkV2wwdVViYjJYU0NmZUM5YUVIeTBPb2V2SGNkY0E3bVJ5UXkxVnlCZHYvakRzRklIZlgyNHZhODFWUnFPZXQxdE9ERlZpTHpjaWE1Q1ozNkcvQkUraUJjYVc5anpFN21DZ0pKbU1YZWVQWlhrZ0pmemZOMmQyWXZ4TGRBZC9BaldDMlhwZmJGRm1POWh4RTdoVVdWVlZBVHYxV1JMQUw0SmxlTDZVeDhpZWJxbGZhdVFpeUQ1N1NmcWZybk9HdHFzUG1YcDBZMWpxUmJ3NEorR08xYWlNQTNRWTNMMklCT2ZRbm1KZ205eWZrTXBlTFZmOEV4SS9tUTZ3aVN4VzNxRFNwMFF4U0gzenI5SjFJcnA3UVZ0eTlwUVE2MVV4bWlCdExBOE5SOHRmVWNSK1ZmUC9ycHZTNEFObFFOOHN0VzVnckduYnRobWZvV3lmU09kMnhRZnNUeVJLT3B0VmN3bnJHN2xDQ2l0azFWallTcHVmd2hiN0dlSnZQOE92ZGh1R0pEWVlvV1hwOXRSYnFVeWlLKzJ5NmZmb1ZPa2FLVXNKN01CSmFvenhoalNSZ1JuOHE1aW5VclE3KzZaOWhxTlpkMVRZVXk4V0ozd05FamZOdUJYNVFPYzRuSVlUZUdOa0M3bFB0cHciLCJtYWMiOiIzYzJmOTAxY2E4ZTg5NWQzZTJlZDlkOGU2M2U5YjVlZDc0NzU4YjFlODYyYjgwOTA5Y2UxYTRlMWZlNmUxOWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901628045\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}