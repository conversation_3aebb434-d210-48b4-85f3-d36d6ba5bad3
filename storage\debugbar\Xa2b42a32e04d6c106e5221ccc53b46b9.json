{"__meta": {"id": "Xa2b42a32e04d6c106e5221ccc53b46b9", "datetime": "2025-07-31 15:55:06", "utime": **********.656687, "method": "GET", "uri": "/finance/plan/get-product/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977304.942156, "end": **********.656724, "duration": 1.7145678997039795, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1753977304.942156, "relative_start": 0, "end": **********.39157, "relative_end": **********.39157, "duration": 1.4494140148162842, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.391594, "relative_start": 1.4494378566741943, "end": **********.656727, "relative_end": 3.0994415283203125e-06, "duration": 0.2651331424713135, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51530856, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/plan/get-product/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getProduct", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.plan.get-product", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=233\" onclick=\"\">app/Http/Controllers/FinanceController.php:233-257</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.03014, "accumulated_duration_str": "30.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.495157, "duration": 0.02075, "duration_str": "20.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 68.845}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5404289, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 68.845, "width_percent": 5.839}, {"sql": "select * from `products` where `products`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.55482, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 74.685, "width_percent": 5.707}, {"sql": "select * from `tax_slabs` where `tax_slabs`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.571945, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 80.392, "width_percent": 4.844}, {"sql": "select * from `product_emi_options` where `product_emi_options`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5867488, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 85.236, "width_percent": 4.944}, {"sql": "select * from `product_shipping_fields` where `product_shipping_fields`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5979538, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 90.179, "width_percent": 4.944}, {"sql": "select * from `product_bump_offers` where `product_bump_offers`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.608793, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 95.123, "width_percent": 4.877}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\TaxSlab": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTaxSlab.php&line=1", "ajax": false, "filename": "TaxSlab.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/plan/get-product/2", "status_code": "<pre class=sf-dump id=sf-dump-1080206498 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1080206498\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1608950301 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1608950301\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-68594755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-68594755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1772462260 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRERGJ0bGs3OWlnSU8yRU5HQXJQTWc9PSIsInZhbHVlIjoiMjNNeC9DRys4UUFSc2FXUU5oUnVCTjMxOHl4MWo5cUIzbnhPWGRrOEVmQXJOZGZvTXM2M2VyaitkdE8vVU9rUEhrQ1grY2Q0bFg2eDR0YTRsTVMyMm05VGUxZzlFL1A2cTZmb2FudVVBbEdTbkxiWnZPdTZ1UGE2a21PcVRGckxRSVMwdXNhK1pZckM0L1dwcFJoRmdNVFZ4MTdxNnA2cEtLUWVxdmNPUlZlNGQwWlRPVGVmclAwTW85VitNWlphNlF4cml0c05HaUc5MTMyY3ZOUk1IUE5IT2xVMmY1UmlFbGU2dHN4aTlLc3VrWHJMVUVIakZlSHo3bFh1M2ZMb3hiOVJ0M3hNb0R6NVlqVXBSelRlUngxK0pHMXljZVF6SzcwbVZVTDhiZ1lEaEFtZGk0SEkwZFNKSHJFZ3V3Q2lyZUpKSXBDVG11VFpENWJ3L3d2RzdKYmx5VUVldjV3VDgxYStHalZnQ0JJQ1dyUXJEQ2dvQzFvVndIRGt6T2ErS0UyM24zekpWbEZ0cnVURGMwNWdCREJObWwwcm5GazNYTXlKQ2NoTm13Z1Z1Wi9IU0V5dG5CU2s1UXc4cWNoSGt6MnVLcnJ2bGROVXVHRkNPRUhsSjJGQjRDWnhzZTBQQ0tURW9tcnZYNUNvYmN3S3p4RU13d2J5QXZYa0UyeHciLCJtYWMiOiJjNDAxZTE0NDEwODQwZmQ0YzNhOTU3ZTgyNzI0YjNiODZjYTEyYTQ1NjZmN2UyYzU2ZmEyMzhiYzg1Mjc2ODRlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImJXanJ1NjNyaWZlSlpzRFZIdzI4RHc9PSIsInZhbHVlIjoicXlpVHlXejFYdFIvNVh4QnE0elYxV1lyNVdJWVZ4UDY1VVpXSEl2QkZsTDh5VEFhR1IwSXdpWFNMdHVxSHdXU1VNZkVkY0RvS0VFelpFbWtVNCt2QXAyWmVlSFFTSW9pRWM5dmFNMTNqMzhsNUJySWNvQnlJRC9DQk5IK21xQW8xN2dndVJUOVh1RzBGejBtWDRFdlBNcWFYNGx1VWN2d1cvd3JRcTBhWUNneENNbzBIbnpweG9kYlYwVXRoRE5hQWg5MllodEJITGQyb3JXa0lsTUlIdlVUUEZZTFpiNHI5RXNCa0FIK3dGcGxYdmJIMzNSdWttZERzL1dPdU5MUXgyVm5oaTh3dldQZ2xIcm5hTDEvblMyYThOVzNhRlJsU3VNUEhHL2UwVHdqWnYrMjJKRElFM3JNTWx0NFVuNThsTEVldW96SXN1MTJtRnhDYUxPNUV5azk5SmxTYWZqUjErOE9reHdBajVuQ29pZHZlUjRVVzI0RGVrYW8zSVF2dGFhVCtEWG9jL0tDZWZDZHBueUJlN0czTDlaYk5mWjhhWFV0TWNEQkVjTnRKVEd1eTROa29Rbzl6L1plcG9VMzBHSE15N3VMWDZsczMxOG9zakp4dUozMEhyZU9UbDNyWll4ZExtekwvbVhUWStFZzZSdUJSc1ZtcVlMdFhJWFUiLCJtYWMiOiI1Nzc5MDJmNjgzNzQ5NDJlYjdiYmI1MTUxYTQzY2I1NzAzOGFjZGE0MmM1YTUxZjZlYmE4ZGFmYTlmZmJkYjNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772462260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-851446345 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851446345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-212451935 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:55:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZDalBnUzlXQ2RXZExoUVN0VTJLbHc9PSIsInZhbHVlIjoiMDhQUHVqcTZKTXhDcnhyS2JtUEFkbGxOTUxFdWU5WXFQcTMxVFgxeTlKa3ByczJ5YlphQUZqam9yUC9NUnh2dHlTZzMvNjY3ZlRsNWVZT1BiOURpMjhzTnpxcHBaM1NYSG9UNm01UFV6NXUrNXFaMUplc0I1QUJGRFZlMWg4a3ZGNnlIUFZVc04rYmVxWkhVNzA4WW5oM2hVdmVEeFVFeWUwZFpJWllWbHVpMDZuR2luYWM5TVR0ekdKZE0vZ0d0OHBzTXcwcVk3cE5wNFR5TWhRQllJVW5JeUpsYXFuclNCTmY3eEVVUmZLUW5KUi8zVXNwcXZsQjhINW90d016NlZXM2hBY2lFaEw4ckcxOVhMZHRldDl1aXZiUGZjb3FWQUgvSFlPS0pvVnBBSS84ZEJkVk95NDMxTTVwajczMFpqcS9TTFVxbWdmMzhMSndYZjBHcG1pVE5RR0V2Z2o4emprZW5uOWxFcGhWZjlpTGdmUGFjYmpYSXhzOXVqbHlkVDNLYklDdEYrOHo2ejNTZ0Z4ZTJoT3ovUllRWm1vdTJnWlk3UzcrRkRHYmpLUDYzWlplU2Q4US9lK1JnYnBBbWUzRldwNnJSbnM5VzJHQkk2bXgxaW10UlBqVkNQbXk5LzE4OExtZTNIbU94dzlibWpVTVdCTHo4SzZaVTd3dnoiLCJtYWMiOiJjYjVkZjZhNTRjNTJlMzc4ZDkxODFkMzlmNjI1Y2MzZWU0YjQ5Y2E4ODNiN2UyZjM1M2MyYjI5YmViMGRjZTFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:55:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhMRU85eThZWStEUEpmOVRUaG56Zmc9PSIsInZhbHVlIjoieWlxNE9HQ1FaQzlTQmMyMkhpcnBETythallad0QzS0QvdVBJQ0tpWkI4MEtZVlFhRUdIZFo5eU1aNitrUVdpM2g1N0t2VER1Vk1VSjBZRHdFeUNpT1ppR1lMNVR3cXBSblRWYzJJd2c5RnNVd21FaElnTkFOaWs1c2NXekNjT3Mzb1JSZTM3S1F4L1MyRWVlellMcjBuY0ZjN21ZNmRSZC83QUNlR3l6Y2NjNkJtOE56M3hjZ2xFRDJ3NFV6WlhvQk9MdjlHekRQVWw3MXpHZm5FaHhpZ0xhVjZOR0xBWnRlZmlweUVpTEo3ZmVOV3NQSnE3NmFVQ3JWd1JOMS9ybm8xUDVWOE9vS2NKUTgzZWFOMk5JZkRsWTAwcGttK3ZwMnRaclNkTXluZjYxWnRkcytkYVBESi9vVUFONXJ6QnpXRFJWWXh3TlZuSHZKVExIdTYwK3VUcTczbjhaUkk3ZDVvMXBwWmxrVnl4L05TRzA4UldMemlNM3BZQlVKalNxaW1QR3BpekIwNE1SQ1FGc1NvWlpIeXpJeTh2VnVKVnY0dGlhcDhoMHBUQlcyallIZmJ5T0VCZWduL0JscFE4ZXJXTDJRL0NQcVVPMG5WMkk3cVNKYjE1UjNrcW1Xbi9MNFJqOXpKeWJIZUdNL2tQbXJ3SnZia1ZVbm1NZEhIV0giLCJtYWMiOiJiYTEyZWQ4MDlhMGNmZGZjODg4MzE1NDQyMDExZTY0NGE4NzliZjNlNDdjMzFjOTVkNjc5YjVmMDljYzkxMmJhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:55:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZDalBnUzlXQ2RXZExoUVN0VTJLbHc9PSIsInZhbHVlIjoiMDhQUHVqcTZKTXhDcnhyS2JtUEFkbGxOTUxFdWU5WXFQcTMxVFgxeTlKa3ByczJ5YlphQUZqam9yUC9NUnh2dHlTZzMvNjY3ZlRsNWVZT1BiOURpMjhzTnpxcHBaM1NYSG9UNm01UFV6NXUrNXFaMUplc0I1QUJGRFZlMWg4a3ZGNnlIUFZVc04rYmVxWkhVNzA4WW5oM2hVdmVEeFVFeWUwZFpJWllWbHVpMDZuR2luYWM5TVR0ekdKZE0vZ0d0OHBzTXcwcVk3cE5wNFR5TWhRQllJVW5JeUpsYXFuclNCTmY3eEVVUmZLUW5KUi8zVXNwcXZsQjhINW90d016NlZXM2hBY2lFaEw4ckcxOVhMZHRldDl1aXZiUGZjb3FWQUgvSFlPS0pvVnBBSS84ZEJkVk95NDMxTTVwajczMFpqcS9TTFVxbWdmMzhMSndYZjBHcG1pVE5RR0V2Z2o4emprZW5uOWxFcGhWZjlpTGdmUGFjYmpYSXhzOXVqbHlkVDNLYklDdEYrOHo2ejNTZ0Z4ZTJoT3ovUllRWm1vdTJnWlk3UzcrRkRHYmpLUDYzWlplU2Q4US9lK1JnYnBBbWUzRldwNnJSbnM5VzJHQkk2bXgxaW10UlBqVkNQbXk5LzE4OExtZTNIbU94dzlibWpVTVdCTHo4SzZaVTd3dnoiLCJtYWMiOiJjYjVkZjZhNTRjNTJlMzc4ZDkxODFkMzlmNjI1Y2MzZWU0YjQ5Y2E4ODNiN2UyZjM1M2MyYjI5YmViMGRjZTFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:55:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhMRU85eThZWStEUEpmOVRUaG56Zmc9PSIsInZhbHVlIjoieWlxNE9HQ1FaQzlTQmMyMkhpcnBETythallad0QzS0QvdVBJQ0tpWkI4MEtZVlFhRUdIZFo5eU1aNitrUVdpM2g1N0t2VER1Vk1VSjBZRHdFeUNpT1ppR1lMNVR3cXBSblRWYzJJd2c5RnNVd21FaElnTkFOaWs1c2NXekNjT3Mzb1JSZTM3S1F4L1MyRWVlellMcjBuY0ZjN21ZNmRSZC83QUNlR3l6Y2NjNkJtOE56M3hjZ2xFRDJ3NFV6WlhvQk9MdjlHekRQVWw3MXpHZm5FaHhpZ0xhVjZOR0xBWnRlZmlweUVpTEo3ZmVOV3NQSnE3NmFVQ3JWd1JOMS9ybm8xUDVWOE9vS2NKUTgzZWFOMk5JZkRsWTAwcGttK3ZwMnRaclNkTXluZjYxWnRkcytkYVBESi9vVUFONXJ6QnpXRFJWWXh3TlZuSHZKVExIdTYwK3VUcTczbjhaUkk3ZDVvMXBwWmxrVnl4L05TRzA4UldMemlNM3BZQlVKalNxaW1QR3BpekIwNE1SQ1FGc1NvWlpIeXpJeTh2VnVKVnY0dGlhcDhoMHBUQlcyallIZmJ5T0VCZWduL0JscFE4ZXJXTDJRL0NQcVVPMG5WMkk3cVNKYjE1UjNrcW1Xbi9MNFJqOXpKeWJIZUdNL2tQbXJ3SnZia1ZVbm1NZEhIV0giLCJtYWMiOiJiYTEyZWQ4MDlhMGNmZGZjODg4MzE1NDQyMDExZTY0NGE4NzliZjNlNDdjMzFjOTVkNjc5YjVmMDljYzkxMmJhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:55:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212451935\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1934634046 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934634046\", {\"maxDepth\":0})</script>\n"}}