{"__meta": {"id": "X4dd2f9d470b51c17ac5bd36e0ad94d4f", "datetime": "2025-07-31 15:54:48", "utime": **********.482693, "method": "GET", "uri": "/finance/plan/get-product/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977286.766596, "end": **********.482741, "duration": 1.7161450386047363, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1753977286.766596, "relative_start": 0, "end": **********.235508, "relative_end": **********.235508, "duration": 1.46891188621521, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.235543, "relative_start": 1.468946933746338, "end": **********.482746, "relative_end": 4.76837158203125e-06, "duration": 0.24720287322998047, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51530856, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/plan/get-product/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getProduct", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.plan.get-product", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=233\" onclick=\"\">app/Http/Controllers/FinanceController.php:233-257</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.013000000000000001, "accumulated_duration_str": "13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.349958, "duration": 0.00611, "duration_str": "6.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 47}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.382647, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 47, "width_percent": 10.615}, {"sql": "select * from `products` where `products`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.394793, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 57.615, "width_percent": 10.538}, {"sql": "select * from `tax_slabs` where `tax_slabs`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.410625, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 68.154, "width_percent": 8.077}, {"sql": "select * from `product_emi_options` where `product_emi_options`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.423362, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 76.231, "width_percent": 8.385}, {"sql": "select * from `product_shipping_fields` where `product_shipping_fields`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.432824, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 84.615, "width_percent": 7.231}, {"sql": "select * from `product_bump_offers` where `product_bump_offers`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 236}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.442131, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:236", "source": "app/Http/Controllers/FinanceController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=236", "ajax": false, "filename": "FinanceController.php", "line": "236"}, "connection": "radhe_same", "start_percent": 91.846, "width_percent": 8.154}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\TaxSlab": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FTaxSlab.php&line=1", "ajax": false, "filename": "TaxSlab.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/plan/get-product/2", "status_code": "<pre class=sf-dump id=sf-dump-1410292916 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1410292916\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1908706076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908706076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1868524376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1868524376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1378586996 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImI3VWtsUy9wVVNEQkVjcFY1VENTWUE9PSIsInZhbHVlIjoiRHZsRlkraXU1ZStqZGdzM0JxQ2NuWjNQaTNYN05ZSDlWZ2xaMlVYNFRuUmtvTzY4N3VZYzhpOEY4YjZ4QVpUTE81YkFvOHdCSWJERFg1WWxEdUdNVzdUZExZN3R4K3puNE5SQVVkeFREQWo5RS9zcWF0LzgzR2J0NWw2NE5QSWx1QTZtK2F3d1BLK1BLWG4zL2xpRXlLRWRWME54RlB0ZzZoeFBBMEljNlRCQnhsTDBMQkR1OGF2Vmw3NHEzUFp2MTFZdnV2KzQwdXpHVUh5RnV0dFpiVzhqaGxtcE1yM09rbHFkY0dHTGtyU3NYb1RuR2tjeFhvWmNtWThzNHRKZTN6UVg5N1RkNXA4Tm9EbFdyTVlUTGZYemJHU1FlWjdDWVBSQlZudit1ZUpPYjVwQVdBYkM4RUJUUTlUZWpqK2RvMTFwc3hPa2tUOXBwaU05ZEhFU21kUXFoUnZrMzVubHN6N2VGKzBVVGE4bEVMaWhyUU04VjFreTBBcE9EcDZYM3RrdTcyNk9QUnNxTzRDRENSOG8vUlIwR3NDWmZKcTFrZU1Cb29Ic3B6bTNxbDBvQ24yTG01SURibXpMSUdVd3U5NmJnRURvQU4za3hSL0RmL244WnJTOXF0eUJPRDY0UzRXMXZhSlpXWXBJQ2pneWJLU2J1TVNDazdpNG5COWYiLCJtYWMiOiI4OTkzOGNjZGY5NDg3YzYxYmIxODFiOTc1ZmNlYjRlY2FlMGQ4MGJlZjFmMDQ1MjhlMmRkNWE0NzllN2U4ZTQyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1oYkZDQzhXVngvZjJyMk9KbE9TZUE9PSIsInZhbHVlIjoiM1RGUHVJZ2NvVFFac2QzOFdkeDhRWXZPSU5pb05McFQyRzArWjAwR08weFN6SmQzbVp3MkZRZk9tdnNDUUNnT0I2U0hJUHppSWtBQW92ZHRWYk5Zb2NCazA3Vk5iYk5LMG1VblZXdmxuejJJT0c4ZGJ2a3B6ZzF4cjJRbjJVYkF5WUpWRG1BQ0ZNQUIrNFFDTDkxcUo3eXp4eENGbTBLVU1CeDRtZkdWU0ZlbTA3eXhJRmZGRTBBSTB6VjlXRXdPVkk5SER0TVA1THZHa0xrTjFraXFPcXpQUTA2MjB3TnM2RFdVTk8xblBEc0I1UUhIeVcyM3E4NTlhNEh1L2ZrN3VuRjJ0S1JkWExBREpvcmFDU1VZb2N0TDlkNjZVS3VZOVNITDVFd1pwK1NrWmpObm9haytrY2JqOTNnRnBycENUYVJMd055MkV6SHNSTWFzZUU1WDh5cFpVSUtsSnMwd1JUemw1dEM3MVJtcno1TlpCVmNrWmdYK3IzcjF4bGNHeXE0aGVUcjZSZnQrbTltZTZudTk5TjRBRFduUGtlKzNDbmlMZ2pGcUV0YnFOck1uL21VQ3NaQUVTYmdHTytKd3lDUTV6aVlNYytMcXpkVHJ5eHFISndVRkxiWHBqZ2pQMWxuaE0wRnd1MnJpU2ZwK3VFSU1UQ2lKQStoTEJPTzgiLCJtYWMiOiJhNGRmMTA4MmIzMzUzN2E1OTEzNGUzMWU0MDNkNmNiNDczNTQwODBhMjVkYmJmYmVjOGIxZWY2MjA5YzUzZTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378586996\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2090654132 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090654132\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1251430192 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:54:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRERGJ0bGs3OWlnSU8yRU5HQXJQTWc9PSIsInZhbHVlIjoiMjNNeC9DRys4UUFSc2FXUU5oUnVCTjMxOHl4MWo5cUIzbnhPWGRrOEVmQXJOZGZvTXM2M2VyaitkdE8vVU9rUEhrQ1grY2Q0bFg2eDR0YTRsTVMyMm05VGUxZzlFL1A2cTZmb2FudVVBbEdTbkxiWnZPdTZ1UGE2a21PcVRGckxRSVMwdXNhK1pZckM0L1dwcFJoRmdNVFZ4MTdxNnA2cEtLUWVxdmNPUlZlNGQwWlRPVGVmclAwTW85VitNWlphNlF4cml0c05HaUc5MTMyY3ZOUk1IUE5IT2xVMmY1UmlFbGU2dHN4aTlLc3VrWHJMVUVIakZlSHo3bFh1M2ZMb3hiOVJ0M3hNb0R6NVlqVXBSelRlUngxK0pHMXljZVF6SzcwbVZVTDhiZ1lEaEFtZGk0SEkwZFNKSHJFZ3V3Q2lyZUpKSXBDVG11VFpENWJ3L3d2RzdKYmx5VUVldjV3VDgxYStHalZnQ0JJQ1dyUXJEQ2dvQzFvVndIRGt6T2ErS0UyM24zekpWbEZ0cnVURGMwNWdCREJObWwwcm5GazNYTXlKQ2NoTm13Z1Z1Wi9IU0V5dG5CU2s1UXc4cWNoSGt6MnVLcnJ2bGROVXVHRkNPRUhsSjJGQjRDWnhzZTBQQ0tURW9tcnZYNUNvYmN3S3p4RU13d2J5QXZYa0UyeHciLCJtYWMiOiJjNDAxZTE0NDEwODQwZmQ0YzNhOTU3ZTgyNzI0YjNiODZjYTEyYTQ1NjZmN2UyYzU2ZmEyMzhiYzg1Mjc2ODRlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:54:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImJXanJ1NjNyaWZlSlpzRFZIdzI4RHc9PSIsInZhbHVlIjoicXlpVHlXejFYdFIvNVh4QnE0elYxV1lyNVdJWVZ4UDY1VVpXSEl2QkZsTDh5VEFhR1IwSXdpWFNMdHVxSHdXU1VNZkVkY0RvS0VFelpFbWtVNCt2QXAyWmVlSFFTSW9pRWM5dmFNMTNqMzhsNUJySWNvQnlJRC9DQk5IK21xQW8xN2dndVJUOVh1RzBGejBtWDRFdlBNcWFYNGx1VWN2d1cvd3JRcTBhWUNneENNbzBIbnpweG9kYlYwVXRoRE5hQWg5MllodEJITGQyb3JXa0lsTUlIdlVUUEZZTFpiNHI5RXNCa0FIK3dGcGxYdmJIMzNSdWttZERzL1dPdU5MUXgyVm5oaTh3dldQZ2xIcm5hTDEvblMyYThOVzNhRlJsU3VNUEhHL2UwVHdqWnYrMjJKRElFM3JNTWx0NFVuNThsTEVldW96SXN1MTJtRnhDYUxPNUV5azk5SmxTYWZqUjErOE9reHdBajVuQ29pZHZlUjRVVzI0RGVrYW8zSVF2dGFhVCtEWG9jL0tDZWZDZHBueUJlN0czTDlaYk5mWjhhWFV0TWNEQkVjTnRKVEd1eTROa29Rbzl6L1plcG9VMzBHSE15N3VMWDZsczMxOG9zakp4dUozMEhyZU9UbDNyWll4ZExtekwvbVhUWStFZzZSdUJSc1ZtcVlMdFhJWFUiLCJtYWMiOiI1Nzc5MDJmNjgzNzQ5NDJlYjdiYmI1MTUxYTQzY2I1NzAzOGFjZGE0MmM1YTUxZjZlYmE4ZGFmYTlmZmJkYjNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:54:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRERGJ0bGs3OWlnSU8yRU5HQXJQTWc9PSIsInZhbHVlIjoiMjNNeC9DRys4UUFSc2FXUU5oUnVCTjMxOHl4MWo5cUIzbnhPWGRrOEVmQXJOZGZvTXM2M2VyaitkdE8vVU9rUEhrQ1grY2Q0bFg2eDR0YTRsTVMyMm05VGUxZzlFL1A2cTZmb2FudVVBbEdTbkxiWnZPdTZ1UGE2a21PcVRGckxRSVMwdXNhK1pZckM0L1dwcFJoRmdNVFZ4MTdxNnA2cEtLUWVxdmNPUlZlNGQwWlRPVGVmclAwTW85VitNWlphNlF4cml0c05HaUc5MTMyY3ZOUk1IUE5IT2xVMmY1UmlFbGU2dHN4aTlLc3VrWHJMVUVIakZlSHo3bFh1M2ZMb3hiOVJ0M3hNb0R6NVlqVXBSelRlUngxK0pHMXljZVF6SzcwbVZVTDhiZ1lEaEFtZGk0SEkwZFNKSHJFZ3V3Q2lyZUpKSXBDVG11VFpENWJ3L3d2RzdKYmx5VUVldjV3VDgxYStHalZnQ0JJQ1dyUXJEQ2dvQzFvVndIRGt6T2ErS0UyM24zekpWbEZ0cnVURGMwNWdCREJObWwwcm5GazNYTXlKQ2NoTm13Z1Z1Wi9IU0V5dG5CU2s1UXc4cWNoSGt6MnVLcnJ2bGROVXVHRkNPRUhsSjJGQjRDWnhzZTBQQ0tURW9tcnZYNUNvYmN3S3p4RU13d2J5QXZYa0UyeHciLCJtYWMiOiJjNDAxZTE0NDEwODQwZmQ0YzNhOTU3ZTgyNzI0YjNiODZjYTEyYTQ1NjZmN2UyYzU2ZmEyMzhiYzg1Mjc2ODRlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:54:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImJXanJ1NjNyaWZlSlpzRFZIdzI4RHc9PSIsInZhbHVlIjoicXlpVHlXejFYdFIvNVh4QnE0elYxV1lyNVdJWVZ4UDY1VVpXSEl2QkZsTDh5VEFhR1IwSXdpWFNMdHVxSHdXU1VNZkVkY0RvS0VFelpFbWtVNCt2QXAyWmVlSFFTSW9pRWM5dmFNMTNqMzhsNUJySWNvQnlJRC9DQk5IK21xQW8xN2dndVJUOVh1RzBGejBtWDRFdlBNcWFYNGx1VWN2d1cvd3JRcTBhWUNneENNbzBIbnpweG9kYlYwVXRoRE5hQWg5MllodEJITGQyb3JXa0lsTUlIdlVUUEZZTFpiNHI5RXNCa0FIK3dGcGxYdmJIMzNSdWttZERzL1dPdU5MUXgyVm5oaTh3dldQZ2xIcm5hTDEvblMyYThOVzNhRlJsU3VNUEhHL2UwVHdqWnYrMjJKRElFM3JNTWx0NFVuNThsTEVldW96SXN1MTJtRnhDYUxPNUV5azk5SmxTYWZqUjErOE9reHdBajVuQ29pZHZlUjRVVzI0RGVrYW8zSVF2dGFhVCtEWG9jL0tDZWZDZHBueUJlN0czTDlaYk5mWjhhWFV0TWNEQkVjTnRKVEd1eTROa29Rbzl6L1plcG9VMzBHSE15N3VMWDZsczMxOG9zakp4dUozMEhyZU9UbDNyWll4ZExtekwvbVhUWStFZzZSdUJSc1ZtcVlMdFhJWFUiLCJtYWMiOiI1Nzc5MDJmNjgzNzQ5NDJlYjdiYmI1MTUxYTQzY2I1NzAzOGFjZGE0MmM1YTUxZjZlYmE4ZGFmYTlmZmJkYjNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:54:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251430192\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1892794277 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892794277\", {\"maxDepth\":0})</script>\n"}}