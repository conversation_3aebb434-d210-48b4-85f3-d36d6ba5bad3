{"__meta": {"id": "Xadf4ce244d90fb1b6dbcbffd6fe79b52", "datetime": "2025-07-31 16:58:52", "utime": **********.655922, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981130.262535, "end": **********.656013, "duration": 2.3934779167175293, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1753981130.262535, "relative_start": 0, "end": **********.454761, "relative_end": **********.454761, "duration": 2.192225933074951, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.454798, "relative_start": 2.***************, "end": **********.656018, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZdtYws8NTVBu8LEscEp6lGT5mSvtUy03Oj5RNhAu", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1563900637 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1563900637\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-337522180 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-337522180\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1826992199 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1826992199\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552263068 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552263068\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1538093050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1538093050\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:58:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxUbStEajJBcDJRNnhjSC91TzY2WFE9PSIsInZhbHVlIjoidlFwNmZ2UWRGWnpBRFZ2Wlh4OHNjODhYeDFSK01iejVKeEgvenhJcUVsSlFSZ3JjNk5FNFNhRDVPVUx5blhZZDBLdFJKU1U0OHExbjcxeVNZY0hPMmVyZ2tteFdNdTV1OE9adHQzTHJxNC9vLzNUZnM3ekg2TnQ1clhvUXk2dkszSEsycUUvbUNFVm1DVEhWMG9vc1pveHJZdmFMSEY0YTV2OW1KWWl2UmhNcUwxUmNmRiswaHBkSHpKWDlTNWlNcjlTVkFscVE5dDhKZ281RU03MjBCbVYvM0dCdkUzclpuekI0RFVTQnpaVGFlZ1dQc0xnMjJJTThLbjVzZ2RicngwM3U1WFZpU2RSNStZaERYSlcrKy95UWYvS1NjMm5vSWZOVU05TnliWWJLZGx3dnN5NTFNb1o5U0dTR3R2Y1BSNHdzWXJPcGRoUXF3VjE4dVpVQ1g0UitleklvUkY3VjIvVHJOYmE5YS9HRFFQWXkvVEhCRFBEWVAwSHhvRzdiVzVOWnVpM3FTSjYwV0ZnQ1FOU2hkbGRlMmQ2QnRtOFN2MW1LZ2lCRTZObjBQNlRwQzhQU2I0WFJjaXRVZjc0UnNxVlIreVZ6VmlhejdJOW14ZXE5VmJ2cFJvM2dlYUZqYnliVkc3VmRHMmJ6eGJicmVpcjExMXdGaU1PcU9zQVAiLCJtYWMiOiI5ZDVmOTA4NGMxYzE2ZDM5Y2U5ZTI5M2YzZTExMzM2ZmMzOGEwNzJlYWU1ZjA2ZDRjODNiZDg5NzNhZGQ2ZDM0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImM0YzAwZmVaUmR1V3p5UklEdk5wckE9PSIsInZhbHVlIjoiMHBicW1ld1pQQVNQY2tmWGxLV0ErZDUrMmRVRXpSM0ExUVVZa3ZpVjdMb1BxeVU4MUlQbStONmd2SWM2ei9zT2FJRnd4eTZVZUsvUDQ3cHcwQlRqZHNPQ2V6d2ZqelkwMXJVVDkwU3MvMFBQUDZnd0RKcEpaYkRVOTkzeHFzVlNKMWtGQ2d3SjQ3MlRXb0JFeTAzWjIrOW1TYkR0cWhuTzdaMHBrN3JManlJRDN5MFhTS1g1ZHRsbnBrRHd1eUtOZTRNcDROT254VU94QTl0NjF3d3Q1ZVVXYWRkaVh6MTRzRzkxUWFvbWl4cjhFMUk2WCtDUWdmRFFteDZtMTVkQStaMEhYL216SGQ3ejE1WWhoMHRLYmhnLzNDNzJTTDJZaDJQd0wrSTZuSTJrbWNqZzdUQ0d2bTAvSDVmcUJCT2NFY1NhSE9BN3locWNxaTBNSW9EZ3hDeTdONHEvOW1HdklhOTJjdnpROHQ4eXFRdWdvRGhsY09PNEcxM0pRR2ZCSkpwZTVkSGo1V2kveFV3TmtBYlpRNTZsTDVBM1FHY0FLa2RFbUVvck03WmpTdEh6RGF3VWtCZ3RFY3BzZXMrSllVNVFnYkdkTEMvb2ZTU2g4Y2ptaTlRVDQ5c0NIYnIzdk1RdHk2OWp3OVJjNlZJRzdHN1ZSTGY0a010NmtjQXgiLCJtYWMiOiI1MDk2MGVlOTNjOTVhMDIwOWNmYjM5MGMyZmYwZjk0NmVlMWY3ZTE0OTY4NmJlYjc2NDc2NmNhNDhlNzUyNTgzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:58:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxUbStEajJBcDJRNnhjSC91TzY2WFE9PSIsInZhbHVlIjoidlFwNmZ2UWRGWnpBRFZ2Wlh4OHNjODhYeDFSK01iejVKeEgvenhJcUVsSlFSZ3JjNk5FNFNhRDVPVUx5blhZZDBLdFJKU1U0OHExbjcxeVNZY0hPMmVyZ2tteFdNdTV1OE9adHQzTHJxNC9vLzNUZnM3ekg2TnQ1clhvUXk2dkszSEsycUUvbUNFVm1DVEhWMG9vc1pveHJZdmFMSEY0YTV2OW1KWWl2UmhNcUwxUmNmRiswaHBkSHpKWDlTNWlNcjlTVkFscVE5dDhKZ281RU03MjBCbVYvM0dCdkUzclpuekI0RFVTQnpaVGFlZ1dQc0xnMjJJTThLbjVzZ2RicngwM3U1WFZpU2RSNStZaERYSlcrKy95UWYvS1NjMm5vSWZOVU05TnliWWJLZGx3dnN5NTFNb1o5U0dTR3R2Y1BSNHdzWXJPcGRoUXF3VjE4dVpVQ1g0UitleklvUkY3VjIvVHJOYmE5YS9HRFFQWXkvVEhCRFBEWVAwSHhvRzdiVzVOWnVpM3FTSjYwV0ZnQ1FOU2hkbGRlMmQ2QnRtOFN2MW1LZ2lCRTZObjBQNlRwQzhQU2I0WFJjaXRVZjc0UnNxVlIreVZ6VmlhejdJOW14ZXE5VmJ2cFJvM2dlYUZqYnliVkc3VmRHMmJ6eGJicmVpcjExMXdGaU1PcU9zQVAiLCJtYWMiOiI5ZDVmOTA4NGMxYzE2ZDM5Y2U5ZTI5M2YzZTExMzM2ZmMzOGEwNzJlYWU1ZjA2ZDRjODNiZDg5NzNhZGQ2ZDM0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImM0YzAwZmVaUmR1V3p5UklEdk5wckE9PSIsInZhbHVlIjoiMHBicW1ld1pQQVNQY2tmWGxLV0ErZDUrMmRVRXpSM0ExUVVZa3ZpVjdMb1BxeVU4MUlQbStONmd2SWM2ei9zT2FJRnd4eTZVZUsvUDQ3cHcwQlRqZHNPQ2V6d2ZqelkwMXJVVDkwU3MvMFBQUDZnd0RKcEpaYkRVOTkzeHFzVlNKMWtGQ2d3SjQ3MlRXb0JFeTAzWjIrOW1TYkR0cWhuTzdaMHBrN3JManlJRDN5MFhTS1g1ZHRsbnBrRHd1eUtOZTRNcDROT254VU94QTl0NjF3d3Q1ZVVXYWRkaVh6MTRzRzkxUWFvbWl4cjhFMUk2WCtDUWdmRFFteDZtMTVkQStaMEhYL216SGQ3ejE1WWhoMHRLYmhnLzNDNzJTTDJZaDJQd0wrSTZuSTJrbWNqZzdUQ0d2bTAvSDVmcUJCT2NFY1NhSE9BN3locWNxaTBNSW9EZ3hDeTdONHEvOW1HdklhOTJjdnpROHQ4eXFRdWdvRGhsY09PNEcxM0pRR2ZCSkpwZTVkSGo1V2kveFV3TmtBYlpRNTZsTDVBM1FHY0FLa2RFbUVvck03WmpTdEh6RGF3VWtCZ3RFY3BzZXMrSllVNVFnYkdkTEMvb2ZTU2g4Y2ptaTlRVDQ5c0NIYnIzdk1RdHk2OWp3OVJjNlZJRzdHN1ZSTGY0a010NmtjQXgiLCJtYWMiOiI1MDk2MGVlOTNjOTVhMDIwOWNmYjM5MGMyZmYwZjk0NmVlMWY3ZTE0OTY4NmJlYjc2NDc2NmNhNDhlNzUyNTgzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:58:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-147583082 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZdtYws8NTVBu8LEscEp6lGT5mSvtUy03Oj5RNhAu</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147583082\", {\"maxDepth\":0})</script>\n"}}