{"__meta": {"id": "Xf950804ee2dfb1f8ac682f3cd8833143", "datetime": "2025-07-31 15:52:29", "utime": **********.910208, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977148.212048, "end": **********.910254, "duration": 1.6982059478759766, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1753977148.212048, "relative_start": 0, "end": **********.476797, "relative_end": **********.476797, "duration": 1.2647490501403809, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.476831, "relative_start": 1.2647829055786133, "end": **********.910259, "relative_end": 5.0067901611328125e-06, "duration": 0.4334280490875244, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44867248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02783, "accumulated_duration_str": "27.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.624496, "duration": 0.02556, "duration_str": "25.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.843}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.661345, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 91.843, "width_percent": 8.157}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1198585967 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1198585967\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1470328367 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iml1Y3pURGFVMXQ4V2ZRZVFzYmYvWmc9PSIsInZhbHVlIjoiLy9TYlFWRjh0NUx2YUlEeDNyWTNWUjJseUl3OVJlTXRVWTJxYktiNE1HYXJSSXUwUFVyYi9KQm9pSmZ5dzQ2VnJaUEtsOE10ckpXaWdvNW5JcFhjN1JPcTc3dUFjSEN3Mk1OWmVNRVk2TnkwUkpNNmdyVjFxWWVqa1RpUnQ0enYzUjZ3OHlaelBRbjRWZkRaKzVkNXQ1WU5yTjd2bjVvRm9SaXRIWlFMQ0ZrK0Rxc0VDcEY5UEEvR1F2YXJCT09vdytObnI1YVZBQmlaL3hENk9LYkVDZklzQWZpU2VpZHRSeDZsZzB5eFBCNUFsQytreEdqRmxjdXlKV1BjOU9PZkhDdU5pQjVnd2xUZHdsQnRBbVQ3dkphM29Sejh0OEszdEtvMmpuaXdxM3lPVlRGWUMzc3JheVAvbXRjRmhNRVhwWUsxTTB3ZG01ZmxCQzA5NnJRaWlRcW8xMXRzeDNuaTZJWFlzZ1o4bGEybUxuSmxiQ0NvQk1IVFJCNHRqM2YxcG5YNHlzcVJvTE94R1JzOGtZM1hSeUQ5QVN2OUVvTW9mMnVIUDhPMDhWdmZuc2RHN01LaHE0VlgvSXBuQ1V4TmQvSjlCaXJBRU5lZ2pvY2FCNzVLSGFEaU5IaHVMSnlPYzBkNGRkaGJTa2g1enVzS09lT1hQcUxuWUdKQ1M0SlIiLCJtYWMiOiJkNDhhMGY2N2NkYjY0NWNiY2M5YTJiYjY3YzhmZDg3YmQyZWE1ODA0MmJlYmY5NjhkZmU1NGU1NWU2MDUzN2Y4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImUvU1J4MGpFU2ZRaW00cGZEL1Z1Ymc9PSIsInZhbHVlIjoiWG5BZG5ndHJOenRlVXZIdG9FcXRETVVwTGs4WDhNZ2xLS0lFQ0ZvVlNITXJQR3dZdjRJMnpZQ2d6WW1xT0pacXUwYnBrQk83NTAvQzNYUi8yQmQ0Ri9sVDlNWVZXaEpSSW9Rak1GczRObWd2dFNsc3VEVndJWExEc041a1VOWmswSGZSZVRndURlZU13aVZtZDRSNXNDdWtIWUszTWQvV2hmWFBnNVZWUUV3RFovM2M3WGNobWd1MGQ0dFJYVE9SeGpUenplWlB2dEJjaitiWTN3a050R3djdjhza285c0FnZmVyVkpGcmhZekx4QSs4NEg1TXp3OVEwbk9ZWk5jZzdHSmMwcnRMYzV1QURwQWEyNUl0QWIwM3JFMVk3cXhmZjNOS0dZaGlPZGU2by9xcUdjRkhabnJjWW9UZ0h3aWZ1bm11a0VwSW1GL2IrQ3JkQjBlQjVndEVXN3o2b2pudTBEUWRWbklKa3l2Y3pHcXV0MW9GOUMxc0tEbGFhZkxDYU15VllMa1IxdGpyc0h2WHJPYnZUKzMrY2ZodXNCYVNoblVyK3o3dU5IbzR6dlcxdEFBOU1LRkgrSTZzNHdjd3JFMUVadXhXVHN3dUFLTkhyWkF1M2tUQ25Nc0Z4NFFrRWxMNEpiSDA0MmV3ZzNwWUlqa2wyZUl1MUl4M0dxdEEiLCJtYWMiOiJlOGU1ZDE4ZTYwMTM3MzgyODU5NTM0MGE1MDAzZWFlNjE5NWQwMWNjNmI1OGE1NmE5YmQ5M2E4YzE2Y2FlZjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470328367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-434368422 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tkY9CRlDbFfp5lomd7oRDJqrNFwJ8jruTsGOoaTE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434368422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1PVlExcDkyQWpOTmI1YkM2c25ZNGc9PSIsInZhbHVlIjoiSVVjTnhCc0tCRkcwQzZQQlJLOCtBREJ2bnBxTGcrRnFSdmNmT1dsSzkyaitwL0JFVEpCZFFBNlNrT3hXem1nZlA0YzdVbnptWk1rcVFmY0FseGdsUEI4QWgvMXZvN2pYZ2xzd2V3R3hCZ0R4dER3VHZZMWI1Ly9YcnVKbkpLaUwrUWF3aWlvUkdsTzBHUXMwdHpJbWNlSXlSS0RDdjc2NDEzcVg1d1MxMXpLblI2TFh0QjVjUHphNGk1TkZnVzV2ZHR2THhMdVFRNzRTR1hFZXVnMlpwQjhDdFNBWHhmN1BuVUsxWHZ1dVVHeC9SY01tQmltYlc0eVc3bzhkY3BQWmxZN01QT3FCZmhkSGZVUE1uTE12dVFCQlltTU1uZUthY0s2L2lWbzIyb09nTGllMjl0cE9SaHBlaEV2aE9jT0daaEtXY1ZDQlk5QXZwK216U2hzazBJV3BWSGd5SG1ETUlKS0dYVCs0RzJSSzJIZGRwWnNHb3QwL2NuS0hybG1hOG5DNnpXVFNJSmZaOVRpUlF1VWdldlpmUis0UGFUSko5Wm4zWXdCV3pjSk5jbzZKaFhmNUlQR2l1QzRMQ1orT0k4MEUxMk9NUGgzU3ROd011QzBTd2xLbXh2K0gzQXRhTytSUUEyL2M5MkZIaGpLQWlTcGRKYTdLc29HQmlRQ2ciLCJtYWMiOiJjNzVlZDRjZDI2Y2JkZTM2ZjcyMzM0MTg0YTIwY2Q5Nzk1NTU4YmUwNGE4NjNjYzRmMjI3YWM3ZjkwZTczYzFkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImgxdlVsNGdkSEJJSmZvMkJrdTZlcGc9PSIsInZhbHVlIjoiWDFWT1VYemowc0EvbWRtUUIwZktmNHhMYTIvZHFZNjFMWlpkczRRM3RlTnBabGxmTExVRnlqaTZyeGxvam5xODl0L2VscW11bFBYSXNLNWVHdTdDd3RZbk9iZ1V5MENuS0dpRGxZQVFDNWs2d3MyU0wrdUlJUmRuaFg5cER2TlhUd0RSdHFrSWxLNlZiendGMVIzZ2ZMaGJyb0gvenBqdzZKdURUQ0ZMQ0dmbnZ2REMrSUFVWU0vcDlLUE4vK2puc0ZZaW03cS9VR0F3UVVucElpYUxqUXpIQ0FhVnJTSjFnck5YNkJtTGJzU2VrT21kNGtBSWwxVEc3eU1HKzd0SG5uRHJqWmEvSTMwN1ZIdEIyY3o1Q3ZmYkhycVNibjg5WmNZb3lCaGdNUnNkQmdkZXdxaDVBNExvMzd4aWRuYThOL3M3VG9DNmloSEVPdjNKK1lBbzhJRWxTVVJIeFpzM2pUWHFnTGo2ZGM5a2ZTWWd4VTVreEtBcWVvUjEzTVR3S1dCQ1NNdGZtYXVnM0FlYTlEVDlNdEJMY09hR0VSMTJBQ1FuK3d5c3ZoMWhWOTFwcVg1YnIzazFHdzJCaFZaSGFlUUJxNWVQZEpIQjdxNDQrOVpORllqUHBGdkYxaExOYmIvMFhPY25ScnJqditwMEZJVWl3aEZHS3NsZjhMRlUiLCJtYWMiOiI2YWMzYTNmZGUyZjYwMmNiNzBmOGU4NzA3NjAxNTdjZTdhMmY0YTg5MTY0MTNkYzVmMDk2ODcyMzAyNTYzYmEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1PVlExcDkyQWpOTmI1YkM2c25ZNGc9PSIsInZhbHVlIjoiSVVjTnhCc0tCRkcwQzZQQlJLOCtBREJ2bnBxTGcrRnFSdmNmT1dsSzkyaitwL0JFVEpCZFFBNlNrT3hXem1nZlA0YzdVbnptWk1rcVFmY0FseGdsUEI4QWgvMXZvN2pYZ2xzd2V3R3hCZ0R4dER3VHZZMWI1Ly9YcnVKbkpLaUwrUWF3aWlvUkdsTzBHUXMwdHpJbWNlSXlSS0RDdjc2NDEzcVg1d1MxMXpLblI2TFh0QjVjUHphNGk1TkZnVzV2ZHR2THhMdVFRNzRTR1hFZXVnMlpwQjhDdFNBWHhmN1BuVUsxWHZ1dVVHeC9SY01tQmltYlc0eVc3bzhkY3BQWmxZN01QT3FCZmhkSGZVUE1uTE12dVFCQlltTU1uZUthY0s2L2lWbzIyb09nTGllMjl0cE9SaHBlaEV2aE9jT0daaEtXY1ZDQlk5QXZwK216U2hzazBJV3BWSGd5SG1ETUlKS0dYVCs0RzJSSzJIZGRwWnNHb3QwL2NuS0hybG1hOG5DNnpXVFNJSmZaOVRpUlF1VWdldlpmUis0UGFUSko5Wm4zWXdCV3pjSk5jbzZKaFhmNUlQR2l1QzRMQ1orT0k4MEUxMk9NUGgzU3ROd011QzBTd2xLbXh2K0gzQXRhTytSUUEyL2M5MkZIaGpLQWlTcGRKYTdLc29HQmlRQ2ciLCJtYWMiOiJjNzVlZDRjZDI2Y2JkZTM2ZjcyMzM0MTg0YTIwY2Q5Nzk1NTU4YmUwNGE4NjNjYzRmMjI3YWM3ZjkwZTczYzFkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImgxdlVsNGdkSEJJSmZvMkJrdTZlcGc9PSIsInZhbHVlIjoiWDFWT1VYemowc0EvbWRtUUIwZktmNHhMYTIvZHFZNjFMWlpkczRRM3RlTnBabGxmTExVRnlqaTZyeGxvam5xODl0L2VscW11bFBYSXNLNWVHdTdDd3RZbk9iZ1V5MENuS0dpRGxZQVFDNWs2d3MyU0wrdUlJUmRuaFg5cER2TlhUd0RSdHFrSWxLNlZiendGMVIzZ2ZMaGJyb0gvenBqdzZKdURUQ0ZMQ0dmbnZ2REMrSUFVWU0vcDlLUE4vK2puc0ZZaW03cS9VR0F3UVVucElpYUxqUXpIQ0FhVnJTSjFnck5YNkJtTGJzU2VrT21kNGtBSWwxVEc3eU1HKzd0SG5uRHJqWmEvSTMwN1ZIdEIyY3o1Q3ZmYkhycVNibjg5WmNZb3lCaGdNUnNkQmdkZXdxaDVBNExvMzd4aWRuYThOL3M3VG9DNmloSEVPdjNKK1lBbzhJRWxTVVJIeFpzM2pUWHFnTGo2ZGM5a2ZTWWd4VTVreEtBcWVvUjEzTVR3S1dCQ1NNdGZtYXVnM0FlYTlEVDlNdEJMY09hR0VSMTJBQ1FuK3d5c3ZoMWhWOTFwcVg1YnIzazFHdzJCaFZaSGFlUUJxNWVQZEpIQjdxNDQrOVpORllqUHBGdkYxaExOYmIvMFhPY25ScnJqditwMEZJVWl3aEZHS3NsZjhMRlUiLCJtYWMiOiI2YWMzYTNmZGUyZjYwMmNiNzBmOGU4NzA3NjAxNTdjZTdhMmY0YTg5MTY0MTNkYzVmMDk2ODcyMzAyNTYzYmEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}