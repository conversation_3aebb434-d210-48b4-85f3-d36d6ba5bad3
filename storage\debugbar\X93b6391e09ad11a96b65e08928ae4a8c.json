{"__meta": {"id": "X93b6391e09ad11a96b65e08928ae4a8c", "datetime": "2025-07-31 17:08:50", "utime": **********.657823, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981727.99766, "end": **********.657873, "duration": 2.660212993621826, "duration_str": "2.66s", "measures": [{"label": "Booting", "start": 1753981727.99766, "relative_start": 0, "end": **********.278573, "relative_end": **********.278573, "duration": 2.2809131145477295, "duration_str": "2.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27861, "relative_start": 2.2809500694274902, "end": **********.657878, "relative_end": 5.0067901611328125e-06, "duration": 0.37926793098449707, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01449, "accumulated_duration_str": "14.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.537885, "duration": 0.007719999999999999, "duration_str": "7.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 53.278}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.594991, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 53.278, "width_percent": 11.18}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.614162, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 64.458, "width_percent": 23.188}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.630655, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 87.647, "width_percent": 12.353}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1319250274 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1319250274\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1220987382 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220987382\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-864079663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-864079663\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-856726287 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikg3VGttdWpocHlNeGhMZytyRFpDbGc9PSIsInZhbHVlIjoiZXJRMDZWYk1SUWp1dVE2V3k2d2p4RFVBNkxYK28wTVZDTzNBdHp2dVBkTDBycUZGWW5YVUFNSmpRM1ZlWTRDdmZpeklqVVRFeXRxM3FrZ1k1eUtZMTU3c0xlaGNhSTBMbm4zMUkydElGOFlCSnJIaXZudEorK2k5b2VXMmlPd25TeDhnbkVaYUFLR25MdllpZlJRZjM5aGxTeXBCUXdWN1FReFUvRWR2TTJqdWhHSWQwSm5lbzhPNkUwVXpGNjR5eDdNNXYyY0hybk5Kdjlod1BpZEprRThBWjluT2lxVWozQ2dxOHVUVHY2U0M2cWVBWkx0TlVuaHFXRGtFV2QrMDY2OGthWVNVNHBZQkZpdFRDdENOcmlOVDJsUEtIL2xMb0EwaHZVOUQ5RGdWQ2hSUC9tb3hYNlliRm1aRXIxaVRMTGdHWEJCQjdFZC9FZXZYWlR2QVFJdTkvbjMvNEMwSkVvVnJjMFEzcDVGTGtzeldtOXluSkpBOHpRQUtrNTRoZUZGN0N0cUVLZXIxQTRqMGpNY2dady9KTkxjazl1VFhGSk9oQzhLYW5GZGw4OGd3SzRZNlNkUHBuUXRLeEV1N1ppYlZmcDd2enlPY0d5VTdWRnRZWU1Tak5kYmtIUERJODlTenJRRlNlcHIwZjRCSXZxUjU2Q0V3M0Uwd3N3WXIiLCJtYWMiOiJlNjliNGQ0ZDQ5MmU5OWYwMjUyODI1OGI1NzFmNGNhMWQxZjM0ODA3NTI1NWE3YjlhODc5MzM0MWFjZWI5NzIxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InNSdGVoRTYzc0dXV2wrVmZYOFQ1Vnc9PSIsInZhbHVlIjoiRURsN3JvbERPb0tnUGJSa3E2OXhLTG5UU0NBSDB1MnUyeTFZeVNvNm9UZEZDMkN6dlptZzd0Q1N5VEdna3h4S1VhRWdZY3M2WVlNUjJhVS9wRmpZcWY0dDJGN1o2Z3RMMXE3V09ZMnYyYUNXR1ZHM3RxTlVZeUdpV0RiRGF3c1lkcFZsNEJkVVQzcGZqTE05eld3LzFEUzl3VHdoVjJYNW1HMnJiampBNzRJNHlxeUZRemNWMlQ1RGpVMjl2S0IyV0kzRm9sQXArcEk5N2E1djJJNW1QakJpN2tpQ2tzNVRKR0k3S2dWc2V6dlZ5c3FGb1YvNUhhQTl0L2NJSjBRTWVFZGdOYjRhZ0hMbEk3Mjd6Y1FZVnc0VjY5dUkwcm9LQVdZa1VYVXVBS3AyTHpYNzlJSDJqUmxhWDYvdkdCeUd3dkRkWlhRR0xzZnk4cVhtZzNOZSswSlI1S1l2VnNLTmUxK2UzVHdneVBMUEhzZUd4VGpmZVNJY29RVUpuYjc0WWhvNHBaYW5QdXN0TmpUVkdoQ0prTnVJcUZLOUkxVzdqMVg2VmtzdUJTZzV5K3RVOEZhSElIWWNROW5wM25yTVhzYnVONG5wSkwyZ2ZhejNTeWtEb0dtUWEyOUVDOFpmTmJpbjVWWkEybXNseTd2V1NLVEl4TGlzNnh5WlVmZHMiLCJtYWMiOiJmYzIzZjdmZjAyYzAzYzIyYjU5NTJlNTI1ZmE4MWVkMGQ4YzRkN2IyMWM4OWNhYTIyYjFmMTA4M2MwOGQwNzMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856726287\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1038012785 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038012785\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1851090473 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:08:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik0yYkl0OG9WbTJ6QnBiMFdoREJNVFE9PSIsInZhbHVlIjoicXFWendSU0hoQUZvVGV6Wmc3RGZYeDNBUFFDaHJQWWlzcUIwTDJTZjRTcVA4L2RKbDBTajU1aURLNGdhMThSaUZ3VXM4YnN5b0tjNWg0b1EzRzc4ckQ2RWJBM25LQXpYZnZCemIvS21NYlZYQkJiTVBtVm42WDRNc0NuL3lDY3c2ZEZFck1QUHdjRVVxN1Q4S2p6YTJVeEtGOWQ4YnloNndIcDlTaUQzZzNMeWpzNnVZcUcwVGx6Q2ZiT1NHb1ZoWjJiRm5abFQ2emJkdFY4QUtrNFdMeVVVVDdkelFPUkZyZXdTZ2psQkdyQjBZUXhjMytpZjdqNHZJb3M3OGN6MWVHb1o0dnRKTExGOXhWNStEcDNJbXBLRzYyd3dNK0NYOXVHbnN2ZjI1UnRnODIwdmxieklGMzFIUW54cXp2aEdGVlZ6b3UxL2FzeGpyd05lVloveHl3NGVSY0Z2bkIyS2VpRy9MOFh2SFpveGx3K09FOGRHdG9aY1VtekRjYTB6N1N3RFBheGRLeEpmcFg1V3YzWGVManFzR0RvdmZTTkZYOXdidUozQWg0TXl2MHJCcGtuKytveXZZaHE4dnJJTEwxTlpRVHNWOXVCcjhheEZZU0VnMGxQUUdWU1FHbTcvMGhVUTV2Mzk4bUtOSXdzdCtJRytGUCsyUHRKNnhBeDEiLCJtYWMiOiIzNDA4N2E4ZDg4ZDkwNTE2NDg0NmU0MTI3MzcxYjg1NDlkN2VkNmZhNzcyMDgyNzI4Y2E2YmNhZmJmOGNkMDNmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:08:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxaYVhQc3l5OXlGOElpc0JyVkxldkE9PSIsInZhbHVlIjoiTDhCY3RaQXl4RmNRVVVGbzBHUGIrRzc2emhHQmRWYzdTdWxoSEV1Z3hkVWVLQWNUaTB4ZlRucFlPRHRhQUtjbW9vc1U2anFGUi8zZXhsd2I2ektpZXcvbzByNytrMzREZUJmT2ttbFhDSzZ2cWRscVR1YjYyQWR3UmZOV0NueEpKZTVhb200V01nSC8yYTd4alJUTUFLdDhYMENNMnlpTDFVTlU4cTIxcWgwQ1o3TkY5MjRUcVFvVWF4c0hCODRhY09RT3A5MmdHSmM5eFp4YTZJdzkzblJJbDRJbzUySHQxTW5OR2R5bHAxL3E4UDlqUmo1TTdxMHM2eTE1UUFRUjJiVHA5cGNzVUMxcEFseUh2R09YV0JDWlRmUGF1c0RmODZJMVpDK0RBdWZUdGYrTmNoa1RZMjNhbUhHUEIvWVltTkdQckJyS1ZRUEN2bnhCVmJjT1VxcHlDT1A0Wml0TzdUcVZDb1o3ckEyM1IyZWhvVGpXM3V2MmRWN1lhZys1aE5hL0o3ZXJBVkFFblNYeldjcmxTRUVTRE5CWldjMGFzbERIRlYzZnJGek9HekJJODduTFhaSG50Mkc2WTBWUFovYnhEdGZUUTl4ZEIzbWtYeStjdjFCdnAvSExkS3lUaEs3OEFmVWExamtnM3FuOUxJREtCNCs2UEw1QlhLTlMiLCJtYWMiOiJmMWYwMDczMjdmYTk1YjJmOWZjYjMyZGQ4ODE1MThkOWYzNjk1ZjczYzY2ZjZjNDE3ODc4M2Y4YjlmYjFhOTlhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:08:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik0yYkl0OG9WbTJ6QnBiMFdoREJNVFE9PSIsInZhbHVlIjoicXFWendSU0hoQUZvVGV6Wmc3RGZYeDNBUFFDaHJQWWlzcUIwTDJTZjRTcVA4L2RKbDBTajU1aURLNGdhMThSaUZ3VXM4YnN5b0tjNWg0b1EzRzc4ckQ2RWJBM25LQXpYZnZCemIvS21NYlZYQkJiTVBtVm42WDRNc0NuL3lDY3c2ZEZFck1QUHdjRVVxN1Q4S2p6YTJVeEtGOWQ4YnloNndIcDlTaUQzZzNMeWpzNnVZcUcwVGx6Q2ZiT1NHb1ZoWjJiRm5abFQ2emJkdFY4QUtrNFdMeVVVVDdkelFPUkZyZXdTZ2psQkdyQjBZUXhjMytpZjdqNHZJb3M3OGN6MWVHb1o0dnRKTExGOXhWNStEcDNJbXBLRzYyd3dNK0NYOXVHbnN2ZjI1UnRnODIwdmxieklGMzFIUW54cXp2aEdGVlZ6b3UxL2FzeGpyd05lVloveHl3NGVSY0Z2bkIyS2VpRy9MOFh2SFpveGx3K09FOGRHdG9aY1VtekRjYTB6N1N3RFBheGRLeEpmcFg1V3YzWGVManFzR0RvdmZTTkZYOXdidUozQWg0TXl2MHJCcGtuKytveXZZaHE4dnJJTEwxTlpRVHNWOXVCcjhheEZZU0VnMGxQUUdWU1FHbTcvMGhVUTV2Mzk4bUtOSXdzdCtJRytGUCsyUHRKNnhBeDEiLCJtYWMiOiIzNDA4N2E4ZDg4ZDkwNTE2NDg0NmU0MTI3MzcxYjg1NDlkN2VkNmZhNzcyMDgyNzI4Y2E2YmNhZmJmOGNkMDNmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:08:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxaYVhQc3l5OXlGOElpc0JyVkxldkE9PSIsInZhbHVlIjoiTDhCY3RaQXl4RmNRVVVGbzBHUGIrRzc2emhHQmRWYzdTdWxoSEV1Z3hkVWVLQWNUaTB4ZlRucFlPRHRhQUtjbW9vc1U2anFGUi8zZXhsd2I2ektpZXcvbzByNytrMzREZUJmT2ttbFhDSzZ2cWRscVR1YjYyQWR3UmZOV0NueEpKZTVhb200V01nSC8yYTd4alJUTUFLdDhYMENNMnlpTDFVTlU4cTIxcWgwQ1o3TkY5MjRUcVFvVWF4c0hCODRhY09RT3A5MmdHSmM5eFp4YTZJdzkzblJJbDRJbzUySHQxTW5OR2R5bHAxL3E4UDlqUmo1TTdxMHM2eTE1UUFRUjJiVHA5cGNzVUMxcEFseUh2R09YV0JDWlRmUGF1c0RmODZJMVpDK0RBdWZUdGYrTmNoa1RZMjNhbUhHUEIvWVltTkdQckJyS1ZRUEN2bnhCVmJjT1VxcHlDT1A0Wml0TzdUcVZDb1o3ckEyM1IyZWhvVGpXM3V2MmRWN1lhZys1aE5hL0o3ZXJBVkFFblNYeldjcmxTRUVTRE5CWldjMGFzbERIRlYzZnJGek9HekJJODduTFhaSG50Mkc2WTBWUFovYnhEdGZUUTl4ZEIzbWtYeStjdjFCdnAvSExkS3lUaEs3OEFmVWExamtnM3FuOUxJREtCNCs2UEw1QlhLTlMiLCJtYWMiOiJmMWYwMDczMjdmYTk1YjJmOWZjYjMyZGQ4ODE1MThkOWYzNjk1ZjczYzY2ZjZjNDE3ODc4M2Y4YjlmYjFhOTlhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:08:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851090473\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2086411825 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086411825\", {\"maxDepth\":0})</script>\n"}}