{"__meta": {"id": "X74f03a97d3424570a62c954e8d49a384", "datetime": "2025-07-31 15:59:14", "utime": **********.854717, "method": "POST", "uri": "/finance/plan/store-product", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[15:59:14] LOG.error: Product creation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_by' in 'field list' (Connection: mysql, SQL: insert into `products` (`name`, `nickname`, `is_one_time_only`, `is_free_trial`, `trial_duration_type`, `trial_duration`, `total_trial_price`, `is_subscription`, `is_cancelable`, `billing_every`, `billing_cycle_type`, `billing_cycle_limit`, `product_price`, `striked_price`, `downpayment`, `show_shipping_field`, `require_shipping_field`, `payment_gateway`, `skip_gst_form`, `hsn_sac_no`, `redirect_url`, `product_description`, `invoice_footer_description`, `tax_slab_id`, `is_active`, `created_by`, `product_image`, `updated_at`, `created_at`) values (AI SLOW BOT MAX, TAINI AI, 1, 1, month, 6, 8000.00, 0, 0, 1, forever, ?, 6500.00, 7899.00, 4500, 1, 0, ?, 0, ?, ?, ?, ?, 2, 1, 79, **********_logo-dark.png, 2025-07-31 15:59:14, 2025-07-31 15:59:14))", "message_html": null, "is_string": false, "label": "error", "time": **********.798014, "xdebug_link": null, "collector": "log"}, {"message": "[15:59:14] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/finance\\/plan\\/store-product\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.842934, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753977551.900489, "end": **********.854763, "duration": 2.9542739391326904, "duration_str": "2.95s", "measures": [{"label": "Booting", "start": 1753977551.900489, "relative_start": 0, "end": 1753977553.967882, "relative_end": 1753977553.967882, "duration": 2.0673928260803223, "duration_str": "2.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753977553.967907, "relative_start": 2.067417860031128, "end": **********.854767, "relative_end": 4.0531158447265625e-06, "duration": 0.8868601322174072, "duration_str": "887ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57434176, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST finance/plan/store-product", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@storeProduct", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.plan.store-product", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=77\" onclick=\"\">app/Http/Controllers/FinanceController.php:77-225</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.05881, "accumulated_duration_str": "58.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0732832, "duration": 0.02275, "duration_str": "22.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 38.684}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.13023, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 38.684, "width_percent": 2.466}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 81}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.158359, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 41.149, "width_percent": 2.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 81}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.173543, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 44.04, "width_percent": 2.687}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 81}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.191146, "duration": 0.009720000000000001, "duration_str": "9.72ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 46.727, "width_percent": 16.528}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.301339, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 63.255, "width_percent": 8.638}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.362134, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 71.893, "width_percent": 24.724}, {"sql": "select count(*) as aggregate from `tax_slabs` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.7058861, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 96.616, "width_percent": 1.445}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 130}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.743205, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinanceController.php:130", "source": "app/Http/Controllers/FinanceController.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=130", "ajax": false, "filename": "FinanceController.php", "line": "130"}, "connection": "radhe_same", "start_percent": 98.062, "width_percent": 0}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 218}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.796072, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinanceController.php:218", "source": "app/Http/Controllers/FinanceController.php:218", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=218", "ajax": false, "filename": "FinanceController.php", "line": "218"}, "connection": "radhe_same", "start_percent": 98.062, "width_percent": 0}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.8277278, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.062, "width_percent": 1.938}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2780, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create product & service,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-113118265 data-indent-pad=\"  \"><span class=sf-dump-note>create product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113118265\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.678049, "xdebug_link": null}]}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/plan/store-product", "status_code": "<pre class=sf-dump id=sf-dump-1381415500 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1381415500\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1233636094 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1233636094\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1802048355 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">AI SLOW BOT MAX</span>\"\n  \"<span class=sf-dump-key>nickname</span>\" => \"<span class=sf-dump-str title=\"8 characters\">TAINI AI</span>\"\n  \"<span class=sf-dump-key>trial_duration_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"\n  \"<span class=sf-dump-key>trial_duration</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>trial_price</span>\" => \"<span class=sf-dump-str title=\"7 characters\">8000.00</span>\"\n  \"<span class=sf-dump-key>billed_every</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>billing_cycle_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">forever</span>\"\n  \"<span class=sf-dump-key>billing_cycle_display</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Forever</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"7 characters\">6500.00</span>\"\n  \"<span class=sf-dump-key>striked_price</span>\" => \"<span class=sf-dump-str title=\"7 characters\">7899.00</span>\"\n  \"<span class=sf-dump-key>tax_slab</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>tax_rate</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>emi_options_value</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str>5</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str>6</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str>7</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str>8</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str>9</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>down_payment</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4500</span>\"\n  \"<span class=sf-dump-key>payment_gateway_link</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>custom_gateway_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>hsn_sac_no</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>redirect_url</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>product_description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>invoice_footer_description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>restrict_one_time</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>is_free_trial</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>is_subscription</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_cancellable</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>emi_options</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>show_shipping_field</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>required_shipping_field</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_gateway</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>skip_gst_form</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>pro_image</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#167</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"13 characters\">logo-dark.png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"13 characters\">logo-dark.png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php555B.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php555B.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php555B.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\php555B.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\php555B.tmp</span>\"\n    <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-07-31 15:59:14</span>\n    <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1753977551\">2025-07-31 15:59:11</span>\n    <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1753977551\">2025-07-31 15:59:11</span>\n    <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>99079191802196045</span>\n    <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>25784</span>\n    <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n    <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\php555B.tmp</span>\"\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802048355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">30443</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarypBJCElsogCgoWJsV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImhBTkdxeksycDdJNlFMd2xJRnBWZUE9PSIsInZhbHVlIjoiMlQ0QUhpOXM3SVBQajhQNndPL20rWUlOWGM0VDdWSzJVTjBEZllOS2xQN2lheEx2RncvZnBjc1FxK3hKajJHd1N4cStPcE9HWnJ4SG9tOGRNT29zVHBhc1J4emFHYnIwRmFQdW9qU0QrL2NXUW5TVTd6SHdlOS9xTncrYk8yazJmOXhONURybjQ4UHJXT25ZM1NqcEF5bXdFT0tJOVQ3TFVPQTFDUnRPUDBhM0J6ZWM2eERkZ2FvQWlEOWJjc0dMc0FCSjdERnpFRDhuNng5eGRSTitQL3pJcGFtZW5pRUVXS0hQNm1iTlRvKzFwQ21mVm9wWWdwK21jcnk5OEtOVENiMFIxcW5iRER2Z0IrRWduSkk5cDlzcW9QN2JPTHN2MlpIb1RnbVFNWlAxTUliQ0pxL2xyUjNtbWdtWEE1aHdvK1pqcFRTYWpCYW43bllZUEFDNGdGbW9hSnRnTDRyampKdG96OG1JMWhUYzZuMWt4ckNubExDQXYxYVBkZ1FsTjVIQW1hcklrM0Z6Z3cwUGlNVDlURmNyS1NwdlhJMnM1MkpZNVdrZVAxd3ltcjZYd3JkTDI1L2JtNU90bE51NEtGZHJ4dU9OVmx3c2s3c3RKMm1EK253dzZ2dERnMFloTUxoemNpRlBCaEJRdHc0WVNBRzE3TzlGd2psL1U1SEUiLCJtYWMiOiJmMmI2MzRkNzViOTUzYTk4NzdlZTc0ZmYwNjU4ZWRlYzkyNTcyOGU2OGUwZjQyYmIxODFhMWI4NjIyYjk2NWFlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlJpR05HaGVXcjRIVTRoaEpzc1krV2c9PSIsInZhbHVlIjoib0dwMUtJQnYxTER6UHhjU0l5dCtBWmczWUIxcnhzWkllQXlpYTFjcGM5alhSaWVIZlM2RnBPSXl1ZE1idzN6UzRTWmhMNmhaNmZkSmxPMlRKYTBtQ2JwN3BydnEyd21zc2Z6NUZkWHozM2ZGd1BRVW9xdG02bW11M2grWmIxNkpBNTBvMjQ1ZWdiL2lBVkhMdkNISVhEdS9zcm1PREhqMGV0ZnBlQUlmd1BGNUtoRXNaemhRRzI4d3lrR2JOZmxiM0pvNXZTVlRpaUVHWmZlNCtzdUN0WW9xYW9PMmFEYVl3Z2htWmJ1MVY5ODVhUkVzbWQ5dkFEcFZBUlB5bFFubkJuNzJ0My83ZFpUZk5ORks2ZVNwdUpXeHVMSG10K0RBbnRTbWxROGtta0RucDR3RnU2b1Zab3oyWkdvQ2FEYkVYbTdsNHN2UzBxZ1cvZ0pEcVNrVWVPeTJTYldXZ2NJenhqQU1YWHNZMnhtTTNvWlovTG5RUnhsQTg1WGI2SDBDbzZuUVNoNHBTUmRTSHRJUTN2QjdObVRRaFpQN1M1RVg5Y3N1bXp3OUZhWHVzYmJtRHdxOHFrU3E1b2V4N1dlNDRUV0pwYTZnOW1nY0ZGMWhyOVdneHRZNjFjZFM4OWRmQVJ5MS9LMFVPd3grTXNDOXR4MTlpNFc1V2pVU0NvekoiLCJtYWMiOiIyMzJjNzQ3Nzg3ODU3MjJjZjUzODUwOGNkZDRmMDcwOGE3ZGY2NDllZTQwMDQ2MTZiZTg2ZjNkOTllNDNiYmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1922334850 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:59:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJQUjNpc0x3N1dVOWFYa3Z1UEFwN1E9PSIsInZhbHVlIjoiVWFCVXRvVFlBQmw0bnZoSS9pYVk1S3RPN1dXUVlQTk1PNTRKdURMaFJaUUNURm95WFJzZEc4TDNHSEVjWFF0SkdnSUhIZjY2bHNYZTBUeGxVeFFhcWZhakNkMEducFZLSldZclhZNGV2dXd4WnRUL1BveHZRTWdlbldaRUFzZmhWZ1FxamluYmV4cTdjVDR1TklqODhDTDdTcEwyRzV5MjNpSlVPR3pObytESmFqMSt0T0ZIM0Urc3JEMU9OaEpnckxUMitwVlg3N3pxeEVEd1FqT1ZCMWRMcTJyUTFFUmZlYlFZY2M2cGQrUnQvVWNFNURwQUtQSFkwZGZwV29VQk5JUUkrZzNXdzV0M0hvSmllQUFIeWFtVGZFOU9VMm1LODVpYm81MHdTZ3JobUFWa1BjcEFYOUdRcy9sQzY0Y3lWQWJWL1psTU5SOWp1cVB1UDhDOEZ6MkloTWw4ZlpKSHhiQTFUOXp3c3FKaWthTXRyK21ZM05TdUNuSU5CQ2xmNDZyM0d4S1hITXNpWjFGUjQ1ZTZCMVF1Um5oOGpZQjZRbTNYOFJhSlFPRzFmU1NlNjZmMmVuVlEyQ0NSb3VXS05FSXJWbkIzYXprSkNZbVNTZ0t1NlRoeDZIU1VFdTRxT3lSeXdVTS8wZ29YTXZXZmlhNlZtRTdCb0IydmdhQ2kiLCJtYWMiOiIyNmNkNjMzODIyMmVkOTJiOWUzZGZiZmIwZTE3MmFiNWZiZjQ3ZTgyYTI3ZDhkMWNkMTYzOGVhNTg0ZTFkOTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZjN1hiREZYVDVqSWZYTkMra3FKWmc9PSIsInZhbHVlIjoiZkt3WDJWaCtTZ3MvelV0MVRoaGpFekF6ZDJpSjRHeTVEK29NckhOZ1BXNkxRdDZuMDVQZWFsa2JFNkNJbENKVU0yZDYxenJaVnNnY2ZVeGRXRFZGQitlQXhLNHFqSldLNkhOWGZJSk1jbWhXRCtXU3g1eXhMdmVUVDJIazlSWEMvRVZ2OHhiVm1WcDVycXlGb2lxZVJWWDAxdlRGK25BN0crRGdmQ0lkMFZnNGtyT3MwZXQ5VnltU1dieFdRbzA2REx1Si9CakZFSldyL1dDajk5WkxTTmtjKzFGNVhmYmFDZm4xbmNHbWRVZGRicDdUayt6WTlUYlduZmdpMTg0ZFo2eUtyc2d2K2xDeEhSMEs1K2FDeFloNEVSMDc1OG5EOW0rUDhhM2xLWGk3QjJFMDFWVGZlNFF3NnJ0M3NUVzE3UkJFa1ZodWlaWGdjUFFjMnNXZ0Nqd29oMVczcE5WcmUxckViYzdJUjh4Sy82MXlpS2lwQUk4emFNM2tZTXdPNDltaUNVYzBFZXNoVkpBUnJ3VHoxMVZxQ0tjK0srQlhPUmdXS3VGT2ovWkF2aTZGdTI5QlQ1UjZmUUlHMUFFM05QQVVEYXlCODdsa25PbGFWaXIzejN4UHVxZFp6cmJkUTJxK2l3dE0yMHZ2QS9aOTJ0MSt6QzZFYTNuaFoxR20iLCJtYWMiOiIwY2M5ZjQ4ZjBiNjIzMzZhODAyNmRhM2ZhN2RmZjk5YjA4MGE0MjlkY2ZlMzkzNDdlMWZjYWQ4MTkzM2QxOGJlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJQUjNpc0x3N1dVOWFYa3Z1UEFwN1E9PSIsInZhbHVlIjoiVWFCVXRvVFlBQmw0bnZoSS9pYVk1S3RPN1dXUVlQTk1PNTRKdURMaFJaUUNURm95WFJzZEc4TDNHSEVjWFF0SkdnSUhIZjY2bHNYZTBUeGxVeFFhcWZhakNkMEducFZLSldZclhZNGV2dXd4WnRUL1BveHZRTWdlbldaRUFzZmhWZ1FxamluYmV4cTdjVDR1TklqODhDTDdTcEwyRzV5MjNpSlVPR3pObytESmFqMSt0T0ZIM0Urc3JEMU9OaEpnckxUMitwVlg3N3pxeEVEd1FqT1ZCMWRMcTJyUTFFUmZlYlFZY2M2cGQrUnQvVWNFNURwQUtQSFkwZGZwV29VQk5JUUkrZzNXdzV0M0hvSmllQUFIeWFtVGZFOU9VMm1LODVpYm81MHdTZ3JobUFWa1BjcEFYOUdRcy9sQzY0Y3lWQWJWL1psTU5SOWp1cVB1UDhDOEZ6MkloTWw4ZlpKSHhiQTFUOXp3c3FKaWthTXRyK21ZM05TdUNuSU5CQ2xmNDZyM0d4S1hITXNpWjFGUjQ1ZTZCMVF1Um5oOGpZQjZRbTNYOFJhSlFPRzFmU1NlNjZmMmVuVlEyQ0NSb3VXS05FSXJWbkIzYXprSkNZbVNTZ0t1NlRoeDZIU1VFdTRxT3lSeXdVTS8wZ29YTXZXZmlhNlZtRTdCb0IydmdhQ2kiLCJtYWMiOiIyNmNkNjMzODIyMmVkOTJiOWUzZGZiZmIwZTE3MmFiNWZiZjQ3ZTgyYTI3ZDhkMWNkMTYzOGVhNTg0ZTFkOTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZjN1hiREZYVDVqSWZYTkMra3FKWmc9PSIsInZhbHVlIjoiZkt3WDJWaCtTZ3MvelV0MVRoaGpFekF6ZDJpSjRHeTVEK29NckhOZ1BXNkxRdDZuMDVQZWFsa2JFNkNJbENKVU0yZDYxenJaVnNnY2ZVeGRXRFZGQitlQXhLNHFqSldLNkhOWGZJSk1jbWhXRCtXU3g1eXhMdmVUVDJIazlSWEMvRVZ2OHhiVm1WcDVycXlGb2lxZVJWWDAxdlRGK25BN0crRGdmQ0lkMFZnNGtyT3MwZXQ5VnltU1dieFdRbzA2REx1Si9CakZFSldyL1dDajk5WkxTTmtjKzFGNVhmYmFDZm4xbmNHbWRVZGRicDdUayt6WTlUYlduZmdpMTg0ZFo2eUtyc2d2K2xDeEhSMEs1K2FDeFloNEVSMDc1OG5EOW0rUDhhM2xLWGk3QjJFMDFWVGZlNFF3NnJ0M3NUVzE3UkJFa1ZodWlaWGdjUFFjMnNXZ0Nqd29oMVczcE5WcmUxckViYzdJUjh4Sy82MXlpS2lwQUk4emFNM2tZTXdPNDltaUNVYzBFZXNoVkpBUnJ3VHoxMVZxQ0tjK0srQlhPUmdXS3VGT2ovWkF2aTZGdTI5QlQ1UjZmUUlHMUFFM05QQVVEYXlCODdsa25PbGFWaXIzejN4UHVxZFp6cmJkUTJxK2l3dE0yMHZ2QS9aOTJ0MSt6QzZFYTNuaFoxR20iLCJtYWMiOiIwY2M5ZjQ4ZjBiNjIzMzZhODAyNmRhM2ZhN2RmZjk5YjA4MGE0MjlkY2ZlMzkzNDdlMWZjYWQ4MTkzM2QxOGJlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922334850\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-434626252 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434626252\", {\"maxDepth\":0})</script>\n"}}