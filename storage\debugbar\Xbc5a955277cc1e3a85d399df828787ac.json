{"__meta": {"id": "Xbc5a955277cc1e3a85d399df828787ac", "datetime": "2025-07-31 15:52:11", "utime": **********.12562, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977129.262584, "end": **********.125665, "duration": 1.8630809783935547, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1753977129.262584, "relative_start": 0, "end": 1753977130.980673, "relative_end": 1753977130.980673, "duration": 1.7180891036987305, "duration_str": "1.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753977130.980699, "relative_start": 1.****************, "end": **********.125669, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OclGRtItddnoDm86RHH3HMIojIHBQrpDaBBjCIkR", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-559591471 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-559591471\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-963896077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-963896077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1108876093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1108876093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-258660630 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258660630\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-326308698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-326308698\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-605095135 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhRVCs1dHdsSXhDK2tCcFprbEZwOWc9PSIsInZhbHVlIjoiVkZLWFYxUWs0bkxLQTNNVC9IcmhHN1JKWU4vMi9WTnZMdVVYVmlkUHRFNmFXSUcwUkRicmU2VGdyZTR2UnRGcjJTZVlNbDJ2VW5rM1pVMHBhRnhTSnFCT3lyeTJGT1RiNmQzNkF6WC9ZazdJNjFrd2RFS2NlYlJrVzN5UEN6V3VuQjdNV0VaczVZZ2lVOVpIZ0J4ZVJ5UjAzR0IvUUZkTE9ndGw5eTVpVHpSZnBjUUdiSlNoQm1yc0FxOVFMclBwY01LajRVN1I2dlFmMXNXWkF3Z3U1NXJCTEtIY1VpTEhxcEcwZ0ZjZS9ZdXJjZ1Q2eUxGL2VKM1ZaM1lTREs4Y3F2UExNdjdHcHJzWlZOZVJ5aGR6a2tsY0QyL1hPeW0xcm8zaWVVK3JGaGl4MTFIMmpsdVBmWWdTRlVNSmJFRjFMQ1N6bVBPNkp0emJhdnpDcjNDWXkxbUwyRXRLUGVuNWJWTmg1TFA3Nmdtb2xpT2pjU1hYcVVDNHo3SnRxZENrZHUrbmJNajVZSngzTDJYQ2V1QWlMcVpsOVFXSGpsR29YYXpjakxySmdJM0x3NkgrMFIxMW9jUVlOWWlOV0xYekJ3R3hQRkp3UHlTS0p3QU43MTNNYlZ3TVhkOVBKZ0prejlOelI0bVlaYm1KRm5pWW0vWXR3K3pTS2M5MCtwSTUiLCJtYWMiOiI4Zjc4OTM3YjEyZDdhNWNkNWVkZjBmZmY4YmMyNWI1NDVlZGZkZDdiMjMyN2IyMzYxNDEyNDllZTE1MzAxMzA4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikg4YVdmUk5XM1dTeGE4MzhUSEVHSHc9PSIsInZhbHVlIjoiQzZ4K0h3ZElPZUpzZHRHa25QTHJOcW5GOHJvUEFLTU9ycHRsZmJiWmcvYjd6SVFwR0ZNLys4WDF4OXc3cUdTYUgvVUxENDA5SHpOYzJTYzFHTUNpRG9TK3MrajdOV3h4SER3ejV2QUFkcW1vNTVkYjJNQlR0bXRzTWlOcFphdmZyd3RoWkpWdHkvT1hpa3dwUmF1TXQ2KzdwbDFNRmR6Zlp1cDlEelM5T3doRURUaFU4MTAwT0dCQUt3NDFWQzc3enUzU0R2c3hBeHFEQ3FiOW82bno0Vmp3Y2haeGxhRFdzTEVROEhjemJQZE9JdnRYYU5mdXJOWlhqMW1MNFF3QkN4VU1pZkgzdCtnRWFYVkhQNUxPeHRPeklBMWs4VjI5V1A0UXRHNlB1OHJZUktmM05TRW1pcFBTMzRhZmtRNUdFVnNrdkdxQ0N4TDVoazQyQjJmVEZvL3kxMmJrRlRiSW84UkIvNUpmZVovbkVrL3Y4SXlVQTkrdEVnQmd5YXNRUEZSZk11RHRrL1ZGSzk4M3E2emxrSG9id0FSQlJKVGdRelFLall4bzk1REpFU2JMaW85bFc1U0EzN2Y1Q01LMXN4bnlEdmQzdFppZHF3SFBhS3paenl6RmJvUTRnVVVPSU03Q2YxN2ZFZFNqWDYrM2IyNDZhTTc1T0Z1MHFtamYiLCJtYWMiOiI3MWJiN2Q0ZTI2ZDdmM2M0NDNkMjJlNmUzNWNmYTQ1MDBhMDFiZjRmNTUzNDhkMGQyY2MzZWI2ODcwMTViN2MxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhRVCs1dHdsSXhDK2tCcFprbEZwOWc9PSIsInZhbHVlIjoiVkZLWFYxUWs0bkxLQTNNVC9IcmhHN1JKWU4vMi9WTnZMdVVYVmlkUHRFNmFXSUcwUkRicmU2VGdyZTR2UnRGcjJTZVlNbDJ2VW5rM1pVMHBhRnhTSnFCT3lyeTJGT1RiNmQzNkF6WC9ZazdJNjFrd2RFS2NlYlJrVzN5UEN6V3VuQjdNV0VaczVZZ2lVOVpIZ0J4ZVJ5UjAzR0IvUUZkTE9ndGw5eTVpVHpSZnBjUUdiSlNoQm1yc0FxOVFMclBwY01LajRVN1I2dlFmMXNXWkF3Z3U1NXJCTEtIY1VpTEhxcEcwZ0ZjZS9ZdXJjZ1Q2eUxGL2VKM1ZaM1lTREs4Y3F2UExNdjdHcHJzWlZOZVJ5aGR6a2tsY0QyL1hPeW0xcm8zaWVVK3JGaGl4MTFIMmpsdVBmWWdTRlVNSmJFRjFMQ1N6bVBPNkp0emJhdnpDcjNDWXkxbUwyRXRLUGVuNWJWTmg1TFA3Nmdtb2xpT2pjU1hYcVVDNHo3SnRxZENrZHUrbmJNajVZSngzTDJYQ2V1QWlMcVpsOVFXSGpsR29YYXpjakxySmdJM0x3NkgrMFIxMW9jUVlOWWlOV0xYekJ3R3hQRkp3UHlTS0p3QU43MTNNYlZ3TVhkOVBKZ0prejlOelI0bVlaYm1KRm5pWW0vWXR3K3pTS2M5MCtwSTUiLCJtYWMiOiI4Zjc4OTM3YjEyZDdhNWNkNWVkZjBmZmY4YmMyNWI1NDVlZGZkZDdiMjMyN2IyMzYxNDEyNDllZTE1MzAxMzA4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikg4YVdmUk5XM1dTeGE4MzhUSEVHSHc9PSIsInZhbHVlIjoiQzZ4K0h3ZElPZUpzZHRHa25QTHJOcW5GOHJvUEFLTU9ycHRsZmJiWmcvYjd6SVFwR0ZNLys4WDF4OXc3cUdTYUgvVUxENDA5SHpOYzJTYzFHTUNpRG9TK3MrajdOV3h4SER3ejV2QUFkcW1vNTVkYjJNQlR0bXRzTWlOcFphdmZyd3RoWkpWdHkvT1hpa3dwUmF1TXQ2KzdwbDFNRmR6Zlp1cDlEelM5T3doRURUaFU4MTAwT0dCQUt3NDFWQzc3enUzU0R2c3hBeHFEQ3FiOW82bno0Vmp3Y2haeGxhRFdzTEVROEhjemJQZE9JdnRYYU5mdXJOWlhqMW1MNFF3QkN4VU1pZkgzdCtnRWFYVkhQNUxPeHRPeklBMWs4VjI5V1A0UXRHNlB1OHJZUktmM05TRW1pcFBTMzRhZmtRNUdFVnNrdkdxQ0N4TDVoazQyQjJmVEZvL3kxMmJrRlRiSW84UkIvNUpmZVovbkVrL3Y4SXlVQTkrdEVnQmd5YXNRUEZSZk11RHRrL1ZGSzk4M3E2emxrSG9id0FSQlJKVGdRelFLall4bzk1REpFU2JMaW85bFc1U0EzN2Y1Q01LMXN4bnlEdmQzdFppZHF3SFBhS3paenl6RmJvUTRnVVVPSU03Q2YxN2ZFZFNqWDYrM2IyNDZhTTc1T0Z1MHFtamYiLCJtYWMiOiI3MWJiN2Q0ZTI2ZDdmM2M0NDNkMjJlNmUzNWNmYTQ1MDBhMDFiZjRmNTUzNDhkMGQyY2MzZWI2ODcwMTViN2MxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605095135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-119140366 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OclGRtItddnoDm86RHH3HMIojIHBQrpDaBBjCIkR</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119140366\", {\"maxDepth\":0})</script>\n"}}