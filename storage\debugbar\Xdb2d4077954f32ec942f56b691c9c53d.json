{"__meta": {"id": "Xdb2d4077954f32ec942f56b691c9c53d", "datetime": "2025-07-31 16:25:13", "utime": **********.850672, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979111.234129, "end": **********.850729, "duration": 2.6166000366210938, "duration_str": "2.62s", "measures": [{"label": "Booting", "start": 1753979111.234129, "relative_start": 0, "end": **********.382815, "relative_end": **********.382815, "duration": 2.148685932159424, "duration_str": "2.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.382883, "relative_start": 2.148754119873047, "end": **********.850734, "relative_end": 5.0067901611328125e-06, "duration": 0.467850923538208, "duration_str": "468ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01683, "accumulated_duration_str": "16.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.709121, "duration": 0.01041, "duration_str": "10.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 61.854}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.773248, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 61.854, "width_percent": 11.468}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.792663, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 73.321, "width_percent": 12.121}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809251, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 85.443, "width_percent": 14.557}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-692966087 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-692966087\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-277290392 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277290392\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-773940742 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-773940742\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-76585105 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVWQkgyM2J5ajFlRnpYSlYwOW5JcWc9PSIsInZhbHVlIjoiL2QySFpZSnRab1VwN2Y0bHRZdEJsTXEwUmo4c0ZnUElTcFo0b2JsanFGdkFTY2RwTkc4YUpjeitMbHBkNHAwRC9uZVFGNzRPdWIydDFraXVUR3dhQkpaUjdIem1TZTBnZitTakdSQlJjN2JwZ2ZPMWtnd0xQendFbzE2Z1hScHUzK3dXNW83SGZYT0dBRTUva3o1T3BhamFDMlhCbGFYakRyTWlWOFNSWXpwTDdxRkF1emV6RGZMYW5ZdXRSZTI5akt6YmVteDNLaHRVSDBUbGJNZEpOL1locFdCUS92RUhnd3lCaC94Q0F1TjJSa1FTQjhIV0dqSWtNMjVseHV4dGQ1MUErck43UzUyY1hBb1pIOGNUQml1WXN5ellGMHhyOEVEZy9Nb1Nnd3hiTStYK29xUUF4L1Z6ZnRJZmp1emxZWjM1Kzd5b3VodWxvYWRoTk03NnA1akttUmJGNWtQYnZWbkErVG8yZC9abnc0S3V2Q29Ea2RyNWIwWHFqQ0JQVkRoMGRTZ2Z2dnlqczU0Rm9XQVFqK3pNKzkyTC9jNHV1Nitkc2hOTkhKN0JXNGZiWDRWa1JXRThQNTV5N3F4SFkzUTh2YlNWSXp6THU4bkdwc0FYdDVxalExcW94dnV4cU1wY0pVam0zUnhWVGswM3g0ckFKTDU0OGdOeS9JcmsiLCJtYWMiOiJmOGY5MjkwYTc5Y2U4ZjMyMDJiYzIzY2IzZjNkMzY2NWU5Mzc1YTAwYzJjNTY5YzBhZjE3M2ExZTE1YTJkNzBjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjduOSsraFM1QTJxekpXbWNZakNtMmc9PSIsInZhbHVlIjoiNHc1alBjMmJCM1BPU3dYaVl6OTdoTzg4aHorSjR4TGxvbFhqVHplbGxZVHFoblk3TE5pUk9CVXZvc0g3ZTV2QytNMkFnUGlvQlp0M2MxeEQ4NmZqK1NBS3lueE9UT0wwQmFNbldCT0I2VUNZSHUwYWNDMGUvQ2Q5bElTblZGL0pVQU9zVVFRUkRuKy9oSzZCbmlZVWdZUXU3bS9QWVNZVWFmcVMyUWg1cVp0bVBVN2hnUVplKy9IVHVIbWhoSUxMTnEwdFBVNzhwSUtyN3I2V0JtRy9tQTFQRHdjQXl6OWpnQTFiZGVDQ0U5YnIraWJ5N1lWMW9MN3B2SDg2bjJCRDc1L2ZFL1lKSGU5V0VFRmRqVityc2p4NkMwRXVySkcyUU14d0dQVjFBdnVYVk5zMitJeVIwdEFKRUN3dG5nd1owSHBsUXNrdzBSVkJrOFJOWTk5bm8vOTdwVEM2MEl3Tk51dm9SNlcxT1owczhPY3RsRDZaUlRWN1VUZWU0RG05VWEyMzE4enFEbkU5eXhpaWRFT3hDV0hUaXJ1aFo0ZWV4L1RCOWpPK1NWZzUvb203MVNRRDNDVUtoczBsNVE5bHVSSnBJOU5oTjNtaHcrU2xsSmg3Skg5QU5TV1RTYmhrcTNRaEo4SmVMdVUvaGg0Ujg0WjdVeUxsVXYzMXFBaVUiLCJtYWMiOiJlYjEwZjA2MDVmMTYzNDM3ZTY0ZmNhM2RjYWEwNzNmZDZlYjAxZmU0ZTdkZGVjMzVkOWM1NDgxY2RiNjZkMmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76585105\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-199055078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199055078\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1164967571 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:25:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ0cEZxMGt0TUxZZVo3Z3UzN3N0ZkE9PSIsInZhbHVlIjoiQmRyV0djcjUwUEk3VzBsTEd6enZQVzFwbTBrVExxSys3b2V5UWFvTzRYcUUwOEFnWGxoTTlTRXBrTStXbytJR0FyVEZDeWJweWtFQ1dhUXd1NlQrYW1YdDJqVVAwamRJUk1JaWJQWlBUYjdDVGdiSGFBVnovUHhObUV1bkl5TWIyWmQ5YjlKVElxSWZqSlpabm83UnV5enRBZUI4a0dQdjlDR0M0RmFLMnlFVVI0cThqajBneTNPTWlwWks0V2phak5yQXkxTFNkWkIzOFBabmVadmVuSVd0SmlVTldhM1I2aGdBMTlwRXdQaExQZTE5cUR4bHdsRHdPRzJQV3FXb1V5NVhHK08wRHdZMFA3UFEyZU1PRDVJV2M4N0dndVZEWkpEazRZTldnUlNOaDZQNkFlZUwzV1QycWZqWmtycDArN3V6Z0czZFIrYTI4TzYrREJTemVXZllnSHN0MnlST0grR3Bka2IxejRTY0NFRlFSK1lMcDF1RDhqdk5wZWNTdm1DZ2g1TkFSSWZSY01BckZQaFcybUJ2a2hoU01OblhXN2JjS2hJMlhtbkhlQ1RUV1FIbmtOZ2Z0UVhOMFNzYWI4L2tDNThlcWwyK2owaDFJL09LZ09nQjlJL2xRN05hbU4waVNaT1FwMXNqV1NJb3dVYm5SNUd5cjBnWlFlalQiLCJtYWMiOiJhMmM4NTQwZmI4ZGYwNTYxOTBhZGM5MjBlNWY1NTRkZTVkODU0ZWVjZDJhZWVmYmNlZmFlZTI4Y2E3YTcyMjBiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:25:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImF0cFpYNWhRVHluYXhXQVlIODRDWFE9PSIsInZhbHVlIjoiOXNkTDluamQrUVpRS2xIYzEvcGk1U2FHWm83aWJUOVlUblpuMWJnZTRHQmNlVURZd3MyeEQwaHl4RjJMcHJTb1JodEpOQ3ZpOElrZ2YrYjZGTk5BdEMzV0QwQnByRzNjdWJYM0JhVnM3MzBJZ1hoMHdZcENvVVRlQU9qT1d6S0dJSU1jTU83RUFCTmJMaTlRTGhvVld6dklEKzV0KzlRQUw0ZXc0WXlxc0R0VEdULzBkeHRoelFJQm1lcytaQSt6MFVZNm9PN2JkLy9JZHNkU3FrRnRmZHNwcHR3NEdwWTlZeEhWNVp4TEx2dEpML2dPWXFKenprMEhLSHRMNDZ6RnBsMGxGN3NvUit5L2h5OWYzMUxKVkY0amJSRUxrMTMwaldTbXhGajlrWXdBdmdZTHhVZFRUUXBBejNVV0YyT1d5VEVIbGVWSEJGZEdKNEVHd1BUWXdOZ0pXUFV2dXlJR3BVVkRKTlVld1I2MmVlVitVRTFwbXBvdVpjMERVOFVRZmg0VGhqTWxrc001VTk0MzlEM1p4WlMzZTBmcUlFTzRTQWV0cStEQ1I3UFRSeWVNQ09kNEdHRTFJenZxMzBEYldoUS9CR2lPVW5OOXpDZXFJNzJKL0N0aEthK2s2VUY4RUhscitkWmJjSWNnQmhNM1pPSUNBaEp3T09Ca2NYM28iLCJtYWMiOiJkYmI2YTgyNmE1MGFlNjRiOGE5NmE3NjhlZjFkMDExYjE4ODk4YTBjMmMzYTIzN2ExZmEwYTAzMDlmOTgwMjhjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:25:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ0cEZxMGt0TUxZZVo3Z3UzN3N0ZkE9PSIsInZhbHVlIjoiQmRyV0djcjUwUEk3VzBsTEd6enZQVzFwbTBrVExxSys3b2V5UWFvTzRYcUUwOEFnWGxoTTlTRXBrTStXbytJR0FyVEZDeWJweWtFQ1dhUXd1NlQrYW1YdDJqVVAwamRJUk1JaWJQWlBUYjdDVGdiSGFBVnovUHhObUV1bkl5TWIyWmQ5YjlKVElxSWZqSlpabm83UnV5enRBZUI4a0dQdjlDR0M0RmFLMnlFVVI0cThqajBneTNPTWlwWks0V2phak5yQXkxTFNkWkIzOFBabmVadmVuSVd0SmlVTldhM1I2aGdBMTlwRXdQaExQZTE5cUR4bHdsRHdPRzJQV3FXb1V5NVhHK08wRHdZMFA3UFEyZU1PRDVJV2M4N0dndVZEWkpEazRZTldnUlNOaDZQNkFlZUwzV1QycWZqWmtycDArN3V6Z0czZFIrYTI4TzYrREJTemVXZllnSHN0MnlST0grR3Bka2IxejRTY0NFRlFSK1lMcDF1RDhqdk5wZWNTdm1DZ2g1TkFSSWZSY01BckZQaFcybUJ2a2hoU01OblhXN2JjS2hJMlhtbkhlQ1RUV1FIbmtOZ2Z0UVhOMFNzYWI4L2tDNThlcWwyK2owaDFJL09LZ09nQjlJL2xRN05hbU4waVNaT1FwMXNqV1NJb3dVYm5SNUd5cjBnWlFlalQiLCJtYWMiOiJhMmM4NTQwZmI4ZGYwNTYxOTBhZGM5MjBlNWY1NTRkZTVkODU0ZWVjZDJhZWVmYmNlZmFlZTI4Y2E3YTcyMjBiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:25:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImF0cFpYNWhRVHluYXhXQVlIODRDWFE9PSIsInZhbHVlIjoiOXNkTDluamQrUVpRS2xIYzEvcGk1U2FHWm83aWJUOVlUblpuMWJnZTRHQmNlVURZd3MyeEQwaHl4RjJMcHJTb1JodEpOQ3ZpOElrZ2YrYjZGTk5BdEMzV0QwQnByRzNjdWJYM0JhVnM3MzBJZ1hoMHdZcENvVVRlQU9qT1d6S0dJSU1jTU83RUFCTmJMaTlRTGhvVld6dklEKzV0KzlRQUw0ZXc0WXlxc0R0VEdULzBkeHRoelFJQm1lcytaQSt6MFVZNm9PN2JkLy9JZHNkU3FrRnRmZHNwcHR3NEdwWTlZeEhWNVp4TEx2dEpML2dPWXFKenprMEhLSHRMNDZ6RnBsMGxGN3NvUit5L2h5OWYzMUxKVkY0amJSRUxrMTMwaldTbXhGajlrWXdBdmdZTHhVZFRUUXBBejNVV0YyT1d5VEVIbGVWSEJGZEdKNEVHd1BUWXdOZ0pXUFV2dXlJR3BVVkRKTlVld1I2MmVlVitVRTFwbXBvdVpjMERVOFVRZmg0VGhqTWxrc001VTk0MzlEM1p4WlMzZTBmcUlFTzRTQWV0cStEQ1I3UFRSeWVNQ09kNEdHRTFJenZxMzBEYldoUS9CR2lPVW5OOXpDZXFJNzJKL0N0aEthK2s2VUY4RUhscitkWmJjSWNnQmhNM1pPSUNBaEp3T09Ca2NYM28iLCJtYWMiOiJkYmI2YTgyNmE1MGFlNjRiOGE5NmE3NjhlZjFkMDExYjE4ODk4YTBjMmMzYTIzN2ExZmEwYTAzMDlmOTgwMjhjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:25:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164967571\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-863090490 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863090490\", {\"maxDepth\":0})</script>\n"}}