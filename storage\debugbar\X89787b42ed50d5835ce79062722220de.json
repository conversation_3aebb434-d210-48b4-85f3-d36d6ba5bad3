{"__meta": {"id": "X89787b42ed50d5835ce79062722220de", "datetime": "2025-07-31 16:21:25", "utime": **********.28087, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978883.576493, "end": **********.280929, "duration": 1.7044360637664795, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1753978883.576493, "relative_start": 0, "end": 1753978884.979269, "relative_end": 1753978884.979269, "duration": 1.4027760028839111, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753978884.979305, "relative_start": 1.****************, "end": **********.280937, "relative_end": 7.867813110351562e-06, "duration": 0.****************, "duration_str": "302ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02223, "accumulated_duration_str": "22.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.154577, "duration": 0.02223, "duration_str": "22.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-752631663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-752631663\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1582781269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1582781269\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjBsZVJ4ekgyektDN1lxSWE2b0tpQkE9PSIsInZhbHVlIjoiSHNQTkxJZHB6b3hwRWFtM3BzTXlnbHJ5dXVWOUZxME1QN1pWZWExMjRSa09yRUFLUlFoQzVaVVlCNldnV3VSUWdxbUhLTC9BcForSjFFQ1pnWmZzcWZtSnNMSVFyRS9ndHRYWEFsVXIwNnVjLzJseXlaKzhJL2R2MGxwVHkrR2pENm8yN211T1JZVlNWMHdqeDFwTXhoWVgwWGswOHArUnpzaEF1MGQxTnRCNDFZY1JkTkVkRk1XMlFFQ2IxVXk3WWtMWmpJWi9RSG41eDBTcUJwTXZmYldHYzdKd3d4RnhHZ3FzY3ByS0lmcUF4cUJBK25vUGVTMU5KRnJJTUFMMUFaUlc3VFN3UHFiMU5hdTM2NFkrV0tmQldQdG1jaGoxMFZyeVhQMmNRWVZMK3F0aFlBLzBBZXpYdUcxY29mYzF4Tk5hU3hhM3BXRW1TRWtBVDlReXFMVElJVmg0b3lVbVhXVFBrWEhjSFZtNE94UUFYU1VTOXMrTklmNE5raTAxcVBRci9ES085bzV4VTcvUzZ4YmtMWUJLeFA2bldJUjJjOXp3dlFEL0dtSTJxQm1KODNsWmxkLzlqbC9acHRWZFVJVWUvNHloTmwxbG54ei9jeDh2T1FvM3J6Yk9KUEgxNG9NdGFqWXNQUUZjUGFUVWNNem1ZODJhRUlQSU4vcTAiLCJtYWMiOiI5YTMwZGEwMGRmMWZiZGExNDEwNTlhNDY4Y2FlN2IwNThjMGQ0YTkyY2UxNzM4NjgxYmVhYzczZWZhYTA3MWJjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9GNEVmMlo2eHlOeXRNKzNDMXhscFE9PSIsInZhbHVlIjoiUXJPbVRYWU5wSUwyUXhBMTg0YTR6RzJqWW5laXpBZGRuRWVwN0VwYkF0YlU0MHhRSk1vWTVSNWdCTktOdDJTWHI2cEtGdi9sVE1scjk2ZG5qckVzV2piODJUdXd6c3pmZ3VaMCtZVkRBelFaRDRPeWFJVGRjODhaWFNrY1hLelN5NlFuT0lWZ1dXdGdvdGpLWWp6L2R4MDJyeG5nZUlUSEtsQlFFMGY1azk4QjhoaDB3cEdwNXZBRUdSMnJnU2U3UE8wRytmYlI2emNHVnNiYVh2UXpzTHdxa1d4aWUvY2VvSlJ2VnIrZnJENEhtUUlUMUdiL2tSeWF0NVFVMjhOZGFlaE5pMEZMbFNlTFRYc2xMbk05Q3hhMWgveDBUTmlBWWJOQVpvY2s5MTNDQ1FvVXlQSkk5OFdMQU02ZWpXOU5Fb1VrcFJXZXYzdWZET1BSS25pY1R6d1owZEo4eVhBczNtdkJhUkFGV2tyZkQvaTRpbFBEOERYWnROcTNhekNBblNJTng0QUtwOEJFeGpYd2ZKVytIdnpQbkhyenhJTWwrVmtPRlNVMUZuczBrTmNHOE9COW1MMUtTQk9kbVlGRzJsWDZZOHZPc2p5eXIyWWJHZVFielRvRjVUdnBBOGFXb3E4L2RieE0xSFI0aFEwQ2pmSEx3VkJTZFJHT2prakIiLCJtYWMiOiI0ZmUwOTU5MDNkNDI3NDc0YzYyNDk0MGZhMTU3M2E4ZGUxMzhhNzNmNGM4NzUzNjJkZjQxZTVhNzgwYTU2YzZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:21:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJzMTh2ajV2WmpyUzYyUDNQeEhaVkE9PSIsInZhbHVlIjoibVR0MXV4SXNHT2k1ci9wRTdkRkhOT0ZXd05JNG5IYzF3OCszWFhMak1peE55NGsrN01ENmNBRnJZRlhLRWdCN3RXSEt5RHRzdUdPODBDTTBTSDFNR2JOejBzdERhRFR2VnhrWHhjVnNyWnN0Q0lCM3BQWFFlbTlHV1QvRmpROTZYZEpRYVhLeWFnaFQ4SUVIWktyVDJIQk1yV0lObGlUcDNmanN2NlpMZG1tWDJzbGY3MUcvNGxieldubzdwcGdyYXlDNm5Xc0RVWnB6bm55bkxRZnNsaUN3ZU1rN3lQL0JCTzlLUVd6bXdHRmxVZzhURGtnWHd1aWZzRXRkb0NodkY5Ny9WeVB2VkoxZjI2ZEs4V2hQUlE1cDQ4MmZ2VG5Zb01zc2VhbGtUMDZ2OE13Y1NDS1hFb3JtZnJKTHF5aVo1NlM0SDkvTUtCUStGU085WUNUNGdOSmRIWTFBZGl4SWFqK0RUeXhpZXUxbUhiZys0V2tsT0ZCYUxaVzY0aUFNLzBveXpqTmVHem1hQzFpeVlJc2RYaVllZUVKeFVzOGkrcUp0UGhPejVuaE0vUWdMRnJmNk9uZXE4M08waGNNbTFXM3lUaDJ6amM3VkdWam1oRDhlRXg4OFRsRmRJVk9iQm53UTZCbWVqQ3FqSysyaTFsZy8wYzZGTS9QT1NLSVQiLCJtYWMiOiJkYWZjOTliMTcyZmRjOGRkZDZkYzUwYTczMjMxOGE3M2JmNzhjMzA0MTFmNjIzMzYwYmUwYjY2YzYxNjRhZWE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkpRam9RbjE4Mk1KTFdnNElZSVl3RkE9PSIsInZhbHVlIjoiVTJJMWRJWHRHa3Rna2hwbnprbWRpeXFyTk9nMlNhZlJkMmltcDVNN09HWkxRcVZpKzcxSFZHVEtZcnJ5TXhUaFJKdjN6TFlDbUlNY3ltWE04RXdYRGlVNVZqRWgxRjRDNGdPc090QXp5aU1TNmZjM1NkQTNMQTRIYU1Ua3RDZzlSSVAveG92OUFySzh1L0VpRjZNWWxlNkJsSHJXdG95aG9OT1hqZW13NWwwMDNiKzJ3MmhnZEx4SnlhdVFGSnpTTzg1bDZyR3pJSzFqaVp4YVB5MytXZkZLK1lMQ01BSFNtMHhuTG1JQUNKVmljMytTeXQyZWkwTUlFdFJoN1ZySmUrQXlBUDJPbTkwZmp5MysrTmQ4emt3ek1peTkxbTNLRUVlZGJPaUJEbzFreXZDNEM1VzhRQy9ZenhsQzJ4NlFpb04xQmhGdmNHR1BlNXJob2MwaVRMc1hSSnJJOXhIRy9hcCtwMHFnWnBhSkZ3QjRoMzZtVzUwY29ZMTByMGo3cWpsL1BxdkVqVnlQMnhWZlBxSkVkeTc5TnhjWU45LzlVK0JiY25zQW9TWGoxOGtTQ2FRem43MUxBemRldmNKK2ZBMUtxQlZ3dHMwMHZybE96OStFUGgwRFFwaGo2QUZRem5pRXJMeDVzRm1FcDFDZXFNR2cyUG9qRTFpYkY5YjgiLCJtYWMiOiIyNWM1NjkzNjJhMTE2NjIxODU4NmEyZjAyYzg2YmZkZDc2YWIxOGVmY2ExYjg4NDlkMGExZjUzMTE5NDMzZWZjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:21:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJzMTh2ajV2WmpyUzYyUDNQeEhaVkE9PSIsInZhbHVlIjoibVR0MXV4SXNHT2k1ci9wRTdkRkhOT0ZXd05JNG5IYzF3OCszWFhMak1peE55NGsrN01ENmNBRnJZRlhLRWdCN3RXSEt5RHRzdUdPODBDTTBTSDFNR2JOejBzdERhRFR2VnhrWHhjVnNyWnN0Q0lCM3BQWFFlbTlHV1QvRmpROTZYZEpRYVhLeWFnaFQ4SUVIWktyVDJIQk1yV0lObGlUcDNmanN2NlpMZG1tWDJzbGY3MUcvNGxieldubzdwcGdyYXlDNm5Xc0RVWnB6bm55bkxRZnNsaUN3ZU1rN3lQL0JCTzlLUVd6bXdHRmxVZzhURGtnWHd1aWZzRXRkb0NodkY5Ny9WeVB2VkoxZjI2ZEs4V2hQUlE1cDQ4MmZ2VG5Zb01zc2VhbGtUMDZ2OE13Y1NDS1hFb3JtZnJKTHF5aVo1NlM0SDkvTUtCUStGU085WUNUNGdOSmRIWTFBZGl4SWFqK0RUeXhpZXUxbUhiZys0V2tsT0ZCYUxaVzY0aUFNLzBveXpqTmVHem1hQzFpeVlJc2RYaVllZUVKeFVzOGkrcUp0UGhPejVuaE0vUWdMRnJmNk9uZXE4M08waGNNbTFXM3lUaDJ6amM3VkdWam1oRDhlRXg4OFRsRmRJVk9iQm53UTZCbWVqQ3FqSysyaTFsZy8wYzZGTS9QT1NLSVQiLCJtYWMiOiJkYWZjOTliMTcyZmRjOGRkZDZkYzUwYTczMjMxOGE3M2JmNzhjMzA0MTFmNjIzMzYwYmUwYjY2YzYxNjRhZWE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkpRam9RbjE4Mk1KTFdnNElZSVl3RkE9PSIsInZhbHVlIjoiVTJJMWRJWHRHa3Rna2hwbnprbWRpeXFyTk9nMlNhZlJkMmltcDVNN09HWkxRcVZpKzcxSFZHVEtZcnJ5TXhUaFJKdjN6TFlDbUlNY3ltWE04RXdYRGlVNVZqRWgxRjRDNGdPc090QXp5aU1TNmZjM1NkQTNMQTRIYU1Ua3RDZzlSSVAveG92OUFySzh1L0VpRjZNWWxlNkJsSHJXdG95aG9OT1hqZW13NWwwMDNiKzJ3MmhnZEx4SnlhdVFGSnpTTzg1bDZyR3pJSzFqaVp4YVB5MytXZkZLK1lMQ01BSFNtMHhuTG1JQUNKVmljMytTeXQyZWkwTUlFdFJoN1ZySmUrQXlBUDJPbTkwZmp5MysrTmQ4emt3ek1peTkxbTNLRUVlZGJPaUJEbzFreXZDNEM1VzhRQy9ZenhsQzJ4NlFpb04xQmhGdmNHR1BlNXJob2MwaVRMc1hSSnJJOXhIRy9hcCtwMHFnWnBhSkZ3QjRoMzZtVzUwY29ZMTByMGo3cWpsL1BxdkVqVnlQMnhWZlBxSkVkeTc5TnhjWU45LzlVK0JiY25zQW9TWGoxOGtTQ2FRem43MUxBemRldmNKK2ZBMUtxQlZ3dHMwMHZybE96OStFUGgwRFFwaGo2QUZRem5pRXJMeDVzRm1FcDFDZXFNR2cyUG9qRTFpYkY5YjgiLCJtYWMiOiIyNWM1NjkzNjJhMTE2NjIxODU4NmEyZjAyYzg2YmZkZDc2YWIxOGVmY2ExYjg4NDlkMGExZjUzMTE5NDMzZWZjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:21:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-972026680 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972026680\", {\"maxDepth\":0})</script>\n"}}