{"__meta": {"id": "Xee9d6e3406a63bf6187f90df6d09dcf0", "datetime": "2025-07-31 15:59:58", "utime": **********.295233, "method": "GET", "uri": "/finance/sales/contacts/customer/4", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977596.706759, "end": **********.295268, "duration": 1.5885090827941895, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1753977596.706759, "relative_start": 0, "end": **********.130194, "relative_end": **********.130194, "duration": 1.4234349727630615, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.130245, "relative_start": 1.4234859943389893, "end": **********.295271, "relative_end": 2.86102294921875e-06, "duration": 0.16502594947814941, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013176, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00777, "accumulated_duration_str": "7.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.234956, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 66.667}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.263525, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 66.667, "width_percent": 17.761}, {"sql": "select * from `customers` where `id` = '4' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["4", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.273886, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 84.427, "width_percent": 15.573}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/4", "status_code": "<pre class=sf-dump id=sf-dump-41285134 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-41285134\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1383561184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383561184\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1890754495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1890754495\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-164618792 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjY1S2hocmZHY0kxbTdXZk1YeFozY2c9PSIsInZhbHVlIjoiZXk0b3BMY0pjbFRHa0RFbDhWS2U0dlc0UHJiWCtaWWUwV1VuQlRVWW5wNWlUdXhFQzBuYWJBZWNVdjZhQzVMbDU0Slk0d0dTRUNWc2lWNDhUL2MzN0VhellPWlFYbzVaRzRjRnREN3JDTVNCT2F1K05zV0phOC9DVmI1d3B3QW1sNzR4NkcydS9sajZwSnE2V1VIaXRrU3RQQ1A0ZjFyNGVUbktLdjZnY0Y2clliY0NEVWFoSU9USHBrTytIaUQzMDdqQklaQzQxenk4V0M4RWd4VGd5VUFZMFQzOVVQRDFuRkhValI5c2RRdUZ0MmwxOE9pVDBUdWo5cWorR1RjSm5VdDBRODJwOUtBTXBHQnpaL2F5dGZWdGNzVEkzbjlMNTM0QUdvYVA5WmZvM1JZNkJoWURDRm50cENZTTVOREZzVjdZc1RWL3E3c3RDb29sK2c3MEg2UkZKWlhPVVhjaytpYmZRWmFVbEQvUU9pSDFkMGxCK2VVb2NiSHI5MHFFVktkZXE0a2prRlZac2UrejA0VlBZMnR0MEY3OExra3ArenhsSXRDN2FEeXBlbGtEMkkvMk1ZQ3lEVkt4ckJuWUJBSVZ3UHY5bms5ZmhkbkVKRTduNmgwSncyRTZUNFA4ZFhiNmRod3VhNlhKQjNPN0ZpallsRmFmYzNMUGUxdHoiLCJtYWMiOiIyYzJiZTNhZTc2YWNjY2YyMjc2MjUzMGJmODllMGIzZTBkY2ZjYmU1ZWFmNGI1NDVmMzgwMWVkMDRiMTg0NzQzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjBGWW9hSVpzaUp4cnpISGpRLzNTWmc9PSIsInZhbHVlIjoiNzFNL0xlc0xlM0M5cDlCUzBuMnU3QXMxWmN0WCt4RUk1dldKSmxMRllVMDI2Z3QwenhOejJFYU1Yc3ViUFI2Njl3SmlnZUpmeGtWdE90SGVXWUtRVHQ1aTdBYmlVT3VMUEJIT1RLaFg4NXlOQU1mSW5xd1dQckFqbldXNUJKWkt3NWJEa2EwMkYvMVorOHA0dGQxTUZQUlVvcjJwdGx2QytEdG92Uys2MXBKbzRJUFpNOUVldjJTSHlLcyt0aWZqejNYWVpiUklENlRFQnFEbjRCRUpqY1NWNUNLN1E5eU5SWDgwN3FBbXlyRW0vQWJCdzAvWVJFaUMrc2s4bmNkNGZZNDYza09IL1FDdDVHRkU1VFhEVThkaTQwVElSK1MvbVRvQmV3T3VLVE1IOTI3WHYzRElXTExyWWlNdGdzdUdoUlBjKzlmQmZ5QUNCT2NRMXd2OWYrUVMrZ3g5eFladzJ2S3VjaUYwZ2xDYk40UU9TblA5TGhPZExJUmVRTHBnSERMN2QyYjhCdEloNUxjcmNpNGxaRVAwYVRuUUZIbjMyMFRYOXpENVRBZ3JwV1IvdmlHQit1Q0J4RTdmVnppMHdKeE93Q3JkYmNacWluUjZSQ3BZcVFpTUhZdEJNSThZckhWL3JtaTlQUmJ6a05UK3lGVVJtVlNaM0NGd2VEaU0iLCJtYWMiOiIwOWY0MjlmZmNlZGI1NjkxMTU4N2VmY2JiYTE3ZjlhODk2OWNhNmFkYmJjZmEwMjc2MTIxNGNhMDQxM2Y1ZDMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164618792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1861424044 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:59:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJQdWtjVDNoWDN2RVpWZ1AzL2pNUUE9PSIsInZhbHVlIjoiOC9NclFoUmJqWUZvK3Z2bUFaME1wcUQ5TlZReWlNNDVRdGROYnA5eWN4SG83R0JsSkUrOHV4am9mWlJCbjVDSlArQ28xSEs3WVdwd2MvdlQ1bFFTL2s2MlA4VjNiWkVRNzA1OHR1bEtoRDJsTm1RRitZMXZBNThXNDhCUTE4N2hpQzVKOVltZTRCUWNxM1ZCVFNjUnV5UytVczZuR1ArcU8vYXRJR3RUSHA5dWxST1FJODAyZlNsNDIwSkY3cFpuVTJBVVpZVTdaQlBvQ29sNHZPRFp1eDhsYVpCZm5Ka3hKbnpkbm5JYVpSbHFrYkdEK3ozaEpHQ2VFMWczbklITDVvZ1BsbDRqWWRvK2w3OVZWdGFTYmIyZHB0ME5NbU12Q0YvKzMreEcwZDRkeWtIQmJEZDZnc2lNeStubXlmZWhZUHFOSUNKS1lwMWpLUnREcWY2em5LZmY5NnpRU3dsajYwRndPNEFMUnhjRjQxZFFtbFBqbzRkeXF1TTBBdy9ZcDczUmdOMTc5dzhlV3U2UDlWTWU3eXVmYlg1QWxDS1F3OWhPMXU3dkt6ZzFjS0dqR3N2YTVvZERPenI5T1JSTGFmcDNrdTZHd0dXWmV5WjBnK2h5NnplTllUTS9kNWVRK3lDZmFEZmRJMldySGw3Y0RPdUd2T3pJNjRqdSt3TEUiLCJtYWMiOiIyMjEyMzE4MzBmMThmYzY5MDk0MWQyYjE5NWVjN2MzYTg0NjE5OTJhOGVjNjMwZGFiMDI3ZWFmM2NhMDMwNjJlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNoNmI5bGpKSWY3b2hrWjE5cDBjQ0E9PSIsInZhbHVlIjoiUWRpRThUS3pFQXBZbGI0U1czclZja2hZWkxlL29ZYi8vdGYveGc2WHpHdzZ6b204QWxzUE5KeG1PeTVQV1poTVZMc01yMHdMTk4yN3p3OXdEc3QxRndZQ2hzVllmUTJHM09RVnFJdjdWaHFsb0xxYVQ2bWYwRXdwL1NMT0cwSWRDbUc3N1VwVmhQSmI4dXhYU2hLeGl0bWdiQVBMdWFMWlFyVE82TFJadUtRYmZOMDZRV0xuQVp6T2xHWkVsQXpiam45VUJEQkF4VEhLWHV3dnVMOVJsNVBsdmo0M09BeGhzMXU3T3k1Ni80UFZPcktrS1RoM0lDV2lSS2VTNVpoL3UrYUR6SmN4dW1iUys4aUZ5UzQxUXQyV29hNG1uaExpTG5IRmw3NUYyQXRQcUhWUzJtOHBLRkN6R1AyWlpCM2IwVFQvRGJ4R2E5V1Fjem1WdzdObjkrMzBiWFdXOFRDemx5a2w5aDlZK3BMRHdsZE5OTmtnRTBVMzgzT3hEaWs2NUNFWHY1a2hoTVFBNnBjUXNabm8wMTFMNFhjUzZ0V1lGNXlYTnRuV2pSTGFzdytHMk1YaXdYTkk5LzNYYW1MQlFkcVpza1dCbm8rWTgxdk9yZjBiYzJmUUhzaE9QLzdUQ0ZsdXlxb2puYXNNM2cyV3VLTmMyYkx4U2Y4NGhjc0siLCJtYWMiOiI3YzFhYzZkZDU0MDQ0ZGU4OTMzYzg1Mzc0ZjdjODRlZWIzYWZkYzExNGQxY2M4YWVhNDgyZWVkYTkwOTcyMmQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:59:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJQdWtjVDNoWDN2RVpWZ1AzL2pNUUE9PSIsInZhbHVlIjoiOC9NclFoUmJqWUZvK3Z2bUFaME1wcUQ5TlZReWlNNDVRdGROYnA5eWN4SG83R0JsSkUrOHV4am9mWlJCbjVDSlArQ28xSEs3WVdwd2MvdlQ1bFFTL2s2MlA4VjNiWkVRNzA1OHR1bEtoRDJsTm1RRitZMXZBNThXNDhCUTE4N2hpQzVKOVltZTRCUWNxM1ZCVFNjUnV5UytVczZuR1ArcU8vYXRJR3RUSHA5dWxST1FJODAyZlNsNDIwSkY3cFpuVTJBVVpZVTdaQlBvQ29sNHZPRFp1eDhsYVpCZm5Ka3hKbnpkbm5JYVpSbHFrYkdEK3ozaEpHQ2VFMWczbklITDVvZ1BsbDRqWWRvK2w3OVZWdGFTYmIyZHB0ME5NbU12Q0YvKzMreEcwZDRkeWtIQmJEZDZnc2lNeStubXlmZWhZUHFOSUNKS1lwMWpLUnREcWY2em5LZmY5NnpRU3dsajYwRndPNEFMUnhjRjQxZFFtbFBqbzRkeXF1TTBBdy9ZcDczUmdOMTc5dzhlV3U2UDlWTWU3eXVmYlg1QWxDS1F3OWhPMXU3dkt6ZzFjS0dqR3N2YTVvZERPenI5T1JSTGFmcDNrdTZHd0dXWmV5WjBnK2h5NnplTllUTS9kNWVRK3lDZmFEZmRJMldySGw3Y0RPdUd2T3pJNjRqdSt3TEUiLCJtYWMiOiIyMjEyMzE4MzBmMThmYzY5MDk0MWQyYjE5NWVjN2MzYTg0NjE5OTJhOGVjNjMwZGFiMDI3ZWFmM2NhMDMwNjJlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNoNmI5bGpKSWY3b2hrWjE5cDBjQ0E9PSIsInZhbHVlIjoiUWRpRThUS3pFQXBZbGI0U1czclZja2hZWkxlL29ZYi8vdGYveGc2WHpHdzZ6b204QWxzUE5KeG1PeTVQV1poTVZMc01yMHdMTk4yN3p3OXdEc3QxRndZQ2hzVllmUTJHM09RVnFJdjdWaHFsb0xxYVQ2bWYwRXdwL1NMT0cwSWRDbUc3N1VwVmhQSmI4dXhYU2hLeGl0bWdiQVBMdWFMWlFyVE82TFJadUtRYmZOMDZRV0xuQVp6T2xHWkVsQXpiam45VUJEQkF4VEhLWHV3dnVMOVJsNVBsdmo0M09BeGhzMXU3T3k1Ni80UFZPcktrS1RoM0lDV2lSS2VTNVpoL3UrYUR6SmN4dW1iUys4aUZ5UzQxUXQyV29hNG1uaExpTG5IRmw3NUYyQXRQcUhWUzJtOHBLRkN6R1AyWlpCM2IwVFQvRGJ4R2E5V1Fjem1WdzdObjkrMzBiWFdXOFRDemx5a2w5aDlZK3BMRHdsZE5OTmtnRTBVMzgzT3hEaWs2NUNFWHY1a2hoTVFBNnBjUXNabm8wMTFMNFhjUzZ0V1lGNXlYTnRuV2pSTGFzdytHMk1YaXdYTkk5LzNYYW1MQlFkcVpza1dCbm8rWTgxdk9yZjBiYzJmUUhzaE9QLzdUQ0ZsdXlxb2puYXNNM2cyV3VLTmMyYkx4U2Y4NGhjc0siLCJtYWMiOiI3YzFhYzZkZDU0MDQ0ZGU4OTMzYzg1Mzc0ZjdjODRlZWIzYWZkYzExNGQxY2M4YWVhNDgyZWVkYTkwOTcyMmQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:59:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861424044\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1412990697 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412990697\", {\"maxDepth\":0})</script>\n"}}