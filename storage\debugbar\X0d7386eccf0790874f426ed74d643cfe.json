{"__meta": {"id": "X0d7386eccf0790874f426ed74d643cfe", "datetime": "2025-07-31 16:56:52", "utime": **********.109972, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981008.145895, "end": **********.110063, "duration": 3.964168071746826, "duration_str": "3.96s", "measures": [{"label": "Booting", "start": 1753981008.145895, "relative_start": 0, "end": **********.69614, "relative_end": **********.69614, "duration": 3.5502450466156006, "duration_str": "3.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.696209, "relative_start": 3.550313949584961, "end": **********.110073, "relative_end": 1.0013580322265625e-05, "duration": 0.4138641357421875, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008749999999999999, "accumulated_duration_str": "8.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.966506, "duration": 0.00615, "duration_str": "6.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 70.286}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.013191, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 70.286, "width_percent": 13.029}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0275679, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 83.314, "width_percent": 16.686}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1848716208 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1848716208\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1902981859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902981859\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-686939216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-686939216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-65367981 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imh3aGtvUHpVMjlhaEszSkVzaFV0bUE9PSIsInZhbHVlIjoic0NoUXBBM212YUJJQ3pZcXM2RlhxN1U4K2pWelZTRDliQWtFejN1NVY0MFBCV0NORnF1T25xdFM0MkQzSHdCK3EzYWJKVFh2OU9qdUR0VVpQYzBBeVJLWDFJbStubkNPZ0RNSTlMUmlNN3JaYmE2aVNKTzI0ZWMrT1RQdjBZYW1na0pBd2tiM0VkRzZjN3R0NDQrcDBnRndNYkdqdEQrYnNVWE9GcVRKbHNORjJucHN3TlpsYTVLblJ5NHFQUktHTnpQeVB2U1dWMnQ5YlZhQ3NkNE83RHY5MCtKcWd0R09jNU10TGdzTzdMZi9Lai9TSWJMMkRlYUw4alRWeHhBMXl1N2I2N2ZCaFBFcVpYTkhPYmpaTG9KcEJJdmdkd2dEYkQxTE9SbjlUL3NxOVkraHNDQ0NLeDl1RVVFU2lMTzVVMFY1UWdPRWxNOXZOUEVqdUFYVStKTUtoYzBDWnQ1b01mcS9Lb21ydEpjRWxGMjdIRzlGejZBVWk5ZUJHNUM1RkdvV2h2dW5xVkkzcm5NNE14UVZVWFVXVmdQS09UeFgvUjk5bFBYUFF0QmV2cmdvbldZUFRSYWdKZ05zaFliclZMcEU3QitxdmV1TmpnbCtTbXpqS2ZUZlNZZkFGVGxPcFZma3hrRi9IaEhnaWxrdjdsMm83VnYzTyt4VVI4SE4iLCJtYWMiOiIwMDUwMDNjY2U1ZmU2YmQyODViYTNiODEzMTZhODU5MThhZjZmM2Q0NjdlOTdlNDg3MjRmNzM5ZmNjYTk0YjBhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlZ1d1BSVnpUZlh4SnQzenVBQ2s0OXc9PSIsInZhbHVlIjoiL2haa2F6NXUrc3lER0ZTMlV3RXMzV1VaZTZNZGlzQlJaMisyZVFZc2JxMEhiZFIrUmRnNHR3MnJ5OXR0cHJpdHdwTFdNMWo2VmRQc2wzU21CQlZXUDVUUHNLaWd2eEJVYk5zSVZwYlVoNWRWOUFuRUVwcXgyK3UzU2g5V2RIZk9JWHY5Qkpsd0JqRnRSRHNUQXFYdU5kRWloeWhqYS9ReHBHQlpkR1lSM1JhaUwvMkpERThKWnY5Y1E0NGJ1N294T3BESDRZYlY3UzVVS3ExZTJOc1dzdElVYXVDUEM2ek02bVRmMlZsMDRDWFh3c21Pc3VNWDc0M3lLZ250N0E0Mkg4c21Gc1dhYlNkU2o5MDl5aWhEdHlMSm9FUnZvUGtJUGFsV2l3Y3lDcHRteTh3ZTZ1OUtaZmI4R2kzell5djhLb1pkWkgwNDFyRHhINldEbU8rbm45UDZKeWJicGoxMmpiR0J0WjFTZ1VhMm9GTEVHeDQ1cUU5amhvck9EV2U0TWlGUzUvL0J1K2FYUStwOEVVa0ZsL0hqYVgyUUViT0xnSE1oeVZtck9rdDRaVVlBcmVhMCtpdjhmK0NCV0V4SDJoVWR3VnhqTzFYRTdrSXFRc2JyMFpyNTY5TWxKODZJZ2hsQUpvQ2tHOHRRYlBzanJpcjAyc0wvYXFsSXdiaE0iLCJtYWMiOiI4OGRkMDZjNTJkMmJjYzYwNDM3MTNhNWY5YTE0Yjc4MDBkODQ4YTRhMzEzNWYwYjcxOGI1ZDZmMTNlNDMzNWI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65367981\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1063850714 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063850714\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-651345629 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:56:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE5a2RnYkI0ellLZU9NTmVGT3ZMZkE9PSIsInZhbHVlIjoiZ2NqbmJyOXV3VlJ5T2hDQ0xYbGx4NCtXRmhyZ0YwUmEyekhlZ3EzQTdmZUlSVUIrTlF2ZGZmVDBETnRzZjZPdm4yM1F0N0x5aGhkZUFMaXNnWkpxY2RqVHJXc0taeTRxVnpTWDdDZEJFRXFwOWlSd001WWQ2Tkd2SXZPeC95Q1hzR28zdUlPZ1ZBWGUrRDV1SGp4RTN6T0ZTcVg2S2VndWc3VE90WTFqMCtDQUJLNXUydjlEa2w4MkJLbXlmODhBb0kyRG1GSmlGUmtKUFJuZmIvdDZMUm93TWRaVThkYzZlcVBpb1Voa2l6a2xiLzIvUTNHM1FETmNQcXY4M29Dc0Fua0JmWVhacHVHaDY1WTZrSzNOVkZOcnI5ZXpreEZsSnBjQVRKQTdwVmJGdnZYdjc4MTQrbUpRbkdoTEVBZHp6dTdCcFlFdkNNcDcxNlptK0V2MnM3UlQ0REQ0WnJ1NElrL2VtUkRDTnpZYTF2NGxzWkswZEJ5c3JvUVg4akNRbTk3TzNhS3JBMTJDdytTbllGZzM0UXlPanBnOHNqNU1kVU1mVzQ2VTFENC82c3VFRjRwV2M3aFg5RVN4VS9DU0d5c2UzSnRvU0xRajMyQ1lYR2Zyenc0TnE0Z1Fhb2lIVHlCVmxaZTFHYzJwbW5XamFURFp3cEtlNzlvVzBmd1oiLCJtYWMiOiJkNDBmNjZkZmZmOTA5N2FlMDEwNWU5OTYyYzQ1MTE2YmQzZTk3ZTMxYzZlMTk2YmRiNDZmMGRhZjRjNjUzZmNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:56:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ill3TFBHb09Bbmk3SWhFMld3U3hwWGc9PSIsInZhbHVlIjoiRE9JV25vRE1SWUJGQUhxQ00vL1dDWmJ4cEVUYWpXUUdxVVpDOG0vL0VhODUyTHVOYjZMejc1QUJ1akNLOGZENSt2Y043QUpNMnhmUHZMNjFJZ1FPMjAxWnV5cmU5VEs4OUE3QTFZcHBBbFY5YVhtQzNhVy9UaHlQem1RTkZmYUx4Q28xQnlETHBtcFNWdWJEbG04ZnFVK1U4Qm5BZXJPaTZpcGFSYXpwbFNEYlRaaFRaeTBLZFM4bFlYRWp1M0dPNCttYjhWRnpDYytuQmErNzBmTHloNVJ1MzNSZWVUZFBlcmc0TTBuWUJncGZXV2kxMGtWVXJBQzg3QjJMWlJJalFVZ0RiUE92LzJtdGpmc1A1R1lheDVLYmIrNTdZd1JZOFNuSjA3OWJkaXRRMDIzSkpjbVd3UUhCMEt2R0lOTEtHaU9sWWhVckFuOVdOMGdaNnpQTUF1OU1zeXl3a3N5Y0hXZ3VFZUxQbDliaHJ5TTZmZFo1c2k2cHpUaWtXUHBDT0lIVyt3Zkx1LzBhNFVrb0FJVjF3YllBb2Mvb0llZHFPOEJIV1pOZGo1ZzhzV1QzbTlNTkZ5V0lOdktwNWtoNXdXcjdaWEJ5TWNMcXlqQ3g4d20va1BDTWk0SlNxUWVQUm1SV2U3dmtIa1A3R25LL0NKSDEzb2U5clZPalh4b2UiLCJtYWMiOiI1NWJiMTJjM2JhZjgwOGVmYzViZWFlNzIxYmNkYzBkYzg2Y2UxZGJjN2VhZTFjNGI1NDIwOTkwMjBmYTUzMjQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:56:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE5a2RnYkI0ellLZU9NTmVGT3ZMZkE9PSIsInZhbHVlIjoiZ2NqbmJyOXV3VlJ5T2hDQ0xYbGx4NCtXRmhyZ0YwUmEyekhlZ3EzQTdmZUlSVUIrTlF2ZGZmVDBETnRzZjZPdm4yM1F0N0x5aGhkZUFMaXNnWkpxY2RqVHJXc0taeTRxVnpTWDdDZEJFRXFwOWlSd001WWQ2Tkd2SXZPeC95Q1hzR28zdUlPZ1ZBWGUrRDV1SGp4RTN6T0ZTcVg2S2VndWc3VE90WTFqMCtDQUJLNXUydjlEa2w4MkJLbXlmODhBb0kyRG1GSmlGUmtKUFJuZmIvdDZMUm93TWRaVThkYzZlcVBpb1Voa2l6a2xiLzIvUTNHM1FETmNQcXY4M29Dc0Fua0JmWVhacHVHaDY1WTZrSzNOVkZOcnI5ZXpreEZsSnBjQVRKQTdwVmJGdnZYdjc4MTQrbUpRbkdoTEVBZHp6dTdCcFlFdkNNcDcxNlptK0V2MnM3UlQ0REQ0WnJ1NElrL2VtUkRDTnpZYTF2NGxzWkswZEJ5c3JvUVg4akNRbTk3TzNhS3JBMTJDdytTbllGZzM0UXlPanBnOHNqNU1kVU1mVzQ2VTFENC82c3VFRjRwV2M3aFg5RVN4VS9DU0d5c2UzSnRvU0xRajMyQ1lYR2Zyenc0TnE0Z1Fhb2lIVHlCVmxaZTFHYzJwbW5XamFURFp3cEtlNzlvVzBmd1oiLCJtYWMiOiJkNDBmNjZkZmZmOTA5N2FlMDEwNWU5OTYyYzQ1MTE2YmQzZTk3ZTMxYzZlMTk2YmRiNDZmMGRhZjRjNjUzZmNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:56:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ill3TFBHb09Bbmk3SWhFMld3U3hwWGc9PSIsInZhbHVlIjoiRE9JV25vRE1SWUJGQUhxQ00vL1dDWmJ4cEVUYWpXUUdxVVpDOG0vL0VhODUyTHVOYjZMejc1QUJ1akNLOGZENSt2Y043QUpNMnhmUHZMNjFJZ1FPMjAxWnV5cmU5VEs4OUE3QTFZcHBBbFY5YVhtQzNhVy9UaHlQem1RTkZmYUx4Q28xQnlETHBtcFNWdWJEbG04ZnFVK1U4Qm5BZXJPaTZpcGFSYXpwbFNEYlRaaFRaeTBLZFM4bFlYRWp1M0dPNCttYjhWRnpDYytuQmErNzBmTHloNVJ1MzNSZWVUZFBlcmc0TTBuWUJncGZXV2kxMGtWVXJBQzg3QjJMWlJJalFVZ0RiUE92LzJtdGpmc1A1R1lheDVLYmIrNTdZd1JZOFNuSjA3OWJkaXRRMDIzSkpjbVd3UUhCMEt2R0lOTEtHaU9sWWhVckFuOVdOMGdaNnpQTUF1OU1zeXl3a3N5Y0hXZ3VFZUxQbDliaHJ5TTZmZFo1c2k2cHpUaWtXUHBDT0lIVyt3Zkx1LzBhNFVrb0FJVjF3YllBb2Mvb0llZHFPOEJIV1pOZGo1ZzhzV1QzbTlNTkZ5V0lOdktwNWtoNXdXcjdaWEJ5TWNMcXlqQ3g4d20va1BDTWk0SlNxUWVQUm1SV2U3dmtIa1A3R25LL0NKSDEzb2U5clZPalh4b2UiLCJtYWMiOiI1NWJiMTJjM2JhZjgwOGVmYzViZWFlNzIxYmNkYzBkYzg2Y2UxZGJjN2VhZTFjNGI1NDIwOTkwMjBmYTUzMjQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:56:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651345629\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-983258252 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983258252\", {\"maxDepth\":0})</script>\n"}}