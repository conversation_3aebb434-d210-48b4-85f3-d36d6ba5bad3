{"__meta": {"id": "X43643b1bbf7c2b7be9a5650acc75bc72", "datetime": "2025-07-31 15:52:09", "utime": **********.238294, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977126.977337, "end": **********.238332, "duration": 2.2609951496124268, "duration_str": "2.26s", "measures": [{"label": "Booting", "start": 1753977126.977337, "relative_start": 0, "end": **********.028915, "relative_end": **********.028915, "duration": 2.0515780448913574, "duration_str": "2.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.028961, "relative_start": 2.***************, "end": **********.238335, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KYUn5hJacZDw7evdHYNSWKmWqHtAKU02ygdYXcks", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-1112131924 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1112131924\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1671970995 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671970995\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1291281012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291281012\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1216312188 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216312188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-12133148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12133148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-509498191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdpWElVQU1ITi96Qm03MlZkalphdUE9PSIsInZhbHVlIjoiNmNSNDYrVTNyQXBwUXgza0dBTW5JMXhVbXNlckhXdjNKbmtVUkMyVk4yY3RFNlk2MG92SS8zY1dMRk1OU3BxU2FNRHIxWVMxRFVHNTM3RXhTZkJzdHBNbG1FcThFalh6MlpSaDdxbTFpUEhneUU2aVpsVWNIRGhVT292WlZ3R0I3Z3p2MDNCL2tzc21XRWkwL0hBdG9iSnRzOFQ2a1NGSVk3TjVwZEVReXRGY3RDTXlUTWZXSkUvaG5zWTJYekhGcmZWTVUwd1RDcnoyYW0zRmt6YkV1aEJXN3FhbGYyRUd4cWpzY3dUMFMzNkh4Ykl6eWh3eXVPaUxibHhvU2NRVlZaNmgrNnRQRGFwNGFkVUN6Q1l4NXhlbHhocHViTVpkb3cxWHQ5bVFQMzlaRVhjaTdZRDNzek9EV2krSFhMUFFrQkFyeGhMek1HK2NzS0k5SVZlUHd3R2dkUWZHeGxkejZuem1ZenBaR29WdUZ2UVhLVU9sbVVGc05ENUJMc0ZIV0hpem1YWG5RbU5tanJJNGR4WXBDd1J5M2I5ZFowM3JxRU12WFFTUS85SFZYeVltR0pNYll4aksrWDBrUStDQUZEVG5yaXFLWURBaVd5R21mS0FmWnJDM1ZHT3gxRlBWbWtUczVMb1l3UVB5NFg3STFDOFI4Y1BTaFlpbDAzaSsiLCJtYWMiOiIzYzFmZDE3MTI2YzlhMDVjMzNjMjNkYzI3OTVlMmRiMmQwZDU4YWFhMjY4ZWZmYzcyYjlhNGU1ZmUzODk3MWQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkV5dlV3eTZ0MzljUXNGYUwweE1Ibmc9PSIsInZhbHVlIjoiZ21aYko1c3RMd0FMUVI2TlBpbFp6dG4xN0RYbW9wdXBPWGJEeFpXallGOUwzTWpqdXVjSjV6NFBlV1lJMVdqT2xFQTZBbkRvQkJTUldtcmRuWEtkUkdFanR4SlRzUEJDMmVxSC9EVVNDYmRDcDdLVzRQUUl0Q0daeC94eFNRNEd0SDJobXdlbWo3dDExbEZYYXEvTi8weit0TjR3dmp4aHY0MHV3b1cwWE5IS1NBSUxOWklvNUY1ZFVmT1pPNDdWQkIwcm0yWnF3UFJVSk1wYjBaT3VkN0VtRTRhNTBVRmdZZE9PdUNHQytlRk5wNzJJQzQ3MVFtejB5RGlLZ1VwbW9UYi9IazJLWExBY004c1FENzNGYkZjSGxHdTBtcCtqQm9hcjRSSjJBeXkwNE5kWDc3QVdvcFp1R0U1cy9IZjd1QjZMWFFOTllPY2hidGRIK0tlMzRiZU1YcmZLLzg0QWVDMHlDcmN1TU9ETnI3dU9yM2Q1ank0dlJLSUwvOEp1NzZPNUtVMGJUVjRHM2ZtUlkwSERzdjY4Y0N2U1FMNWI5cXNYaDJQVlBMOXhXeFh0eTEwaGIxU1lrM3hvaG5RN2J2YUFZYlBYaXhVWWZxcDJmbjVtd0JqUWd4Zlk1amNxMkh4bVpmVXNVdEliN0p3VCtTSkVKMUIvUVNTZUhleW4iLCJtYWMiOiI0ODQ3MDFhYTM1MjFiZjZhNGYzMWMyMWQ2ZTZkZjFhZWJjZTdkOGZhZGMzZThlNjFhNzExZjRkMTQyMTk3ZmY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdpWElVQU1ITi96Qm03MlZkalphdUE9PSIsInZhbHVlIjoiNmNSNDYrVTNyQXBwUXgza0dBTW5JMXhVbXNlckhXdjNKbmtVUkMyVk4yY3RFNlk2MG92SS8zY1dMRk1OU3BxU2FNRHIxWVMxRFVHNTM3RXhTZkJzdHBNbG1FcThFalh6MlpSaDdxbTFpUEhneUU2aVpsVWNIRGhVT292WlZ3R0I3Z3p2MDNCL2tzc21XRWkwL0hBdG9iSnRzOFQ2a1NGSVk3TjVwZEVReXRGY3RDTXlUTWZXSkUvaG5zWTJYekhGcmZWTVUwd1RDcnoyYW0zRmt6YkV1aEJXN3FhbGYyRUd4cWpzY3dUMFMzNkh4Ykl6eWh3eXVPaUxibHhvU2NRVlZaNmgrNnRQRGFwNGFkVUN6Q1l4NXhlbHhocHViTVpkb3cxWHQ5bVFQMzlaRVhjaTdZRDNzek9EV2krSFhMUFFrQkFyeGhMek1HK2NzS0k5SVZlUHd3R2dkUWZHeGxkejZuem1ZenBaR29WdUZ2UVhLVU9sbVVGc05ENUJMc0ZIV0hpem1YWG5RbU5tanJJNGR4WXBDd1J5M2I5ZFowM3JxRU12WFFTUS85SFZYeVltR0pNYll4aksrWDBrUStDQUZEVG5yaXFLWURBaVd5R21mS0FmWnJDM1ZHT3gxRlBWbWtUczVMb1l3UVB5NFg3STFDOFI4Y1BTaFlpbDAzaSsiLCJtYWMiOiIzYzFmZDE3MTI2YzlhMDVjMzNjMjNkYzI3OTVlMmRiMmQwZDU4YWFhMjY4ZWZmYzcyYjlhNGU1ZmUzODk3MWQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkV5dlV3eTZ0MzljUXNGYUwweE1Ibmc9PSIsInZhbHVlIjoiZ21aYko1c3RMd0FMUVI2TlBpbFp6dG4xN0RYbW9wdXBPWGJEeFpXallGOUwzTWpqdXVjSjV6NFBlV1lJMVdqT2xFQTZBbkRvQkJTUldtcmRuWEtkUkdFanR4SlRzUEJDMmVxSC9EVVNDYmRDcDdLVzRQUUl0Q0daeC94eFNRNEd0SDJobXdlbWo3dDExbEZYYXEvTi8weit0TjR3dmp4aHY0MHV3b1cwWE5IS1NBSUxOWklvNUY1ZFVmT1pPNDdWQkIwcm0yWnF3UFJVSk1wYjBaT3VkN0VtRTRhNTBVRmdZZE9PdUNHQytlRk5wNzJJQzQ3MVFtejB5RGlLZ1VwbW9UYi9IazJLWExBY004c1FENzNGYkZjSGxHdTBtcCtqQm9hcjRSSjJBeXkwNE5kWDc3QVdvcFp1R0U1cy9IZjd1QjZMWFFOTllPY2hidGRIK0tlMzRiZU1YcmZLLzg0QWVDMHlDcmN1TU9ETnI3dU9yM2Q1ank0dlJLSUwvOEp1NzZPNUtVMGJUVjRHM2ZtUlkwSERzdjY4Y0N2U1FMNWI5cXNYaDJQVlBMOXhXeFh0eTEwaGIxU1lrM3hvaG5RN2J2YUFZYlBYaXhVWWZxcDJmbjVtd0JqUWd4Zlk1amNxMkh4bVpmVXNVdEliN0p3VCtTSkVKMUIvUVNTZUhleW4iLCJtYWMiOiI0ODQ3MDFhYTM1MjFiZjZhNGYzMWMyMWQ2ZTZkZjFhZWJjZTdkOGZhZGMzZThlNjFhNzExZjRkMTQyMTk3ZmY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509498191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-104456228 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KYUn5hJacZDw7evdHYNSWKmWqHtAKU02ygdYXcks</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104456228\", {\"maxDepth\":0})</script>\n"}}