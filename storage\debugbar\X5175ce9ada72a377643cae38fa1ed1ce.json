{"__meta": {"id": "X5175ce9ada72a377643cae38fa1ed1ce", "datetime": "2025-07-31 16:15:40", "utime": **********.311445, "method": "POST", "uri": "/finance/sales/store-subscription", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[16:15:40] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/finance\\/sales\\/store-subscription\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.301453, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753978538.011371, "end": **********.311511, "duration": 2.300140142440796, "duration_str": "2.3s", "measures": [{"label": "Booting", "start": 1753978538.011371, "relative_start": 0, "end": **********.520508, "relative_end": **********.520508, "duration": 1.5091371536254883, "duration_str": "1.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520533, "relative_start": 1.509162187576294, "end": **********.311516, "relative_end": 5.0067901611328125e-06, "duration": 0.7909829616546631, "duration_str": "791ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53501344, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST finance/sales/store-subscription", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@storeSubscription", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.store-subscription", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1287\" onclick=\"\">app/Http/Controllers/FinanceController.php:1287-1403</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02831, "accumulated_duration_str": "28.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.639249, "duration": 0.00684, "duration_str": "6.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 24.161}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.678012, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 24.161, "width_percent": 5.652}, {"sql": "select count(*) as aggregate from `product_services` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.7483668, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 29.813, "width_percent": 5.828}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.08248, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 35.641, "width_percent": 7.559}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.112494, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 43.2, "width_percent": 9.361}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.135134, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 52.561, "width_percent": 8.654}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1556191, "duration": 0.01098, "duration_str": "10.98ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 61.215, "width_percent": 38.785}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/store-subscription", "status_code": "<pre class=sf-dump id=sf-dump-1919700716 data-indent-pad=\"  \"><span class=sf-dump-num>422</span>\n</pre><script>Sfdump(\"sf-dump-1919700716\", {\"maxDepth\":0})</script>\n", "status_text": "Unprocessable Content", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-803354100 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-803354100\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1295420177 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>customer_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">customer</span>\"\n  \"<span class=sf-dump-key>customer_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>customer_phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">9932313212</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-31</span>\"\n  \"<span class=sf-dump-key>product_price</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">offline</span>\"\n  \"<span class=sf-dump-key>down_payment</span>\" => \"<span class=sf-dump-str title=\"3 characters\">800</span>\"\n  \"<span class=sf-dump-key>discount_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n  \"<span class=sf-dump-key>total_emis</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>billing_cycle</span>\" => \"<span class=sf-dump-str title=\"7 characters\">monthly</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295420177\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1887800020 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1629</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryK1JhOl0Od9kTwwyn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjkyVjZ1Tis3YTVSVTB6bmYzbUEzdHc9PSIsInZhbHVlIjoibnl1UXUxL3FCdTJDVlhpcTZIQk15cDg1MS9kVnN5MzhyMVQ0TkN2b1c4WlZLRWd4T00xZW5XcDc2Q2wrTzd2aTVzZWptTEtneWxQZXNkOUEzMlRmdVQvVitqbHRWcEF3a3RwUm1zZnQrSnJTQkpjeXFXT0w4ZzBPWVhuT3V5bXFERVJ6YTJTQUIra1pxbTk5ZGEvdmtYcXduK3Brc2ZibU95N0lCMVRLK2Y3dHk4M3RwTHdJdUc5UUhXU29rRFhWM2pvRHJ2eUtiUHAyQ2VEV25ZNkZoRGI1OE4rTWZiWjA3UmE5NmU2Sit5SHJZbnFON0NxM0UyVHZacEd2cFlQOWlhZ2JQU1orQ0djVjRpNm5ZYUZ3TFRBa2dUUWE2OFplTzNDdzV0c1ZCR2lDazhPcUFuenBIMU1CQ3ZFbHV1Z3R0eXFNNUpJRVNnUEtnZkpJdDExNmNHRWFDMllORGNiZzlwa05UQ0NGaTJwNkdiUUM2RW9sTERzc3ZJRUo0QTZmOXVjOFZQaVlTTnVPSjM0WmNZRTNvbjBtNXU3SW51NUw5a3RJa3F3cHNBZC8yb2pKeUtiY1JIOGNFYTI3RVYxNDNaQmVHTVJHNHFXWFRwMGtQSGNNYkpwcXErTERqT1VjYWJHcUpSMjYrYkJLOUlUMytKYk1xZkkyYmVkYUZmUWYiLCJtYWMiOiIxYjZiNmJkNzI1NDNiYzgxNTcwMDZjZDE2NDIwNTBjZmNkODE5NDhhNGU2YTEzZTc2M2ZhNGVhMTM5YTRmZTI5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkhnWmd4Y2hMN3lYaUtXS0xLRmZCMXc9PSIsInZhbHVlIjoiMGc2dGVURFU3c3crRTBoTDBoSjU3MGMwaXIrSXZlbDhsTThUZWF2NlJQWmF4ZC91Z21NYVJybU9wRlF6ZnphOTNXU3JIekEyczdNb1ZPOXZReVRueGQ3ZGdNejZSdlRyUlNRa3J3bHlRcVdaaFZSZDRkaUkyYWp2K1VDZlVxRkJicXNWVTU2N3BRbnFrbnRlbHdBVmRBWG4vYW1FdDZzTTBDMGt2QjlhTzRxeWRYVXpnNDBhQ3BSRm9mSnFPK0l3UjFzSnVWaGJIQ1p5S3hBZzVPY1JJL0F5cG90YVE0MC9CeE9hQnB6WCsvemNocTN6UlpuL3RKWkRIQnVTT1ZuTU03UjY3NE11aUErb1ZWbExVYTVSbGYzUHcrZ2lOT2tML20vQVFzRXFpMWliTGVxOFRvam5OeHVoUWF2U2o3L2VVbmRPbDZRQ2ZFdkdPclYzSzNqV0YzejFLUFZMVFdrQy9nOEM2T2k2VWpTTDN2VGxhUGZDMmZBOUh3YkQ0cVgyM05TbitGT2RvOU5sVCt0YjQ0M3ZVNS9LMDJTbTgyNEx4TkZqUjhjTDFUamwwSHFHcXBLL1B1VVhDc20yaXk3bDNoK0tMSWdWY21IdGl4RHROdHVvdFZWRllhejEvUHVxb3hMNWxLSDAybDVMeXE4ZWp3anZOT1pxcnVxWkp6aUgiLCJtYWMiOiI3YWE1MDFjZDlhZWRmYTA1MGRjZjhkYTYwYzY5MWQxNzFmMGU1ZGQ2ZTE0ODMxNWYxNzVmOGEwZjk3ZmU0MmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887800020\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2115125383 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115125383\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2003923898 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:15:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBYYXMwL0dZbzJhVkt2NmRBWHpXMEE9PSIsInZhbHVlIjoiWTJFZjdLZm5sTGRnem5OWC9EYm0yamJFQWpIK2psZmtoMGZ0MlhaRTVSZVNtbm5acTNXWTNHZ2hkTHNkUDZCRzdQMTIrMTUvMjMwdDdCU2V5MGR1TWxFNW1JVkRyYXVHbVMxWGZueEJWekhBejJTcUY1WE8walI1U1FKRCt0Q3ZVWWszcmRMdVJsanZPL1pDMThZdys0dlVLMFZ2aUt3SmtrQjVab0FnVUhRVDJtalYyNytpWklaL05UQlF5SC95RmZpdHVqcmduWWtjY1h4d2VyejNMbUh5MEF5TnJlUWNMZExkb1llMldBYkdmd1FlZnZEdndXL2pMaHdFTXFLdzY0eHZndDJaSUk0NDIvb1ZNYmhVQ081ZDd6QnI0TWxOZjFaeCtCMlNscUYwQm5QVmFFaFRYTlBGcVZWNHVGWlZCWEVSQnBvdnQxdE84K0ZZMnM0S3l2R0hmZ3FaYzFoaW5rak5DMitVbUZ3V2Nwakl3eUhic1lwWVN2Z2dab3dyajZyMWVHdDE3WnVTd1Jkb1BTdzRKcmhlT241VytKdjBLdStLVElQbkQxdUpzOWdIb2hQRkhvc3lCV1dGOGJodktPemlyKzArS3FaTmw1bko2SmdwY09FTDQ5dis0YUlrVytQNFIwK2VwR21xUHZ3TE0xQkFRUWFyU3FMbkRwYkkiLCJtYWMiOiI0ZDI5ZjkxODA5ZGMzMzRlM2VmNDIwNGE2NTEwYzc0MGJlMTA1ZGI2NjI3YTM3MjAyZDlmNzY5MDQwNmE1MTk1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:15:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iks5dXEvZVMxMUpLSC94eUdwaHZHUGc9PSIsInZhbHVlIjoidXBYMFZPclJSM1hVdEM1QVBqNEp4ZnNSaGNYWEtKY1BwOXFFK1hlUnNaOTAwQmJybjBLYUpTL0kwVkxtdnZ2L0lzNGxrT2VlbDY5eFR6QXJEOEMvRFVtRGcrSy9KUDFoeGs5SGt0eCtsbVQzbDRoMXZ0d2Z6bk9nc2JDeVdQTE5scjlRck12R2x1bEhCd3IwRlR3cFJIcUNaNk5EWWw5T2RrbjJjR3NUVWhueWJCdW5peGV4Mms3Tjhka3ZKQXVhVDMvc3YwQjNXMTllWmJ1R1FvS3N0Q0pNejNQQStXYWlkeFlPTjgyTjJmcU5kL3FGOUhxclQwVHFybXZlNFQ1N3puUnMwSTZnYWRoZ0M3c096VzRncmtPL21TTWIxOFpZdHVKaGhpdE9pR1VVQ3RXYjhFek9ad0paQ1A3VXhWVnVGSjM0blBXUWtuM1dxUVNIOVcwMFZwYmR6OTFDL05hbHh2c1F1bEI2aHpIS2wreXZmaGk1ay9BVFBEUkZ5MXNUSk1YNWJuZ0N2bkU1SUcwMituUzFWbk9wRE1SQ21mT2J4cnhjYW56TzBmS2JKRjk3ZnNiNXh6SFRzdXo5Z1NSbDAvdzZWYzJZUkdVbW1JTHU3S2RXWGdoQzhOemRjcStqYWdCZkxReVN6ejRCWWtOWE1NYU5JVW40MWJBV0diWWsiLCJtYWMiOiI3NzNlYjYyOGQ1MzZhODhhMDExZjhlNWM0YjhjNTEyNWQwZDRiODA5MTAyOTk4NDA5MWMxMWFmNThhMDVlNjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:15:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBYYXMwL0dZbzJhVkt2NmRBWHpXMEE9PSIsInZhbHVlIjoiWTJFZjdLZm5sTGRnem5OWC9EYm0yamJFQWpIK2psZmtoMGZ0MlhaRTVSZVNtbm5acTNXWTNHZ2hkTHNkUDZCRzdQMTIrMTUvMjMwdDdCU2V5MGR1TWxFNW1JVkRyYXVHbVMxWGZueEJWekhBejJTcUY1WE8walI1U1FKRCt0Q3ZVWWszcmRMdVJsanZPL1pDMThZdys0dlVLMFZ2aUt3SmtrQjVab0FnVUhRVDJtalYyNytpWklaL05UQlF5SC95RmZpdHVqcmduWWtjY1h4d2VyejNMbUh5MEF5TnJlUWNMZExkb1llMldBYkdmd1FlZnZEdndXL2pMaHdFTXFLdzY0eHZndDJaSUk0NDIvb1ZNYmhVQ081ZDd6QnI0TWxOZjFaeCtCMlNscUYwQm5QVmFFaFRYTlBGcVZWNHVGWlZCWEVSQnBvdnQxdE84K0ZZMnM0S3l2R0hmZ3FaYzFoaW5rak5DMitVbUZ3V2Nwakl3eUhic1lwWVN2Z2dab3dyajZyMWVHdDE3WnVTd1Jkb1BTdzRKcmhlT241VytKdjBLdStLVElQbkQxdUpzOWdIb2hQRkhvc3lCV1dGOGJodktPemlyKzArS3FaTmw1bko2SmdwY09FTDQ5dis0YUlrVytQNFIwK2VwR21xUHZ3TE0xQkFRUWFyU3FMbkRwYkkiLCJtYWMiOiI0ZDI5ZjkxODA5ZGMzMzRlM2VmNDIwNGE2NTEwYzc0MGJlMTA1ZGI2NjI3YTM3MjAyZDlmNzY5MDQwNmE1MTk1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:15:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iks5dXEvZVMxMUpLSC94eUdwaHZHUGc9PSIsInZhbHVlIjoidXBYMFZPclJSM1hVdEM1QVBqNEp4ZnNSaGNYWEtKY1BwOXFFK1hlUnNaOTAwQmJybjBLYUpTL0kwVkxtdnZ2L0lzNGxrT2VlbDY5eFR6QXJEOEMvRFVtRGcrSy9KUDFoeGs5SGt0eCtsbVQzbDRoMXZ0d2Z6bk9nc2JDeVdQTE5scjlRck12R2x1bEhCd3IwRlR3cFJIcUNaNk5EWWw5T2RrbjJjR3NUVWhueWJCdW5peGV4Mms3Tjhka3ZKQXVhVDMvc3YwQjNXMTllWmJ1R1FvS3N0Q0pNejNQQStXYWlkeFlPTjgyTjJmcU5kL3FGOUhxclQwVHFybXZlNFQ1N3puUnMwSTZnYWRoZ0M3c096VzRncmtPL21TTWIxOFpZdHVKaGhpdE9pR1VVQ3RXYjhFek9ad0paQ1A3VXhWVnVGSjM0blBXUWtuM1dxUVNIOVcwMFZwYmR6OTFDL05hbHh2c1F1bEI2aHpIS2wreXZmaGk1ay9BVFBEUkZ5MXNUSk1YNWJuZ0N2bkU1SUcwMituUzFWbk9wRE1SQ21mT2J4cnhjYW56TzBmS2JKRjk3ZnNiNXh6SFRzdXo5Z1NSbDAvdzZWYzJZUkdVbW1JTHU3S2RXWGdoQzhOemRjcStqYWdCZkxReVN6ejRCWWtOWE1NYU5JVW40MWJBV0diWWsiLCJtYWMiOiI3NzNlYjYyOGQ1MzZhODhhMDExZjhlNWM0YjhjNTEyNWQwZDRiODA5MTAyOTk4NDA5MWMxMWFmNThhMDVlNjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:15:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003923898\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1978444844 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978444844\", {\"maxDepth\":0})</script>\n"}}