{"__meta": {"id": "Xf1c70a43d27c9a4dba0a880cfd3a3eb6", "datetime": "2025-07-31 15:56:33", "utime": **********.882467, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977391.938761, "end": **********.88251, "duration": 1.943748950958252, "duration_str": "1.94s", "measures": [{"label": "Booting", "start": 1753977391.938761, "relative_start": 0, "end": **********.763696, "relative_end": **********.763696, "duration": 1.824934959411621, "duration_str": "1.82s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.763728, "relative_start": 1.****************, "end": **********.882514, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Emx1XUHL9KAproTOegZZhnOQynsCZ9v9wpHgOdWK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-874668439 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-874668439\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1253331988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1253331988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2136541657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2136541657\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1057967337 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057967337\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1824152026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1824152026\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-739791999 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:56:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlZL2N3dEZaWmZyd2Zxcm5ZMTdLNVE9PSIsInZhbHVlIjoiWXNCZjBreGxqeUpyeU5lTUM5ejFGaWoxTnlxTExyN1phd2tkWExpMm5ZNTRkcGRiL0VwR29nWENzSDJndTI5eFJkZW5UNElNT3Q5bkRxaEs3eWUwUjBRZC9lRERkZmdGTVZOWlM4OHFhdkplUE1sM3UweGVIN1VqTWFhaVAvczQ5V2VUWmZSSk9WcVVvaVRzekJzeFBIV3dqT0MzcnpQVGFQYVRHL0tWbUZFalRid2pJUjgrZHA1UDRlRnRlajdWNjR6eDY2WmQzNkN4dEJBQjVrVnJmUTl0OGgzSEo1TVllaXVVZlpyWlNabUpYYk9WdmVzSTBqcjczclBLSlFvQkc5aDlMRUpqWnpheWV2RElYVDlIYllTOFpvbTUvZDByTjFRQ1FvQ0lGVVV2a1NHdWttUGVoT2x6MlpUZ0tVNnlkeDJHVjRrUSt3WDhBYjE1dURzZEtXb3JWRXlhdDhTcC8wSDlPYkxwN2R0eTZHR0hpTzl0R2lRYVhjKytDTFk3U0M5Sk4ybzlLd0Q0QmwyZHk3K0k0amFnQ2pBUGFDYVpjSHBiZ3VWaXkreXFha0VxNm5kNDBEd3ZRVzdscWFFMnEzNk93clhYa25kWDRvd0VqRHBodmZDdVF3TThJWDltWUtIOVJYWFVlb01qZ1ZXSlExNnlRczVId0VvbXdBMTciLCJtYWMiOiJlYmFlOTAzM2FmZDI1ODM1YjU4MzExMWVlYjBhYTU4OTE0YTU1ZWFlYWRkMzVlNzhlZWVhZmUzYTUzMjNiZjlmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:56:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlCRS8yNFVYT0tBdHRrZEpGSUpva1E9PSIsInZhbHVlIjoiZjNWSmU2MzBXdXREdmgzODdFMnRLaWlFV2tKbjloUEZCSVZIYmw3eFIwcVpWVnUyZ01xd25iTWJPajIxd0VhSWFJUnFRQ2JTc0pKanZzWVFtaWdaSUZUclRRY2JpbXMzLzNmaEVLQ0VGVnk5azNYYlR2czFSWUs4YnhpckhVMUp5WmhqWEY1cEgvQ29PK1lMN2l6emhwVFQxOVh4QUFDZ2VZT3NVRDlzM0s0d3JoM1JrNWpQdVNGTmZyR01Pa3A0elFvQ0dNMU9CZUp2dlNhd09QbCs3WXJldFYycEFlTFJQUk5NUXRhREhaNTN5YmM3VitTeitWUG5CMzA3ay9GMi9QakpGSXdUVVIwZDRBdnB3YldSclZHbVFBYXNGRXpQV3loNkJRWFN2U3dXbk5vVElIVjA0UW9KMStTbHBNUmtBOFo1OVNHNThJaU0vNzRsRWcrQUs3WFBOWFVid1JlQnFPaVRFQWRydkQ0Z3pTUTZ3STYyUG5zMDJzU1QrSGlBNlBzcE5ITXRJVXhMT1paT001MGVuTG4zMG1Sd2N4TTNnTEJMNGNnMjFqNnljTVBYRHpNYlRJdVVpR1JDa0o0QU9pRW84bThtQUhLb3NyQ0E0VnBxYm1uL2Z0UjhxYm5sSGVHaFRkMm9QakVNTUZQVnBiRFBXM0dIc2tCZGl5Wk4iLCJtYWMiOiI2ZDUwNWZkOWFkMmZlOGY5ZTdkYWQwNTYzNDk4ODc4ZjM0ZDU2NmY4OGE2Y2JhMzVhMzNiN2VhYjk5MzMzNjljIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:56:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlZL2N3dEZaWmZyd2Zxcm5ZMTdLNVE9PSIsInZhbHVlIjoiWXNCZjBreGxqeUpyeU5lTUM5ejFGaWoxTnlxTExyN1phd2tkWExpMm5ZNTRkcGRiL0VwR29nWENzSDJndTI5eFJkZW5UNElNT3Q5bkRxaEs3eWUwUjBRZC9lRERkZmdGTVZOWlM4OHFhdkplUE1sM3UweGVIN1VqTWFhaVAvczQ5V2VUWmZSSk9WcVVvaVRzekJzeFBIV3dqT0MzcnpQVGFQYVRHL0tWbUZFalRid2pJUjgrZHA1UDRlRnRlajdWNjR6eDY2WmQzNkN4dEJBQjVrVnJmUTl0OGgzSEo1TVllaXVVZlpyWlNabUpYYk9WdmVzSTBqcjczclBLSlFvQkc5aDlMRUpqWnpheWV2RElYVDlIYllTOFpvbTUvZDByTjFRQ1FvQ0lGVVV2a1NHdWttUGVoT2x6MlpUZ0tVNnlkeDJHVjRrUSt3WDhBYjE1dURzZEtXb3JWRXlhdDhTcC8wSDlPYkxwN2R0eTZHR0hpTzl0R2lRYVhjKytDTFk3U0M5Sk4ybzlLd0Q0QmwyZHk3K0k0amFnQ2pBUGFDYVpjSHBiZ3VWaXkreXFha0VxNm5kNDBEd3ZRVzdscWFFMnEzNk93clhYa25kWDRvd0VqRHBodmZDdVF3TThJWDltWUtIOVJYWFVlb01qZ1ZXSlExNnlRczVId0VvbXdBMTciLCJtYWMiOiJlYmFlOTAzM2FmZDI1ODM1YjU4MzExMWVlYjBhYTU4OTE0YTU1ZWFlYWRkMzVlNzhlZWVhZmUzYTUzMjNiZjlmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:56:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlCRS8yNFVYT0tBdHRrZEpGSUpva1E9PSIsInZhbHVlIjoiZjNWSmU2MzBXdXREdmgzODdFMnRLaWlFV2tKbjloUEZCSVZIYmw3eFIwcVpWVnUyZ01xd25iTWJPajIxd0VhSWFJUnFRQ2JTc0pKanZzWVFtaWdaSUZUclRRY2JpbXMzLzNmaEVLQ0VGVnk5azNYYlR2czFSWUs4YnhpckhVMUp5WmhqWEY1cEgvQ29PK1lMN2l6emhwVFQxOVh4QUFDZ2VZT3NVRDlzM0s0d3JoM1JrNWpQdVNGTmZyR01Pa3A0elFvQ0dNMU9CZUp2dlNhd09QbCs3WXJldFYycEFlTFJQUk5NUXRhREhaNTN5YmM3VitTeitWUG5CMzA3ay9GMi9QakpGSXdUVVIwZDRBdnB3YldSclZHbVFBYXNGRXpQV3loNkJRWFN2U3dXbk5vVElIVjA0UW9KMStTbHBNUmtBOFo1OVNHNThJaU0vNzRsRWcrQUs3WFBOWFVid1JlQnFPaVRFQWRydkQ0Z3pTUTZ3STYyUG5zMDJzU1QrSGlBNlBzcE5ITXRJVXhMT1paT001MGVuTG4zMG1Sd2N4TTNnTEJMNGNnMjFqNnljTVBYRHpNYlRJdVVpR1JDa0o0QU9pRW84bThtQUhLb3NyQ0E0VnBxYm1uL2Z0UjhxYm5sSGVHaFRkMm9QakVNTUZQVnBiRFBXM0dIc2tCZGl5Wk4iLCJtYWMiOiI2ZDUwNWZkOWFkMmZlOGY5ZTdkYWQwNTYzNDk4ODc4ZjM0ZDU2NmY4OGE2Y2JhMzVhMzNiN2VhYjk5MzMzNjljIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:56:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739791999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-249641284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Emx1XUHL9KAproTOegZZhnOQynsCZ9v9wpHgOdWK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249641284\", {\"maxDepth\":0})</script>\n"}}