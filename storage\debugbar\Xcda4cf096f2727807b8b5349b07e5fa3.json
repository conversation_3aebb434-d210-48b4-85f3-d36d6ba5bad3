{"__meta": {"id": "Xcda4cf096f2727807b8b5349b07e5fa3", "datetime": "2025-07-31 16:39:23", "utime": **********.678453, "method": "GET", "uri": "/finance/sales/contacts/customer/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979960.903949, "end": **********.678488, "duration": 2.774538993835449, "duration_str": "2.77s", "measures": [{"label": "Booting", "start": 1753979960.903949, "relative_start": 0, "end": **********.282078, "relative_end": **********.282078, "duration": 2.378129005432129, "duration_str": "2.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.28214, "relative_start": 2.3781909942626953, "end": **********.678492, "relative_end": 4.0531158447265625e-06, "duration": 0.39635205268859863, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2026\" onclick=\"\">app/Http/Controllers/FinanceController.php:2026-2085</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0093, "accumulated_duration_str": "9.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.596524, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 75.054}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.63777, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 75.054, "width_percent": 11.935}, {"sql": "select * from `customers` where `id` = '2' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["2", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 2035}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.652272, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:2035", "source": "app/Http/Controllers/FinanceController.php:2035", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=2035", "ajax": false, "filename": "FinanceController.php", "line": "2035"}, "connection": "radhe_same", "start_percent": 86.989, "width_percent": 13.011}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/customer/2", "status_code": "<pre class=sf-dump id=sf-dump-613751827 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-613751827\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-722046118 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-722046118\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256023553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256023553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-475820397 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJQRkVZQmU4QjdkamEzY3h1UWhibFE9PSIsInZhbHVlIjoiMTFhRnc2cm9CZkZPQ0xkam9QYmRGdmNBeUJ4TmVjclhUblN2NDhFNWtxclVPTTFyTnp4TnpPZGdXOE82Wmk0aFVrMnV6OU1aRmZQWWtEc3Zpc2szZUo1WTVDMUJqcnRoU3g4UVRaSUJ0RjRUekQ1SlE1ZFJIenR0YU9zQklsbUNmdVhicjJla3BFdk5sL1N6NnlVWjRSNGxza3dSR2VpY1ZxemlVYzZPL2N3Q0M5UGNCM3UxMXo3eXlsRWsvMlFmTnNLU2xFNVVHUmxlM0ZDR2ZqUWhwVDRmWXJvYjE2MXIyeGV0OXpBcHZ6K2lEZ0hoV3UrS3dLT2cwTGpyOHdSUEloQXBKK2YyUWhYSFFzeVR6T2hhYWNPVjhtaDNvQjJEc2ZiT05KVjZqVi80SjBGcTM0NjlNcUtqV1UyQ0huV0MrQVB3L2p2T3A3N3hFV0w0NWJmTlNnZE1LZGVKa3ZhbHZRd3YrdklsNnFYSmJ4ZG5WVlk2NnFZTHV1RTU0eXhXVWEzQlB2TlQ0VVJra2ZsT0FzZVlKbU5wRWh3OUZaUWRNQUZHZkkwa3kzUDc1cW03Tlk3djFpcmdqck90Uk9acVlOZW5HazdSc0RRMkl5Qk12cXRhTXpkNE92YVA4Qjc0dlRGaVZLV1k3RXE3KzhReEZIcFVGOTBnOVp1VWMzMHIiLCJtYWMiOiJkYjQ3YTFlOWE1NjAxMzA3NzNlOTg3Y2M0NjZhZDg1MGFhN2ZkZjU1NmNmZmJhNjMzZTRlYTY1N2MwODUxMDk0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImNwZW1zQnZ3ZTRRd3RNNE5SYm9TcUE9PSIsInZhbHVlIjoiNFJyTzZVQng2eWJjVlhSQjRvbUp1NXphbkFMTC9HYkhGZmVSREFlM3dEWGQzNTVOQ0pKeWo2V0dUbnd6NUxNOU8yTkRTY3plMEIyUi9MdW1BM3VOeGpPRGRjYU5RNmIyQkVoVTQrYjdpVUdubEpwQU15QVUwOXJjRzBja2JxTGVSTEJJd3FKRzlKY2N6RHZzSHBlVG56dXIwUW0rZkFLbXJYNWlPOW5URVF4Q2ZUS0FBTzVIZHhtQ2lXSzBEb1MrZGRjRjJ5Uno0UFRiM0R6TnRTYmNuWFdjOFVSZ2ZmWUVDTXlFSk84RnRqc3dxamd5SStjbzN1ZzRlY0tJWXp0US9Gd3gyM0w3RnEzWkFVVHB0VkRTdVFadkE2Y3R2N0xaNmJLZHNnb0h4RlZsclZiWGxzVkR3azhBaXl0YWpPRXQvbWZhRjMvdkRSV0tSaDN1ZFQ1T3pOL2cyZUFCendXVFE0R3ByaFpTYVE1STYwcVcvdENXRlgyczlydHI5Q3M5WTBCVjkrTkw4T0xjYU1BRE9XZ2E2MHJFZnYvVEdvbFZERlgxeFEyQlppcG9ZaE9WbFBsVFZ2SDVIdGVmKzc0dnZxK2p3Y0dzbDZ6NnJxNUFncjg1YXgwN2F4bmtQSTFWblFUMWFCdXFjVWpncHkwQ1d0bGdUYUdadDlQLzdRRUsiLCJtYWMiOiJmNTBiZDZhODZhYWFiMjJjNDdkMzEwZDk0YTE2Mjk4ZDdlY2Y1NTMyZmNhNzhmM2JjZWI2YmZhY2QxMTc3YWY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475820397\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-772203368 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-772203368\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:39:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlNUEtocDRWUFhHNXRKb0c4UWs5UUE9PSIsInZhbHVlIjoiMjRwRmEvVEJTOGwza1N2UDlobzhPRXNsYWpJZzlFZk1wYVFucW0wazZXbGk1eHFPbnhXcE1vUW9GZ3N1UHp6Y3lWTmNCTjFEaGVUWUZhM1pSVnI4M0g3V3d4bXJrYWlYT05vRlI1VWgycXUwdGxkNlFobjZvREtGK3JQdWtlbjR3SVRHeWpubDcyUGtiaGNwU3VYVzgrU2gxK04xVzQxWWVoN1BqVDB6ZUFPTzVYekJkTmtTZnA1aWtZbEo5Y0R3dW1ocWRWRm1oMEEzRWN4YmhKR1BnUEJyWGdKQmNiYTUrOVJtd3ZBNWZvSDRjUjZ1V0pZaDRrSVBtdTFOYWRhREI4SSthNVZoRnVxSUxsMjA2Ri9lcWxwc0JNWmdNVlNaQzFjU21PcENseDF1T0dGTGtST3lOTUZiZE9KYnJzVm44ZXJpRWdCd01scHJsV1ZUWmtYeGpMVDlEU3BiYlpvNlVzVnNRZUNyN3hhcnVmWG0xZ0hwVThtRGlINlo2Q0w5djc1QmI4UVM2NlBEWmhqVzVRczM3a2txTUdBOC9Ua1hUVTJXczlNQU5EWUJnSlVUQVVZRmdKQzMzYU1ObkUydE8vR0IxNlQzNTZud09VQjlpeE8reXhDSHFET045SEhSbWZ0Q1BSTjlHczN5SGpDM2lZWi9lczdkZnZyTFVOZ0ciLCJtYWMiOiJiYTNmYmIxZjEzNzI5YjMyOTBiNTY2YTZhOWFmY2FiYTBhYjZiMmMxNWExZjIzNTQyZDM3NjQ3YjM1ZGQ5NWRhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:39:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVaeGY1UTFVaXFGczgwN1BQeWhIekE9PSIsInZhbHVlIjoiRDRGQnNvYThOcjJocFpPaE1RQjNHclpFcHlBRnArb1lRVG9lbjU3elo4bzRwQytIVjhPZ2JuR2pjUlhpVFczUXk2b08rd09yeVBSWUxVdDZhN1h0eEViTWJqSXJVQ0g4S0x5NWlYTzZBU2ZpaDRpOXhOM0Jva251L1B2eDdBRXJtZ1ZoMkpPUU9mTExIbjVjQnh6RzFCNU5lRDVqOU5Zb0o2bVMvLzJMR2E0L3cvVEZnUlNrcE1HVXUwTnpZMVFmMTRNeldwVDFUS2NEY1hKTCtsNVY0cnpTOHZpUlJLQ0M5dTR1NzRCMlExV051UmZROFVqTFNXcktoY2V1UmdJc0o1QXpJMG9Mck1UU0QrMmE0akErZXBxbUJQYlBhaFRGZEd2RmdrZjY4dnVXZEdDa0NKbDExa1FwSzQ5UGxXbnp0UHNQdldzVWVITGQxQVhpcjFzeDVqRWcrUWhodWU5UktseG9XbGo2c09QdWk4RUMrSUNKTmxhejdWQXhNSWRYaDBCV2JuajF1V05jVnQ4QnZROXUwM09KSUZLSHVRenFDbStQMUIybWVIYmlMbVRSbDZnc0JLRXhzWlUrOUxuNERaazM1V09iR0EyRFJBRjl2dmVSWEZnYW5lNlVvc1lTMFpuY2xwVHFFcWdHWGxrUGNoVlI5dTQ5cmc5S2tIRFIiLCJtYWMiOiJjYzY1NjY1ZGU3YjVjNzI2YTEzMDIzN2Q4NWM0M2U0NjI3NWU0ODQyMzExNmIyMWZiMjRkZjA1NzIyY2MxMDNjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:39:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlNUEtocDRWUFhHNXRKb0c4UWs5UUE9PSIsInZhbHVlIjoiMjRwRmEvVEJTOGwza1N2UDlobzhPRXNsYWpJZzlFZk1wYVFucW0wazZXbGk1eHFPbnhXcE1vUW9GZ3N1UHp6Y3lWTmNCTjFEaGVUWUZhM1pSVnI4M0g3V3d4bXJrYWlYT05vRlI1VWgycXUwdGxkNlFobjZvREtGK3JQdWtlbjR3SVRHeWpubDcyUGtiaGNwU3VYVzgrU2gxK04xVzQxWWVoN1BqVDB6ZUFPTzVYekJkTmtTZnA1aWtZbEo5Y0R3dW1ocWRWRm1oMEEzRWN4YmhKR1BnUEJyWGdKQmNiYTUrOVJtd3ZBNWZvSDRjUjZ1V0pZaDRrSVBtdTFOYWRhREI4SSthNVZoRnVxSUxsMjA2Ri9lcWxwc0JNWmdNVlNaQzFjU21PcENseDF1T0dGTGtST3lOTUZiZE9KYnJzVm44ZXJpRWdCd01scHJsV1ZUWmtYeGpMVDlEU3BiYlpvNlVzVnNRZUNyN3hhcnVmWG0xZ0hwVThtRGlINlo2Q0w5djc1QmI4UVM2NlBEWmhqVzVRczM3a2txTUdBOC9Ua1hUVTJXczlNQU5EWUJnSlVUQVVZRmdKQzMzYU1ObkUydE8vR0IxNlQzNTZud09VQjlpeE8reXhDSHFET045SEhSbWZ0Q1BSTjlHczN5SGpDM2lZWi9lczdkZnZyTFVOZ0ciLCJtYWMiOiJiYTNmYmIxZjEzNzI5YjMyOTBiNTY2YTZhOWFmY2FiYTBhYjZiMmMxNWExZjIzNTQyZDM3NjQ3YjM1ZGQ5NWRhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:39:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVaeGY1UTFVaXFGczgwN1BQeWhIekE9PSIsInZhbHVlIjoiRDRGQnNvYThOcjJocFpPaE1RQjNHclpFcHlBRnArb1lRVG9lbjU3elo4bzRwQytIVjhPZ2JuR2pjUlhpVFczUXk2b08rd09yeVBSWUxVdDZhN1h0eEViTWJqSXJVQ0g4S0x5NWlYTzZBU2ZpaDRpOXhOM0Jva251L1B2eDdBRXJtZ1ZoMkpPUU9mTExIbjVjQnh6RzFCNU5lRDVqOU5Zb0o2bVMvLzJMR2E0L3cvVEZnUlNrcE1HVXUwTnpZMVFmMTRNeldwVDFUS2NEY1hKTCtsNVY0cnpTOHZpUlJLQ0M5dTR1NzRCMlExV051UmZROFVqTFNXcktoY2V1UmdJc0o1QXpJMG9Mck1UU0QrMmE0akErZXBxbUJQYlBhaFRGZEd2RmdrZjY4dnVXZEdDa0NKbDExa1FwSzQ5UGxXbnp0UHNQdldzVWVITGQxQVhpcjFzeDVqRWcrUWhodWU5UktseG9XbGo2c09QdWk4RUMrSUNKTmxhejdWQXhNSWRYaDBCV2JuajF1V05jVnQ4QnZROXUwM09KSUZLSHVRenFDbStQMUIybWVIYmlMbVRSbDZnc0JLRXhzWlUrOUxuNERaazM1V09iR0EyRFJBRjl2dmVSWEZnYW5lNlVvc1lTMFpuY2xwVHFFcWdHWGxrUGNoVlI5dTQ5cmc5S2tIRFIiLCJtYWMiOiJjYzY1NjY1ZGU3YjVjNzI2YTEzMDIzN2Q4NWM0M2U0NjI3NWU0ODQyMzExNmIyMWZiMjRkZjA1NzIyY2MxMDNjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:39:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}