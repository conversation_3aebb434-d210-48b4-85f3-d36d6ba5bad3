{"__meta": {"id": "Xc763137769b44227fc543fabfd98ec5d", "datetime": "2025-07-31 15:53:28", "utime": **********.324791, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977206.507222, "end": **********.324858, "duration": 1.8176360130310059, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": 1753977206.507222, "relative_start": 0, "end": **********.206705, "relative_end": **********.206705, "duration": 1.6994831562042236, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.20673, "relative_start": 1.****************, "end": **********.324864, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IFBzdVpo9xkNNUzLbQAdzIuLPOxD7XRMZVSbeQFL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1404548474 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1404548474\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2133260032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2133260032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-742613476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-742613476\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-319518583 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319518583\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-965967825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-965967825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-443572828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:53:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRkc29JS291Z3lEcHVVbmU1UVJaQkE9PSIsInZhbHVlIjoiTk1xUmpQODhNWXZOZTM0QXYzaU5BcmJZWTdxa3J3T1ZRSmxZVmNzdmxnQ1pLcS9SZml0bzJvRXRUWlh1SnN1SHl6S0NDQW92M3V2ZTk4UU9NMXhTRWtsZWxFelZQY1JWZmtwcHR4NUpuSFNZdGF2T3Uxano5d2QvUHFZalVuQUM4R1lOZTJMblRDM2MyVVAwS01pTy80cklwR0VxMzFLdGR4bFZ6a0YrQjZvaFRtUE1mSklvb0RSd3ZZdGwxWVY2ck5POVJsZFRHNFcyZFJ6WTVpRkdub2NYeVlTa0pTWnZ1YlhvVmdEaElobG9xOGN3a1owc3M5ellwc3hyTXQwYXVLRE9Tem5DVzFEenArdlc4MjNmZy9JSk5HcUkwU2ZUSXRDc0NWR1NVaEtzRys4RnE0L1grd1piRVRLSjlzcUFwb21GMTFJa25NMFlzaDJJdUR2bjBsM1I4VWYyZitsaVVzRmpXU0NsVGx3R0o2TS9DbVdoS2kxcGdvMXRmSEcvTTVJMkY5bEwwRFVXNE9HdzNWVGYzNFJxSDMwUE9kZ3czQkNuTUFRQ21EMTZEZWJCcFBIc1M3SjBJNldickNBRm02TlhaZ3ZHdm9iK0dYdThNUkJSa2ZwRkFab0xXaDZRZmw5Rks3U1J0cWhFQVErQlVRN1BUTzZkUTZWbWhsNEEiLCJtYWMiOiI3OGExYmQyOGI5OTBhZmJlMjEzZjhjZjYzYjJmOGRlYjY3ZjllNDI1OWIxNDM5NTk2YTI2ODE3ZmZiNjRlZmMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imk5TGQyZGNrRFpIb1dyYU01Rm1BQ3c9PSIsInZhbHVlIjoiZkpMYmVBaWlzZEpnTkM2Y3l3bGl6ODB1WVh2RzBZNkt5Wjg5TXg0NXZDWnFyODhCZEhBODVzZUpmZ1RPcnREYlNzUFUwWkpGMm54WmVDcG1mMnBLNEV4MDR3RXFKazlNQlhqMC9JYms3NEJIL09ybmtRbGpoQ1g1bkR2Tk51MzZ0RUV3YVdjWmJ2eCtpT1pHQmduUGVjM1A2UHZ2a3Z3QkRDVzFFTFBSQVg0cUMvRFd5RURrcncrdHpncWJOOUI5bE1YVWI4eTJNUFFRYzdjUjNSYXo3clFNSzN0ZE5ya3NLZ1Uvb3phV1ZRdlgyU05CeEh6WmwyTUxLTWxzUWdFZERsbEFJSlV5aHVhUVZJVldUdEhNczY1WnA1M2lsS3g5a0JRMytSMGczQWpCN1R5UlE4cklhMXJMMmtwZWlVVjhvTi9BL1NEN2luSW1EUExNcnd4TWRIdHpYNHJiV3lvTWhzZE5SU3pIU2xoc3BQeFZaMEdRVmJUYlIrSERvUTYyOE8raEhBMjd1eWlKRHcwK250NE5iczF3Q2FaMktNWGxyNjF3NlU4NzlGNGtLVFBESUlDS1RLc3paR1NiVjFwbzNna3VzejV6YUg5MUUxZklpcHdpeWpnQ2tLeG9uTExXMUVxRG1mZWEzNlg4alVNblRLck1LUU5EY1FsZ25jejEiLCJtYWMiOiI3OGVlZmIzZjIxOTIzOWFlZTRlN2RmYjJkMzIwN2RmOTVmM2VlOWY5NzUzMGNmNGUyNGQxYjJmODY0NzU4OWZlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:53:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRkc29JS291Z3lEcHVVbmU1UVJaQkE9PSIsInZhbHVlIjoiTk1xUmpQODhNWXZOZTM0QXYzaU5BcmJZWTdxa3J3T1ZRSmxZVmNzdmxnQ1pLcS9SZml0bzJvRXRUWlh1SnN1SHl6S0NDQW92M3V2ZTk4UU9NMXhTRWtsZWxFelZQY1JWZmtwcHR4NUpuSFNZdGF2T3Uxano5d2QvUHFZalVuQUM4R1lOZTJMblRDM2MyVVAwS01pTy80cklwR0VxMzFLdGR4bFZ6a0YrQjZvaFRtUE1mSklvb0RSd3ZZdGwxWVY2ck5POVJsZFRHNFcyZFJ6WTVpRkdub2NYeVlTa0pTWnZ1YlhvVmdEaElobG9xOGN3a1owc3M5ellwc3hyTXQwYXVLRE9Tem5DVzFEenArdlc4MjNmZy9JSk5HcUkwU2ZUSXRDc0NWR1NVaEtzRys4RnE0L1grd1piRVRLSjlzcUFwb21GMTFJa25NMFlzaDJJdUR2bjBsM1I4VWYyZitsaVVzRmpXU0NsVGx3R0o2TS9DbVdoS2kxcGdvMXRmSEcvTTVJMkY5bEwwRFVXNE9HdzNWVGYzNFJxSDMwUE9kZ3czQkNuTUFRQ21EMTZEZWJCcFBIc1M3SjBJNldickNBRm02TlhaZ3ZHdm9iK0dYdThNUkJSa2ZwRkFab0xXaDZRZmw5Rks3U1J0cWhFQVErQlVRN1BUTzZkUTZWbWhsNEEiLCJtYWMiOiI3OGExYmQyOGI5OTBhZmJlMjEzZjhjZjYzYjJmOGRlYjY3ZjllNDI1OWIxNDM5NTk2YTI2ODE3ZmZiNjRlZmMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imk5TGQyZGNrRFpIb1dyYU01Rm1BQ3c9PSIsInZhbHVlIjoiZkpMYmVBaWlzZEpnTkM2Y3l3bGl6ODB1WVh2RzBZNkt5Wjg5TXg0NXZDWnFyODhCZEhBODVzZUpmZ1RPcnREYlNzUFUwWkpGMm54WmVDcG1mMnBLNEV4MDR3RXFKazlNQlhqMC9JYms3NEJIL09ybmtRbGpoQ1g1bkR2Tk51MzZ0RUV3YVdjWmJ2eCtpT1pHQmduUGVjM1A2UHZ2a3Z3QkRDVzFFTFBSQVg0cUMvRFd5RURrcncrdHpncWJOOUI5bE1YVWI4eTJNUFFRYzdjUjNSYXo3clFNSzN0ZE5ya3NLZ1Uvb3phV1ZRdlgyU05CeEh6WmwyTUxLTWxzUWdFZERsbEFJSlV5aHVhUVZJVldUdEhNczY1WnA1M2lsS3g5a0JRMytSMGczQWpCN1R5UlE4cklhMXJMMmtwZWlVVjhvTi9BL1NEN2luSW1EUExNcnd4TWRIdHpYNHJiV3lvTWhzZE5SU3pIU2xoc3BQeFZaMEdRVmJUYlIrSERvUTYyOE8raEhBMjd1eWlKRHcwK250NE5iczF3Q2FaMktNWGxyNjF3NlU4NzlGNGtLVFBESUlDS1RLc3paR1NiVjFwbzNna3VzejV6YUg5MUUxZklpcHdpeWpnQ2tLeG9uTExXMUVxRG1mZWEzNlg4alVNblRLck1LUU5EY1FsZ25jejEiLCJtYWMiOiI3OGVlZmIzZjIxOTIzOWFlZTRlN2RmYjJkMzIwN2RmOTVmM2VlOWY5NzUzMGNmNGUyNGQxYjJmODY0NzU4OWZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:53:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443572828\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-369932331 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IFBzdVpo9xkNNUzLbQAdzIuLPOxD7XRMZVSbeQFL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369932331\", {\"maxDepth\":0})</script>\n"}}