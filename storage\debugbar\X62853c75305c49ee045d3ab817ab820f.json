{"__meta": {"id": "X62853c75305c49ee045d3ab817ab820f", "datetime": "2025-07-31 16:08:27", "utime": **********.344317, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978103.753313, "end": **********.344425, "duration": 3.591111898422241, "duration_str": "3.59s", "measures": [{"label": "Booting", "start": 1753978103.753313, "relative_start": 0, "end": 1753978106.972767, "relative_end": 1753978106.972767, "duration": 3.219454050064087, "duration_str": "3.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753978106.972825, "relative_start": 3.****************, "end": **********.344442, "relative_end": 1.6927719116210938e-05, "duration": 0.*****************, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HtsiHAIFagpceN3NiP6YXkXeOitOQfhJtcXOyaB2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1958857139 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1958857139\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1700571991 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1700571991\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-900853191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-900853191\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2048994822 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2048994822\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636272603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1636272603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2083906040 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:08:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVKd2xrTWkrVjEvREhDYmNQZ0ttbGc9PSIsInZhbHVlIjoiZlY3ZEl1TnlraGJVL0lnYVBwL0gwUVNCaWtTY0M1RDh0M2Z5U0RXbHVLUlpua0FCbld1aXpZc1lFNnY3YVB4QVV0WGhrc1pTNE00OEJjbnE5MWNhaEhpb1YyVGRiN2orWkhKek9Yd016b0MzUDJjLzNobTN1MmVFMFdBU2Z6bTNyVUYycEt5MnBRYmdiZllURHRVcko0eTVpTWUzU3lXMnRKMFhPMnd2dmd0TURhTU1Ed0xVRXpiTTFqRnp2eFFSM3NiYmQ0aGtNM0tGNFlwclBpMlNFa3E4RlErTWtFZzdQMzRURnJKRHlRV1BTRmRvRFd1RUMvTEUyTE05RmpGUWFpd3RmUmVGUnU0TGdoeFRJeWdYWXo0VmljUXp1ZUhVWTA5YmVpb3VBSXlZcHlvLzJveHlJQkkxTkhXa0JwTGFsaTRJRFo3eHo5U3E4MzlwRjBidEFlZmxRVEkvU1habmZrOE1jbHlxUXh3NEtYSWR1ZTBpR2VneXIvOE9zUGtCdEJiclBZVEJHaG5IQmpvc1BvLzlla0YrTHUvRmpkb3lVTEYvMWlheUtlQTZxTVlUR0NPUis4bDVzcm9oenUvYTBPTVp0ZzVUaGRFY0ZrUkpmM0xIWTZ4c1hPR0ZtOE9pK29LMkJkd0VteG5Cc1RXdGpxV0lzS25tRkhGK2dmNVQiLCJtYWMiOiIzYWJhMzI4NDQ0OGM0OTVlMzJlNWE0YzdkZWI4MzQ4Mzc3NzMyOWQxYmFhZTNkZjIwZDBlNmIwOWJjMjc3OWEzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:08:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpVOGlWdHBNM1NSU3BoTkVWc3p3b3c9PSIsInZhbHVlIjoiVDBjWFRDM3kvRGZwWWZoQnRybGpoL241OEFVd3hoMzNFNDRuZTJPSU1vSU9jTC9QSzhqbUg4ckpoZjVyM05sN0tmVEtySDZKTmRYZ3FONmszZ0dKdXB3cldjSUJvMUFiUGZlSTQxL0lPOWd2OHZ5dzRFVTlTMkNNaXVBbE5NUzNCUCtrTkljSC94TGUvKyt5UGhwZko1NFlHOEZaWEVpbDdyVmFLdCtBdmxMS1lZTGtOUHpBNGVpaXI1eFNkYUhKS0QyQ0RReWFxcFkxWkVaMWFmTThFTUxlV2JtK2p6ZTFRd0srRWtRbnh1OVVuc2wxdWlXSVNBWDFmR09GSjZ2NDZlRHNqMXhyUHpnaDA3ZXBuNktSMTNSTlpHajJBYllHTkZBYnNyK2tZK0t5ci9hQjNVN2JjdEFZNGhXeVlObGtHNlNyWDErSUxDRjhwM1N6R1Noc3plMGFBOGpaZW9pYzNXQTBETFM3WkRSZzZmUjlVMmhyQzlWbldHMC9wTFFpcXFBK1J5amJ4QVNQajQwbGZYZm96MmlSSjR6NlJ0VHpvNi8yU2NoWWR3VEhXallHSk9zcDBFYTVzcHJ0R0QrV3FuUThDbWJLekVtNisrRjNZMVJHb0RPSUpMR3pvbkNNM0tMUFRlam15REhoMDRnVEhEZHh1ZHd6R1JKR0ozZkoiLCJtYWMiOiIxYjM0NzM3MTNjMWE1YThkNzBjZDkwOGJlZGFjM2JiMmY1NzcyMzdiODA4MDU0YWQwYzFlMzFmZGUyYTBmNjFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:08:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVKd2xrTWkrVjEvREhDYmNQZ0ttbGc9PSIsInZhbHVlIjoiZlY3ZEl1TnlraGJVL0lnYVBwL0gwUVNCaWtTY0M1RDh0M2Z5U0RXbHVLUlpua0FCbld1aXpZc1lFNnY3YVB4QVV0WGhrc1pTNE00OEJjbnE5MWNhaEhpb1YyVGRiN2orWkhKek9Yd016b0MzUDJjLzNobTN1MmVFMFdBU2Z6bTNyVUYycEt5MnBRYmdiZllURHRVcko0eTVpTWUzU3lXMnRKMFhPMnd2dmd0TURhTU1Ed0xVRXpiTTFqRnp2eFFSM3NiYmQ0aGtNM0tGNFlwclBpMlNFa3E4RlErTWtFZzdQMzRURnJKRHlRV1BTRmRvRFd1RUMvTEUyTE05RmpGUWFpd3RmUmVGUnU0TGdoeFRJeWdYWXo0VmljUXp1ZUhVWTA5YmVpb3VBSXlZcHlvLzJveHlJQkkxTkhXa0JwTGFsaTRJRFo3eHo5U3E4MzlwRjBidEFlZmxRVEkvU1habmZrOE1jbHlxUXh3NEtYSWR1ZTBpR2VneXIvOE9zUGtCdEJiclBZVEJHaG5IQmpvc1BvLzlla0YrTHUvRmpkb3lVTEYvMWlheUtlQTZxTVlUR0NPUis4bDVzcm9oenUvYTBPTVp0ZzVUaGRFY0ZrUkpmM0xIWTZ4c1hPR0ZtOE9pK29LMkJkd0VteG5Cc1RXdGpxV0lzS25tRkhGK2dmNVQiLCJtYWMiOiIzYWJhMzI4NDQ0OGM0OTVlMzJlNWE0YzdkZWI4MzQ4Mzc3NzMyOWQxYmFhZTNkZjIwZDBlNmIwOWJjMjc3OWEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:08:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpVOGlWdHBNM1NSU3BoTkVWc3p3b3c9PSIsInZhbHVlIjoiVDBjWFRDM3kvRGZwWWZoQnRybGpoL241OEFVd3hoMzNFNDRuZTJPSU1vSU9jTC9QSzhqbUg4ckpoZjVyM05sN0tmVEtySDZKTmRYZ3FONmszZ0dKdXB3cldjSUJvMUFiUGZlSTQxL0lPOWd2OHZ5dzRFVTlTMkNNaXVBbE5NUzNCUCtrTkljSC94TGUvKyt5UGhwZko1NFlHOEZaWEVpbDdyVmFLdCtBdmxMS1lZTGtOUHpBNGVpaXI1eFNkYUhKS0QyQ0RReWFxcFkxWkVaMWFmTThFTUxlV2JtK2p6ZTFRd0srRWtRbnh1OVVuc2wxdWlXSVNBWDFmR09GSjZ2NDZlRHNqMXhyUHpnaDA3ZXBuNktSMTNSTlpHajJBYllHTkZBYnNyK2tZK0t5ci9hQjNVN2JjdEFZNGhXeVlObGtHNlNyWDErSUxDRjhwM1N6R1Noc3plMGFBOGpaZW9pYzNXQTBETFM3WkRSZzZmUjlVMmhyQzlWbldHMC9wTFFpcXFBK1J5amJ4QVNQajQwbGZYZm96MmlSSjR6NlJ0VHpvNi8yU2NoWWR3VEhXallHSk9zcDBFYTVzcHJ0R0QrV3FuUThDbWJLekVtNisrRjNZMVJHb0RPSUpMR3pvbkNNM0tMUFRlam15REhoMDRnVEhEZHh1ZHd6R1JKR0ozZkoiLCJtYWMiOiIxYjM0NzM3MTNjMWE1YThkNzBjZDkwOGJlZGFjM2JiMmY1NzcyMzdiODA4MDU0YWQwYzFlMzFmZGUyYTBmNjFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:08:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083906040\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1480440890 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HtsiHAIFagpceN3NiP6YXkXeOitOQfhJtcXOyaB2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480440890\", {\"maxDepth\":0})</script>\n"}}