{"__meta": {"id": "X2f0099c484a10134ea442a3058fcc6fe", "datetime": "2025-07-31 15:52:52", "utime": **********.343837, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977170.075225, "end": **********.343906, "duration": 2.2686808109283447, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": 1753977170.075225, "relative_start": 0, "end": **********.075775, "relative_end": **********.075775, "duration": 2.000549793243408, "duration_str": "2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.075822, "relative_start": 2.****************, "end": **********.343913, "relative_end": 7.152557373046875e-06, "duration": 0.*****************, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VZv447ndK8FNFz4jdZQJSWRmI2hQOVBhwUOmFFEm", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-651280323 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-651280323\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1744033051 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744033051\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-288472634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-288472634\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552071737 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552071737\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1026380326 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1026380326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1582974886 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRvaGxqYUtWM1dQV0tPM0xEdzBpZFE9PSIsInZhbHVlIjoiQTVQWldWQXU2aU0zVTRnYmh5b1FGOU5ORGp6dFFZUEhTR2ZFSVNQamQxNE50aWl5ZmhIelYrZlB3L0k1cmg1SE9vcUg5VjZudVpCQ0lyV0tTMndTc0Z6MzhlMVI1SEdkMmhVMkpEWDBXeGZxa2F0b0Q0YTE0Z3ZoeURvVXBWbHhJSkVuck9oa1ZhZzRPdndLT1RIVll0K294WGR4eVhPRzRWanRhdFJHR1UxOHcvbW1EKzNQOFU1WDBCdFdLb1J4Zi9sQVBsaUpvYktlaXhBT0RucmpvNWNVd0NVZ1R5dk1KdGp0cnZFRVlzUzlvZlkva1R3L05RYVVONHJxMHBtRE42N01XUWIyM2R4N0Y5RWhkTlk5RDMwQktLOWp2ZkhkdDNSNWhiTGRLZVpCQURaSVpscm5aaTEybzZtc0d5SWp6QkxJMnJoZEpsN0c5WWlwcWZZVXJreXJzZVc0Z0grTWpYN1lzbzhFSTg0NGhFTDFra1NYMVRpekN4V3BZREwzYk9JYzYzd2xCM0hodHRGRm5RNTRzZFpoVWR3dVZGR3A5NTlPTzZDV1gwcU9jL0Qya0RwNzJmYzVXN3p5QzA0NldQNWU5M1h6dEo4dktsVjRFZGNWdmRQSjFGV2dWRHVKYVZ3c0ZFbWJJNG9aMUdFNzNRdnVhNmZqbGJiS1lqVU8iLCJtYWMiOiJhM2M0MDQ1YmVhZTQ1NmVlODg5N2E1OWJmMzczZmVlYTI0Yzk1MzY1NTg0ZDNjMjQ5MDYyYjIyNDA4OThkOTc2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlnVVpnVlJqcnlMWHhyc0JCRTZoMlE9PSIsInZhbHVlIjoiNVl3TmN3M0ZwR0VVdkxSVjBOdTRPQ0Nld0pZbUNDcmhwZngzUzlUTVdjb2tBdjU0Z2d0OVZDTzgvYU52aEh2OTJXa2RlbDE4Wmh0cmYvenMxNjRYeFZrV256VTk3UzM3VnBsRzdXYThLajdaWVV4TkJqVUZqcXlxbHJGT0JPMVp4MDA0WXhqRGl4T05mQm1yckIwWGE1Y1o3NGFJTzdmcVRZWENod2JucnB0cXFBSGlSeUVieEhTc3Zib25IMk4zWW1XQlNIb2p4MGc0c1VreU5QOHREdmtJck5oQ1MvZHZvWnppZ1MwRTZwZmtheGx5Wnp1UWpFcHlnU0VBY0xvQWNQd0xHQ2RObG51MWpKVzF6OElhOEFlazR0Mys5ejJtVlZ6c1VmakxvcnNCeWdjc2QzUGJXTEhJV0VJeG1YaGM0dG0vSTYrbW9IOWkzN3duaCtCWDNMdmdqUlg2THhPZlVmdFVrL3hIZTIrL2tDVmkraHN2NXlTZ0hZOXA4MTM4L21jZ3dpcWxxRHFELzVqSzdWK1MxajdxbEwzQmxOVFJKYzNVOE9iMUVPL2dFaWhOZk1jSXBYTmNCT3FYVDVzcmt0QUtzb0ZjYTBpYmtuRFlqeFF2Y2UzdFJIbkpyVzFYSk4rZVB3cjJPU2t3d1FvbjFLd0h4cjcyWkxWaDFxS3ciLCJtYWMiOiJiMGY3Yzg0ZGJmNzAyYjgyYzBkMTgxYTlmYTE3ZWJlZjNlODY1YmExZTQzMzIwZjlhN2UxYTY5MjE2ZGY2Y2M4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRvaGxqYUtWM1dQV0tPM0xEdzBpZFE9PSIsInZhbHVlIjoiQTVQWldWQXU2aU0zVTRnYmh5b1FGOU5ORGp6dFFZUEhTR2ZFSVNQamQxNE50aWl5ZmhIelYrZlB3L0k1cmg1SE9vcUg5VjZudVpCQ0lyV0tTMndTc0Z6MzhlMVI1SEdkMmhVMkpEWDBXeGZxa2F0b0Q0YTE0Z3ZoeURvVXBWbHhJSkVuck9oa1ZhZzRPdndLT1RIVll0K294WGR4eVhPRzRWanRhdFJHR1UxOHcvbW1EKzNQOFU1WDBCdFdLb1J4Zi9sQVBsaUpvYktlaXhBT0RucmpvNWNVd0NVZ1R5dk1KdGp0cnZFRVlzUzlvZlkva1R3L05RYVVONHJxMHBtRE42N01XUWIyM2R4N0Y5RWhkTlk5RDMwQktLOWp2ZkhkdDNSNWhiTGRLZVpCQURaSVpscm5aaTEybzZtc0d5SWp6QkxJMnJoZEpsN0c5WWlwcWZZVXJreXJzZVc0Z0grTWpYN1lzbzhFSTg0NGhFTDFra1NYMVRpekN4V3BZREwzYk9JYzYzd2xCM0hodHRGRm5RNTRzZFpoVWR3dVZGR3A5NTlPTzZDV1gwcU9jL0Qya0RwNzJmYzVXN3p5QzA0NldQNWU5M1h6dEo4dktsVjRFZGNWdmRQSjFGV2dWRHVKYVZ3c0ZFbWJJNG9aMUdFNzNRdnVhNmZqbGJiS1lqVU8iLCJtYWMiOiJhM2M0MDQ1YmVhZTQ1NmVlODg5N2E1OWJmMzczZmVlYTI0Yzk1MzY1NTg0ZDNjMjQ5MDYyYjIyNDA4OThkOTc2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlnVVpnVlJqcnlMWHhyc0JCRTZoMlE9PSIsInZhbHVlIjoiNVl3TmN3M0ZwR0VVdkxSVjBOdTRPQ0Nld0pZbUNDcmhwZngzUzlUTVdjb2tBdjU0Z2d0OVZDTzgvYU52aEh2OTJXa2RlbDE4Wmh0cmYvenMxNjRYeFZrV256VTk3UzM3VnBsRzdXYThLajdaWVV4TkJqVUZqcXlxbHJGT0JPMVp4MDA0WXhqRGl4T05mQm1yckIwWGE1Y1o3NGFJTzdmcVRZWENod2JucnB0cXFBSGlSeUVieEhTc3Zib25IMk4zWW1XQlNIb2p4MGc0c1VreU5QOHREdmtJck5oQ1MvZHZvWnppZ1MwRTZwZmtheGx5Wnp1UWpFcHlnU0VBY0xvQWNQd0xHQ2RObG51MWpKVzF6OElhOEFlazR0Mys5ejJtVlZ6c1VmakxvcnNCeWdjc2QzUGJXTEhJV0VJeG1YaGM0dG0vSTYrbW9IOWkzN3duaCtCWDNMdmdqUlg2THhPZlVmdFVrL3hIZTIrL2tDVmkraHN2NXlTZ0hZOXA4MTM4L21jZ3dpcWxxRHFELzVqSzdWK1MxajdxbEwzQmxOVFJKYzNVOE9iMUVPL2dFaWhOZk1jSXBYTmNCT3FYVDVzcmt0QUtzb0ZjYTBpYmtuRFlqeFF2Y2UzdFJIbkpyVzFYSk4rZVB3cjJPU2t3d1FvbjFLd0h4cjcyWkxWaDFxS3ciLCJtYWMiOiJiMGY3Yzg0ZGJmNzAyYjgyYzBkMTgxYTlmYTE3ZWJlZjNlODY1YmExZTQzMzIwZjlhN2UxYTY5MjE2ZGY2Y2M4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582974886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2118710922 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VZv447ndK8FNFz4jdZQJSWRmI2hQOVBhwUOmFFEm</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118710922\", {\"maxDepth\":0})</script>\n"}}