{"__meta": {"id": "Xe472444b8b8eb07cfbc6940bbac981a7", "datetime": "2025-07-31 16:09:54", "utime": **********.317456, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978190.619359, "end": **********.317525, "duration": 3.6981658935546875, "duration_str": "3.7s", "measures": [{"label": "Booting", "start": 1753978190.619359, "relative_start": 0, "end": 1753978193.932497, "relative_end": 1753978193.932497, "duration": 3.313138008117676, "duration_str": "3.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753978193.933165, "relative_start": 3.****************, "end": **********.317532, "relative_end": 7.152557373046875e-06, "duration": 0.****************, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvgu4l8LvvWEHR3UeCCMno446w9PlvgOuuVRaReD", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1185361488 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1185361488\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2041277366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2041277366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-482012397 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-482012397\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2075211713 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075211713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-853952407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-853952407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-550240114 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:09:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9LR1dxV2p3Vk9qVVd4ZjIzMXpWVmc9PSIsInZhbHVlIjoiODFGMWpUUVl5QjliNG4wTWltbnJnZ2o1N3M5dzluYUkva09MZzJLWjJyMGQ3a1ZUeDFNYXRSRXYvNENsRVV6ZXlseXMyWEpvSXdrNUVOam5uZys2emlFZU5ibUxEaTNUSVp6Yk1uTitFd3lsSmkyMkxQV2c1QXVqK2ozVGhyK2V0S1JNdExxbWg0cFZwV1RhSnltMWpaay90dFlvNU5uWVVONk4zaEJUZXUxOFlZWWRNR09xRnc3VVlCYVY3YzM3bW1INDlidm52Y2I2WC9LUUZaK21JT1o1clJxQ2oyRTJRVFRTSTZnQVY3aWhucitrQmJzUGhHRzBhWENzOERSK3lQczNpVUZDeEFqU3oxQkEzUnJCUUhNTHBiYnkxRkYrb0w1MkJRZ1VPRUhiWks1TTRFL0xSRjN3ZklqczRXejhZSmcxKyt0OElrWkJxVTNOaDVLUVAvZTJtUmxVaGlGU0pDMXpqK1NpUEswcXFWdUlORFFyNENtRnFGL0hTVFJSYnBrOXEwU0o4MUVYaFZ6cDV4WmVRSVNyRkE1WDZRQTJ4SmpYT2x5NFRZS05La0FKTDFkTDlKZm5vd2VsTGJ6OHJoSmZHVjBDMWN6K1AzOWJhblRCVVY2RThhbW9hTFpYWlFKd1p0MklSRmVjWWd4aFpCS0VYcU5memRFRFRTcSsiLCJtYWMiOiJlZTY0ZGQ2NDM2YTYwNTRlZWNmZGZlMGRiYTk0ZjZlODc3OGYxNzk3OTRlYjFhYjM1MTU5NmUwNGFhMDExYWRlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:09:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIyUDh4RElNT1o4TlVmb2ExaU5TQ0E9PSIsInZhbHVlIjoiMkJxVkljVlpCL2FSM1ZJcDdJT3Q3alp2dGc4T1J1TVU3TmF1Ni9DN1RySUZ0N1BrUG5JL0RzMHhFbVFrTnYrWWRUYzlOZUhKZUFiR1EyWnBXenYyc3JyR0JGaWRiRHZOczloT0tYamNSeFdhQWJrZTdjaXpJQzRsbEl3WUdGNTZ3a3hxeEpaOHdIMFRxSnppNFpVd0R3VW14dVRMTDk5M0dDanp0cHBCWm5ZTk53S1p6SkNBUUJRcWw3VXBnNVhSYmg3YXhmb0Vla0NaMlBkNHJ3b3RmVHI0cUFyWUJoUFdhVDF4RDZTMHVvZzJoTTVsSERUb0Q1d3BMSXNrOHVCcjV3NHJGdWFqcWVMQklMSXhPaWc5UytkQ0gzdHZ5OFpEdktKOWJaOGtHU1JUTGZjc3p3RDFSUDhjVDFDaVA4blYyYzZYMGd0MExZTEcrcjZLQXJBZ0ZIRzdTR2lnL2daMWZWWUh0Wm9Rc0pnSFdrOGFwNVpGYzE3STBtRWlhcjc4Z1hkc0VjWk9BOHJKNzV3VDE3cVRndTQ0Q3ZxckhVcS9vSWszdjRBckc1MzUzM1N2eHVlbHRVL0VyYjFSaHRST1NaUTVPU25sRkJxR3JpZUtyc1dXamJlak9FVHRUeW81SVBNdzU2dE9rS3Rob2FtNW1vbDhuZEorOTc0ZFVuMysiLCJtYWMiOiI5ODVjZjFkOTA4Mzg5OTg0MTFjZjI4NWUyN2E3MzdjNGYyZDQ4ZGYwOGJiYzdjZGVhNzM5MWYxNjI4ZGMyYzc0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:09:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9LR1dxV2p3Vk9qVVd4ZjIzMXpWVmc9PSIsInZhbHVlIjoiODFGMWpUUVl5QjliNG4wTWltbnJnZ2o1N3M5dzluYUkva09MZzJLWjJyMGQ3a1ZUeDFNYXRSRXYvNENsRVV6ZXlseXMyWEpvSXdrNUVOam5uZys2emlFZU5ibUxEaTNUSVp6Yk1uTitFd3lsSmkyMkxQV2c1QXVqK2ozVGhyK2V0S1JNdExxbWg0cFZwV1RhSnltMWpaay90dFlvNU5uWVVONk4zaEJUZXUxOFlZWWRNR09xRnc3VVlCYVY3YzM3bW1INDlidm52Y2I2WC9LUUZaK21JT1o1clJxQ2oyRTJRVFRTSTZnQVY3aWhucitrQmJzUGhHRzBhWENzOERSK3lQczNpVUZDeEFqU3oxQkEzUnJCUUhNTHBiYnkxRkYrb0w1MkJRZ1VPRUhiWks1TTRFL0xSRjN3ZklqczRXejhZSmcxKyt0OElrWkJxVTNOaDVLUVAvZTJtUmxVaGlGU0pDMXpqK1NpUEswcXFWdUlORFFyNENtRnFGL0hTVFJSYnBrOXEwU0o4MUVYaFZ6cDV4WmVRSVNyRkE1WDZRQTJ4SmpYT2x5NFRZS05La0FKTDFkTDlKZm5vd2VsTGJ6OHJoSmZHVjBDMWN6K1AzOWJhblRCVVY2RThhbW9hTFpYWlFKd1p0MklSRmVjWWd4aFpCS0VYcU5memRFRFRTcSsiLCJtYWMiOiJlZTY0ZGQ2NDM2YTYwNTRlZWNmZGZlMGRiYTk0ZjZlODc3OGYxNzk3OTRlYjFhYjM1MTU5NmUwNGFhMDExYWRlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:09:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIyUDh4RElNT1o4TlVmb2ExaU5TQ0E9PSIsInZhbHVlIjoiMkJxVkljVlpCL2FSM1ZJcDdJT3Q3alp2dGc4T1J1TVU3TmF1Ni9DN1RySUZ0N1BrUG5JL0RzMHhFbVFrTnYrWWRUYzlOZUhKZUFiR1EyWnBXenYyc3JyR0JGaWRiRHZOczloT0tYamNSeFdhQWJrZTdjaXpJQzRsbEl3WUdGNTZ3a3hxeEpaOHdIMFRxSnppNFpVd0R3VW14dVRMTDk5M0dDanp0cHBCWm5ZTk53S1p6SkNBUUJRcWw3VXBnNVhSYmg3YXhmb0Vla0NaMlBkNHJ3b3RmVHI0cUFyWUJoUFdhVDF4RDZTMHVvZzJoTTVsSERUb0Q1d3BMSXNrOHVCcjV3NHJGdWFqcWVMQklMSXhPaWc5UytkQ0gzdHZ5OFpEdktKOWJaOGtHU1JUTGZjc3p3RDFSUDhjVDFDaVA4blYyYzZYMGd0MExZTEcrcjZLQXJBZ0ZIRzdTR2lnL2daMWZWWUh0Wm9Rc0pnSFdrOGFwNVpGYzE3STBtRWlhcjc4Z1hkc0VjWk9BOHJKNzV3VDE3cVRndTQ0Q3ZxckhVcS9vSWszdjRBckc1MzUzM1N2eHVlbHRVL0VyYjFSaHRST1NaUTVPU25sRkJxR3JpZUtyc1dXamJlak9FVHRUeW81SVBNdzU2dE9rS3Rob2FtNW1vbDhuZEorOTc0ZFVuMysiLCJtYWMiOiI5ODVjZjFkOTA4Mzg5OTg0MTFjZjI4NWUyN2E3MzdjNGYyZDQ4ZGYwOGJiYzdjZGVhNzM5MWYxNjI4ZGMyYzc0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:09:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550240114\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1565421699 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvgu4l8LvvWEHR3UeCCMno446w9PlvgOuuVRaReD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565421699\", {\"maxDepth\":0})</script>\n"}}