{"__meta": {"id": "X37147af4242f998b853087a143c796c5", "datetime": "2025-07-31 17:14:09", "utime": **********.235247, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753982047.084226, "end": **********.235278, "duration": 2.1510519981384277, "duration_str": "2.15s", "measures": [{"label": "Booting", "start": 1753982047.084226, "relative_start": 0, "end": 1753982048.993314, "relative_end": 1753982048.993314, "duration": 1.909088134765625, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753982048.993447, "relative_start": 1.9092211723327637, "end": **********.235282, "relative_end": 4.0531158447265625e-06, "duration": 0.2418348789215088, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0098, "accumulated_duration_str": "9.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.15336, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 51.02}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.186002, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 51.02, "width_percent": 19.592}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1987438, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 70.612, "width_percent": 13.163}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209151, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 83.776, "width_percent": 16.224}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1746256225 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1746256225\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-902608180 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902608180\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1753729520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753729520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-74183957 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJ1WlhnYTdxUm5TajVPWVk0YkZ5c1E9PSIsInZhbHVlIjoiL0d2QUV6c3h5Y09TSGJORzdXdzBxcTFyaXc2dUcrdWpqejhLL1dORVlRTWQzOWxzUHNqNHBKenZkbnNQdGlpbE41ekNYVTZpRnNhbUx1L3Q2NzRXbnpVUm5IVjk0YkxGS0VPWE1PYmNPanRhekVqa0VMRklJTFZSQUJ0UG1pOEFPTnVMVmZxelA2MUlDVkEzL0dQWW1YdnlHajhsYytyQ1BUY1ZpSDhyaVY4OHpJZ3pzMTUrbHV2aWN4cFVaTmZ4ZTErd1lWQW15UmZpOHNiRG5nTld3eWhvQ3JlT1NHRUdJTm1CMnhoMWlNRVB0SlVGbHNxZFp5WTRuOFZwbC9UMGNBZWNFaTQ3ZGM2a3RUdnRhSkRSYkJ0TjMrTEFxUmdYZ1pTQWxOY3JoWjB2aEVBc3VBcFJKQ2NmNU1lbFlDL0gxenRhS1BvOGNBZTUxanVrOVRCNGVaVFhUUXhLUHlWbzF1bWozSFlTMWtFUFFYQndiaGFhOStSUWFCTkg1TDh6Z2N6dmJhYTNEUWpoSzk2ZkhneE5pTnVwSVJxK3hUd3JRc29ER0FVaHgvdnorRkpjKzF3L1g5Rkk4b2Z6azlzdVdjM3Fqay82Tk5zNURkUkQ1dXNRckp6NFRpcUQvWnhDN2prR3o5Y0VnRWY5MFJwaEk2YWozUUNwZWpFRFk5WVIiLCJtYWMiOiJlMDAyZDZiOTNhNmE2NzM2MzIzMGVjOGRjNmYwMTJkYjI5YTgzNzU4ZjRlZWQwOGIxZmNmZmY4MGVhZmUxNzhkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjJYTllrSjNsZmt2bjRKM1lYL1ZNY1E9PSIsInZhbHVlIjoicXpaTWthdDJWWktuUUp3bGRHMWIxRXZBczVyRlZoU2VSdlM1eHRTQ2NjMmVPYnErRlhjL3lWUHBlQ1BjaXRkZGVkeFYvZkMwbWlMb1JVZzF4U1B6NGQwaEZnM2c0dzJCL3JPT3h2cWhha0RVbjM5dkhhT1h0R2N2N2U2T1VETnhUQjdqZWcrQTJQRDZISDJsOWJyR016WVBqNkJSN0ltZUluRkdaNFNJV09EQi9WVEh3UUt2TTEzMmJyajAxNWNVWnpoWTcvWU5nSDJGQTdlcnZQQUpnWGlpS3JjeUVNUkI3SXZBcXI4ejBFaFU1WTRtbkNTelJjV1pBWHQ1MVpmVi9yS1ZjRGZkdUNBcU1VTndib3F3b0tGcDRsNmFrWkRIUEhmRzBIangvVXdWYU9mYU1hZHd0a29GOEEyaXl3V1hkQXJpQU9RK3dna1BpcFhoOXFpUTBpaUdSYkozTWpjdnhFSFduRTlwMjhPV2dWZWhWTjdBQUUvaU0rUXVxL2ZaMWZESDFSa3IyQWFodTJhQ2liWG5DRldqeklwMEwxVGVEVXBOeWtCWmsvWWtjeng5Z3Rjdm5qN2pYMCtoekhTRzMzMG4vVUZValowNjNYbWpvVDNDRm5zZTZvcU9zZytnYS9SYmhhVGk0Qmo3UHNCZSsyZVFpUk9NTDdHdzFQanYiLCJtYWMiOiI3NjY0OTgzNzRiN2U0MWNjZDM2N2JmODYwZTZlMzhhMGIwNzBiYTk0ZjU3ZTEwMjI2YTBlMzIzNjM4MzgwNDE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74183957\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1345384603 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345384603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-931822285 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:14:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlFZ3FHbjNycmhOb1V3MTBGR1F2SWc9PSIsInZhbHVlIjoiUmpHNWRGOWZoY2RmZHZodzdTNzhHVkd2Q3FFSEp5RjFjclVJdmZ4cmNQb3pWQW9CbWNqeGZMQWtKbmpqOEpIT0RqU2toamZVSDV4eUNsaTJubHdNY1ZLTDd3VGRFdUo4MVIwbGRvZ3pUVEppemZBTlo1MHk1TVZtWHlnMG1VMm1CVHo1Y1BjOW44Tmd0T1BHWmFIR2ZrWTdKOE9BbGxwVXNXWTFVVDVtNUgwT25STWc5M2JuL2JDUnRIOEFVTit4MDBzUndqUnBlU1Vqa0FzOC9TRDc3MVAvNUkrY05IbUgvTmlTWnZFS2VpY3BNdDNpdFpQejB3eTE4cVVocHZ1VEpYblhPeGVSZ1pKMTMzcTRpOStRdm5LQmlXWWVtVDl0YitDT1lLY1A4amV2MkdvcHFOalRQQmVkT3pkUUtzcjJ3Y3ZIWEgrU1lGOGhoNWxrTDdkQ0R4dE5QOXlIN1NKY2tIdXVlblhYRTcvaldSZmkxYjNWbnk4eVA3Q2VJeEJJVXgrZjRlSGVGR0FiRy8zYUVVS1J0SHhSSUNxMGhZWXJxUElkWVM2QVZVTExvekMybTdGMmtHTlVjdnJ6c2FneVFnMERMUUFoS3BwVW9lUGRMNE9XSVU5SnFrNFNITlM4RFVUSjhPZk5GM3hDNXkyOEhFQTlGYzBTVksyTEJHbzIiLCJtYWMiOiI5Mjc1NzFmNWFiMjBhYzYxY2UzOWY4ZjU1ZWM3ODgyMWU4NjdlMTU4N2ExNGMxYzU2MzllMzNkZWZkYzFkYjJiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im0yK0lwUCtHbHVBQjkzYkxLR1FQcGc9PSIsInZhbHVlIjoiY3FVRkNZU0hCS1l3QzRnWnNtdndnd2VaS1pudEZiY1RpRXdJM1d1STF0ck1SZkx0ZGdVK2Jrck55SGZxY0syOHN4UGRNY0FjclR5SHY1S3B5N1NJQ0JBUmt2MkJWREhpRnAxK3NYam1xYmRIMnRhakwyekN2dTdPeGJzNmM2amFJWGNnK2h5bnJyaWpHVUVzd0p2ZUx0SXdJR3grVjAxbE9mVUlrK2xVMW5jN1FCblNFS2FaVW5wRnlWMmNHaW9KWk9LL2x0aWhXT3hNQW5OZ2phS0IrOUVjQ1UxZjVoeDE0aGRqcG1nQ3RaS2d3TlhVYnV1SExma2xwOTNISFBzVkUyem1xcVowaWVMbXh3Y04zMVhQcUFqR0VvUGdDTWxqVTBiTTVKNkZjczZiaFNLK0pJTnlEcXphbk8yRkJsT1BwOTFGRW9pYmZIN0JJYUVCdW5BNTAzUld1SEhHTXFYMWVKdUtRTzlZMjdTb3hsSDVGdW5reCt4ZEtiWjE0Z1FEUHFGYVFJa0wyMzcwamVFNEZqbjIvOHNFdlVremlESzN5dEFNNDhDdDdBVXNWa1RQNktjcTRObWM3ajhEUTZ5MnpFT3p3UDBBVmxjQ2tmZG1GelRYdWR5NTkybUZEV3B3OVFtdmRieTBzZ2dQSzNtV2FlclpRM3dpWEZROVpPbkUiLCJtYWMiOiJlOWRjMDY3OGRjNDZkYmU2ZGY4ZGE1YjNjNTZkZTQ4NTNhZGZlODNlMzdiYWYwMjQ1MDBiZjA3YmM3NGE0NGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:14:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlFZ3FHbjNycmhOb1V3MTBGR1F2SWc9PSIsInZhbHVlIjoiUmpHNWRGOWZoY2RmZHZodzdTNzhHVkd2Q3FFSEp5RjFjclVJdmZ4cmNQb3pWQW9CbWNqeGZMQWtKbmpqOEpIT0RqU2toamZVSDV4eUNsaTJubHdNY1ZLTDd3VGRFdUo4MVIwbGRvZ3pUVEppemZBTlo1MHk1TVZtWHlnMG1VMm1CVHo1Y1BjOW44Tmd0T1BHWmFIR2ZrWTdKOE9BbGxwVXNXWTFVVDVtNUgwT25STWc5M2JuL2JDUnRIOEFVTit4MDBzUndqUnBlU1Vqa0FzOC9TRDc3MVAvNUkrY05IbUgvTmlTWnZFS2VpY3BNdDNpdFpQejB3eTE4cVVocHZ1VEpYblhPeGVSZ1pKMTMzcTRpOStRdm5LQmlXWWVtVDl0YitDT1lLY1A4amV2MkdvcHFOalRQQmVkT3pkUUtzcjJ3Y3ZIWEgrU1lGOGhoNWxrTDdkQ0R4dE5QOXlIN1NKY2tIdXVlblhYRTcvaldSZmkxYjNWbnk4eVA3Q2VJeEJJVXgrZjRlSGVGR0FiRy8zYUVVS1J0SHhSSUNxMGhZWXJxUElkWVM2QVZVTExvekMybTdGMmtHTlVjdnJ6c2FneVFnMERMUUFoS3BwVW9lUGRMNE9XSVU5SnFrNFNITlM4RFVUSjhPZk5GM3hDNXkyOEhFQTlGYzBTVksyTEJHbzIiLCJtYWMiOiI5Mjc1NzFmNWFiMjBhYzYxY2UzOWY4ZjU1ZWM3ODgyMWU4NjdlMTU4N2ExNGMxYzU2MzllMzNkZWZkYzFkYjJiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im0yK0lwUCtHbHVBQjkzYkxLR1FQcGc9PSIsInZhbHVlIjoiY3FVRkNZU0hCS1l3QzRnWnNtdndnd2VaS1pudEZiY1RpRXdJM1d1STF0ck1SZkx0ZGdVK2Jrck55SGZxY0syOHN4UGRNY0FjclR5SHY1S3B5N1NJQ0JBUmt2MkJWREhpRnAxK3NYam1xYmRIMnRhakwyekN2dTdPeGJzNmM2amFJWGNnK2h5bnJyaWpHVUVzd0p2ZUx0SXdJR3grVjAxbE9mVUlrK2xVMW5jN1FCblNFS2FaVW5wRnlWMmNHaW9KWk9LL2x0aWhXT3hNQW5OZ2phS0IrOUVjQ1UxZjVoeDE0aGRqcG1nQ3RaS2d3TlhVYnV1SExma2xwOTNISFBzVkUyem1xcVowaWVMbXh3Y04zMVhQcUFqR0VvUGdDTWxqVTBiTTVKNkZjczZiaFNLK0pJTnlEcXphbk8yRkJsT1BwOTFGRW9pYmZIN0JJYUVCdW5BNTAzUld1SEhHTXFYMWVKdUtRTzlZMjdTb3hsSDVGdW5reCt4ZEtiWjE0Z1FEUHFGYVFJa0wyMzcwamVFNEZqbjIvOHNFdlVremlESzN5dEFNNDhDdDdBVXNWa1RQNktjcTRObWM3ajhEUTZ5MnpFT3p3UDBBVmxjQ2tmZG1GelRYdWR5NTkybUZEV3B3OVFtdmRieTBzZ2dQSzNtV2FlclpRM3dpWEZROVpPbkUiLCJtYWMiOiJlOWRjMDY3OGRjNDZkYmU2ZGY4ZGE1YjNjNTZkZTQ4NTNhZGZlODNlMzdiYWYwMjQ1MDBiZjA3YmM3NGE0NGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:14:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931822285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1445913668 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445913668\", {\"maxDepth\":0})</script>\n"}}