{"__meta": {"id": "X4fddf9985ec9eff480368f83bfdf41ec", "datetime": "2025-07-31 15:51:36", "utime": **********.499315, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:51:36] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.492619, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753977094.809532, "end": **********.499357, "duration": 1.6898250579833984, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1753977094.809532, "relative_start": 0, "end": **********.191377, "relative_end": **********.191377, "duration": 1.3818449974060059, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.191415, "relative_start": 1.381883144378662, "end": **********.499361, "relative_end": 4.0531158447265625e-06, "duration": 0.30794596672058105, "duration_str": "308ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46097192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01044, "accumulated_duration_str": "10.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.352397, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 66.858}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.396831, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 66.858, "width_percent": 11.494}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.453572, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 78.352, "width_percent": 11.494}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.46206, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 89.847, "width_percent": 10.153}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1905752610 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1905752610\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-590808489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-590808489\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-4266481 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-4266481\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1724806822 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkphclBQT1VLbGdXRTJwUnJHUEZxSVE9PSIsInZhbHVlIjoiNlkyM0FLZ2phNTExamhscktFRlpjOGhRaXh3cTg2YW11dVE4WkdEY2JkZXY1cGYza2VRRWVDWk0rdUhvMFdwaGNBT21VY2ZyMjFwSUVCck9rbXdma0Jrajd2UUhNL2sxSVpvRmJDcWNrTWRGc0FZWXl1Ym9pQ3dBSFVJYUR3TTlVOW1VQXJJdkVlQUQ4SW1NQUU0L3k0MGdZbCtxbFE3SjFpaXZkQkUxbFJTUWtYM1dCaXh1SUNkZ0pjNnRiMVRkaDBrcWorWm9JaUdaM0k3b3l1Mk83TmpibmoxS2xrSzNIZCtnbGY0TG9hZ29jR3UydmJmNW9mR2hPSHowN3VDQlMzamtDU2JLR1ljLzZiM2kvaWV1ak1INFlHUDF4TndPcXozQnE5NXBzNldiZ1lMLzhOSU8xcWZrWDJrajU2K3E4TzlJd2dIanZZSk1jdi9wWlg1K0lKaXp5RE1UNlIvc1JsaVQxelUzeFZTa1ZVd0dwN2hlUmVQV3JhQTZIMURMM0lDZVRvL3k5YkNMemxiMmR5K1p3RE1iRXlLR1pLaE10Z2pPcEtrWkNLeGJ5eFNBMHoxTERXL1V5MGpZMGo1QndSREduV25oZjJiU2hGaVozNFhrSEJqQ0xIdTBRV2dRNUlTaFNKMEg3R2VpSm0wNlhDSTJqUlhEUjJOYTVDemQiLCJtYWMiOiI5MDJkZDYxNzNhMjIwZGRjZmFjYjk5YTY2NWZkN2MyZWMwNGMzYTAzYWI2M2U0NTUxMDY3YzUwMzVhYjM2MDlhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjlZMFlPQXVDT3JWSFU0VzRhRHpUK2c9PSIsInZhbHVlIjoiYSszZi9iRjgvWmdEa016SzAyOFVrWkllcEo3UmlaS3VZM3NVV1NjZGJxL01JaWFzSVFJK09BWkkvVVBUby9VelJVTEdxRkVIZS8xbjYrTEJ0WGR1aWROOUI0WFdwckJ4NHNJYmtEd0s2a1RjQzhySjNyZW4vRnZPcHB6UTdQS0MyRVNrVzEyKzNjOVZoRU1OWnZIcmFiLzN5dFpVekdxbjEyelBicytYdzJTemVYczJEdmxlL3I3M3NROTNHWWhiMEI3RVBTZklEcmt6Ym56emJlcGtUWDVSMnV5TTZRdUlGdXJZR0NJbjRLdDlrTnFjTkkvd0xhaHpxT1FkZjZ0TEIvODlCaC91ZnljUEJFQlFkb3VTY3BlZUxJVWJhMldNQjJrYis0SWJnM25GVjIrd3AwRVF4K05ESEFkQ25LVTZYb2E5VXgrcDZaOEJCbXN3MloyMXZoT3I0MGVtYnRSYitxQkp1WE9jVm1Odk9PUlo1THpiRFhZU3dhOS9iOUFFeXJVZ0lKa1JEbk9uVlZJMzdCdFdPRVVHVjZtYjJIQmVYU0JaelF4eitqUWxXV1QyazlpbU5OMXI1a2ljdzBFV29SbXlZQXFoSEJIYXZmY3NuS1VsS2pvSmplbmNPZ1hpazUrWjdMamtCVWdnQzFWVStZSmJOT2YxMVNwUTFwQk4iLCJtYWMiOiI2ODNlZWY0YWVmYTM0ZDMzM2I0ZDM3NjdjODkwMWY5Y2FhZDI3NGRkOGRmOTU5ZjU2Yjg1ZjM5OWRkNjY0NDYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724806822\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-491379162 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tkY9CRlDbFfp5lomd7oRDJqrNFwJ8jruTsGOoaTE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491379162\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1244511592 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:51:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVaN1dYeXRJYWM2YWRUeWFydTE2U1E9PSIsInZhbHVlIjoiSVlUNWZPaXZxMlRUZW5kbmphVFBpa0tiYWw2T2laKzNyWU5VRWRZNmJCT09FNktMY3I1cXBTaFhOUzFpZkxxbUE1QUo5TjQ5NDRWNEZJUmRXTkhqM21MQXdHbCtxamRyU05PMEkvV250UjdGQjVWUCtBekZndVdMUVR4RCsySjlCd3pXb1lQL0tMYmxpU2Rld01vd1BnVzBUdWw5Q3RqS3pobDNtOWd6Ukd0U05JWnlvVFBEZEJ2eHJ0aVJudmhYZ3pTNVFIM1RFVVkwVW9lQnU4a2d2V0NZTjdBSGRsMUtWazdRaGpxWmF1WSszZW1pTVZ0V3dqdG1ocENXZ3oybXQ3VUllU1hlSXBvZVNXbytIM2g3N2NGcThJNzNDdm1ZY1F3TFlBcUpkeWI3T3VIVHJmTWt0MUJVb2RpSHVJQ1dPb05PcDJPdlg1Y0M2eDc1TFZVamlXdmx0N1gvdTB3STVaNjM4YXFWRlU0WktWVHZQUk50c2FUWDd1SW5OWDF4blh1dFF1d3hjVzBmVnU3ejdad3BXWVZPWi90QjBQM0tyeWNBSS9ITERsaW96Tmh3MzEzSk9hY0t3TGxiQ0hwUmdIU0FZMyt2N3NvTFJrQTd2aTFyd0ZhaHk4dkxMdUsrRkJ4OFROd1A1RFppWk56d1NVTmtGMWZYM3laZUVlN2kiLCJtYWMiOiIwZDUwNGVlMmFkZmEyODNmZDI2YTZjZDE4ZjZiMTc1NGYwM2Y0MzQ5OTk4ZjNjYWIzYjA0NTg1YjUxMDkyYWFkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:51:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVsckptaXhCL1lnNlB6Z2FURWxncVE9PSIsInZhbHVlIjoiNkhPWEROaWFsanpCMlU5RW5LVmV1cXBlOGpkSWJKVWdFOFc0L1llcUp2c3hsQnVGVTRJVjdNZHluYVkxcU02b0pyN3ZocUpjVHl5Z3pxLy82eDNVOUxlQXJLYm1uQzgxSWRxKzNwOE9UdnRMTmZ3SENaL3dWcHFiMGpQakMyWUY4M3ZSbmppd2ZGajZsVDFBVUpYMEFMb2RqbmJ2eGRmS2g0aSszZkF4SDA4NVE2UkFRelM1MGtOd2lLekYzbjlYQTVWdVA1UVkvL3I3QnU0eGY3QU1tSG9nR2JJVGt6V1J1WHA3amc2cDZDaTF0alR1VmNpZzNNVzJPYkdDZEtCbVpLS2lvYzlTelpld3lsd1ZTL3hEUlR6UFNqWmRGZzllMjZuUU9LQXpGcHUvZXNlZjZoSEQ5Y0dGMVBPaTNwVm1jMXk1Z2FMcStUbnFWUzVhejIzZ2VGb2FnTlc0YjJqdnpzRzJxdzlJNnJzaENOWEZ2TlRzSmdHc3BWNVQ5N215U3RyMkhFUDRPOWxZUmxjOXRyOFJPaVgrNUl0Y2FXZVBpeWhTaks2UCtGVVQxZ2liSDhxckhFZlNFNjdPRXNXZGdydjJVSzVUb3VXYXlRclU4clNaODFvL1YrczduemNjcjBIVGdFQmZrYnN2eW9RNWdPS01lNDNSbkhMdzlVRWkiLCJtYWMiOiI0MmVjMjdmZTdiNzhmNDQwOGU2YTZhMDExNGUzM2IxNzBhMzE1YjU5NTY3YWFjN2QzNjAzYzQ1MTMwN2Y3MjJjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:51:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVaN1dYeXRJYWM2YWRUeWFydTE2U1E9PSIsInZhbHVlIjoiSVlUNWZPaXZxMlRUZW5kbmphVFBpa0tiYWw2T2laKzNyWU5VRWRZNmJCT09FNktMY3I1cXBTaFhOUzFpZkxxbUE1QUo5TjQ5NDRWNEZJUmRXTkhqM21MQXdHbCtxamRyU05PMEkvV250UjdGQjVWUCtBekZndVdMUVR4RCsySjlCd3pXb1lQL0tMYmxpU2Rld01vd1BnVzBUdWw5Q3RqS3pobDNtOWd6Ukd0U05JWnlvVFBEZEJ2eHJ0aVJudmhYZ3pTNVFIM1RFVVkwVW9lQnU4a2d2V0NZTjdBSGRsMUtWazdRaGpxWmF1WSszZW1pTVZ0V3dqdG1ocENXZ3oybXQ3VUllU1hlSXBvZVNXbytIM2g3N2NGcThJNzNDdm1ZY1F3TFlBcUpkeWI3T3VIVHJmTWt0MUJVb2RpSHVJQ1dPb05PcDJPdlg1Y0M2eDc1TFZVamlXdmx0N1gvdTB3STVaNjM4YXFWRlU0WktWVHZQUk50c2FUWDd1SW5OWDF4blh1dFF1d3hjVzBmVnU3ejdad3BXWVZPWi90QjBQM0tyeWNBSS9ITERsaW96Tmh3MzEzSk9hY0t3TGxiQ0hwUmdIU0FZMyt2N3NvTFJrQTd2aTFyd0ZhaHk4dkxMdUsrRkJ4OFROd1A1RFppWk56d1NVTmtGMWZYM3laZUVlN2kiLCJtYWMiOiIwZDUwNGVlMmFkZmEyODNmZDI2YTZjZDE4ZjZiMTc1NGYwM2Y0MzQ5OTk4ZjNjYWIzYjA0NTg1YjUxMDkyYWFkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:51:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVsckptaXhCL1lnNlB6Z2FURWxncVE9PSIsInZhbHVlIjoiNkhPWEROaWFsanpCMlU5RW5LVmV1cXBlOGpkSWJKVWdFOFc0L1llcUp2c3hsQnVGVTRJVjdNZHluYVkxcU02b0pyN3ZocUpjVHl5Z3pxLy82eDNVOUxlQXJLYm1uQzgxSWRxKzNwOE9UdnRMTmZ3SENaL3dWcHFiMGpQakMyWUY4M3ZSbmppd2ZGajZsVDFBVUpYMEFMb2RqbmJ2eGRmS2g0aSszZkF4SDA4NVE2UkFRelM1MGtOd2lLekYzbjlYQTVWdVA1UVkvL3I3QnU0eGY3QU1tSG9nR2JJVGt6V1J1WHA3amc2cDZDaTF0alR1VmNpZzNNVzJPYkdDZEtCbVpLS2lvYzlTelpld3lsd1ZTL3hEUlR6UFNqWmRGZzllMjZuUU9LQXpGcHUvZXNlZjZoSEQ5Y0dGMVBPaTNwVm1jMXk1Z2FMcStUbnFWUzVhejIzZ2VGb2FnTlc0YjJqdnpzRzJxdzlJNnJzaENOWEZ2TlRzSmdHc3BWNVQ5N215U3RyMkhFUDRPOWxZUmxjOXRyOFJPaVgrNUl0Y2FXZVBpeWhTaks2UCtGVVQxZ2liSDhxckhFZlNFNjdPRXNXZGdydjJVSzVUb3VXYXlRclU4clNaODFvL1YrczduemNjcjBIVGdFQmZrYnN2eW9RNWdPS01lNDNSbkhMdzlVRWkiLCJtYWMiOiI0MmVjMjdmZTdiNzhmNDQwOGU2YTZhMDExNGUzM2IxNzBhMzE1YjU5NTY3YWFjN2QzNjAzYzQ1MTMwN2Y3MjJjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:51:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244511592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-569363345 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569363345\", {\"maxDepth\":0})</script>\n"}}