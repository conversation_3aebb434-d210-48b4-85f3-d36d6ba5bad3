{"__meta": {"id": "Xed7cf3d27b8f1e2f2da616c7acfa4621", "datetime": "2025-07-31 16:55:26", "utime": **********.943284, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980925.164257, "end": **********.943314, "duration": 1.7790570259094238, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": 1753980925.164257, "relative_start": 0, "end": **********.690705, "relative_end": **********.690705, "duration": 1.5264480113983154, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.690814, "relative_start": 1.5265569686889648, "end": **********.943318, "relative_end": 3.814697265625e-06, "duration": 0.2525038719177246, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421568, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00951, "accumulated_duration_str": "9.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8644888, "duration": 0.0062, "duration_str": "6.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 65.195}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.899074, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 65.195, "width_percent": 14.826}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.909439, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 80.021, "width_percent": 19.979}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1161413134 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1161413134\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-600549802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-600549802\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-949289183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949289183\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-950710746 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZ5NFgyU0NFc1FwYTgzVFNNUjIvVHc9PSIsInZhbHVlIjoieFMxaGQ3RVpPK1p5MjJZejBrcERLaHgvTEFqREU5OTN3ajM2T0dTVDIzNUNyaGZlU1dUN24zTEkrbUlIUHRNTjAyWENKVEs0a1JzWGQ5RGk1ODAxSjRkdTlnQXJyMzRSQnRPTXNkZjdUbmRqaWR0RDJTaTVoajNQU1RUdXNVa2VIaVpBR3pjeTZSb21LemlXNXF3R1N0TEo3cTlsVUxOQnEwWE0waHVaeGJ5LzZ2SkZ5VmZnOFdXUmdlWUx1ODVRMWZWUUhaRFpQWGhjeHl4eTB2VTI2Z015U2I3SDduY3MraUtxaGJwbjZZTlYxNVA5Z1lBREZZeThJcStlTHFUbVltNXRDU0RxbjNhamppSnplUEdCZTM2dVkvaENqR24ycTFxWGlONEVPS1dmblIvSHZmMWppOGJmNTFTUEl1dlkrM1pJSDdOUmdoUVcvMGM5THF4VkE3QUZScFhRMGttYlNtelFBNFhQc0RvUkMwUzVvRjhiZExwd084OEpjMTg2aEZRNTZsTFlZSUJGcy9mQmNJRUNxR0NrcnFXVjJ2amZPbjZVYzRkUklpc3JXbnNSRm42dlJmYkJrWC9WQzFDejVxMXJQWERlODh4MFJZbXltOElIekFuN2FoV0cvNkhld1NSRmlpeW81RkJXc01nY0FmaTZnL2VVSFBLYkd1azQiLCJtYWMiOiIyNWI1YjlmOGNiOTJlODg1NTRkYTZmZjgxYWU5OWE1MWM0NWM0NGVlZWMzM2EzMWY1NWRjYjlhOGEwMGJjODZkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im95OWFBQVUyL1RkcmpqWkNWK3V0TkE9PSIsInZhbHVlIjoiY1dyU3hTVWhoRHMwQmllcXE2V0FuNVpHcGRFN1JiMFhGb3Z1UEtESmgzUDIyYWlCRVl0aTZkbGpqVXdGb1F3MDFIMndWZTBnNVkyVEhJZzRhMVRDaURhZTFiUmtuclRNK0FDVUcwSHRMZXZnTjRFTnh3MmIvZUx5UUkwYTkzUWdDaUNCelYyYktKMEFOTUxPMmpjOEQ3b3hCL3MrQ3JMVEVOTE1UV0trYjRkTi9LNVl0U1grZjR2WGZFNitNM0J5ZnJCd2F5UXV1ZnBVSWVpdE12MzBVcHFqNXBxdTh0MWtFbnNmSnNObFk1VGFVZkJQblA2RTI4TUFSbnRWaC96ME1lTGc1VHVxbFZUdzJtSlVHc0sySml2bGNEN1JyWVp2cWFJTFdWK2VEU28xSE9PYnZjNTYwM1pSUnkwQVExSnVReHdmc2sxb3dXU0N2THdNQnBnTm9WZDBJOU9OWlk5MURDZmRtVW9RTUpkanlrbmJGMFBFUU5zRGlVNGNqQTlWWGhjL3dmSFVOTVZ4cWhCdVF5OEZZVGhCMldlR0l4RlI5SldNcTZNV09SL0NsdDZMZGRReFZmSTM2b2JudmJlVUFhN0dLNlJOTmVqZlN1VUJESjVaNTNtWmRtR0lWcXltRkxTRmU3MWxLRHNwVDlIeGd3YVJMN1gyS3A5NXBQTVMiLCJtYWMiOiJjNTA4ZDg3MDg5YTJkMmQ1NDIxOTI1NmEwNDZhNzgzMDljOTk1MzUzZDQ0YTczMzVlY2ZlNGU0N2JhNTQ2Y2MxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950710746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1276886436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276886436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1180868500 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:55:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZ1Uis5UWFCR3Ayb0RaQ0JKWk5hZXc9PSIsInZhbHVlIjoidG9WTGNjNDdzVklEVlBvQXJBb2UzRWpFN3FKeG56UWdyRWEyZWEvSktRRUs2WDdPUVFxZVpNdTZxSTBoNWI5VkU1R1lvZUJBQVFEc09YUXVHamNJczdBRDlnTUtsMzFzbmtpRUJiaTJvbmRmS3hwZXlzN1c3L1lpeWRKQlBhKy93OWl1cGpWTDF2UHpCSVFDVmlmTWhnMUZ5ZTVWeklvNDEzcU4xVlNHUXAySCtZVk1pd2tpaVBCV2ZhK21nSUNLRmw2dzg0emlpT2craUhNN0pod0g2S0gveW9GS2xLK1AxVnA4VXJXZ1FKeDExWHgrOUt4S3ZmVy9SaXJlY1R6d2huN1NWV2dtRUlHc2krdEdzc1BHc01DaFRXT2tWZUNoQXNEVVFjcHRsRE1VYkQ1eVVmbC9iK0ZxOVRiL0ZMVzYzYTR6ZmF3L3VZVk1NRG9vUXQ5VXZGRVV2WnpwdG42WVEyMWJxcUFWTlJsaGh1MHRPR1d4SThFTkI2cWV6OHU4YXIrL1Z6OW1vNnFyTm9lWHJDWE1GY3VCcStiRndycFVSaEdDaXBNZUdjckhoTXd3c0poTUR6ckFMWUl6WS9mbkRSeC95UDJKM2UzRjlGbDE5cm9iZGx5ZkJ3eHhXUllXQnEzNFUyUHNSelYrWU9RRndQN1AxK3pmMDJNMHhPRlYiLCJtYWMiOiI4NWQwOTM3MmE4ZjI5ZGRhNzlmODM1ZTQ0ZGE2MzAxNzJmNzVjODg2ZDVjMzUzYmUwMWZjMzA0NjdiMDgwNWNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkswNGRnT2xwV2NMOHhhSVh2Nno4TGc9PSIsInZhbHVlIjoiWjZoRFE2VXNVMHFJRmlpYTVzdEZVaXNNMk9vdTAyT2FVQmUwcE0yUGdjWU1ndjNMam9BZnJmLzl0Uzk3TkIxaGhSZ2R6cjNaQWJHU3VKQUNFNXRUUmNUWVJDMFhwL1hCWlRSUTZMYi9pSUpGM1R2d2U2UWduTlpaYkloaUROdHlaTUJrREVZR1ZHWnZETTI3bmM5dVF6RmZPZ0JNMUUvbzhKS1ZtbGIwSk9tbEhTR1NGZEVXeStBbGZraTJJQVpsYVBXUWRTOFhxdXR1MTdRQlFNZHRIK0pVd0lXSE1HOVpLSEdJTkNjRnVRRGdPZCtXZmtlWkQyNFhBTzRVM3Z6RHBZWE5LLzJJUkVQWDhQcGxZTU1WZFJWNVRKajJVeGZSZ0ZpSXF1V3poWXV6YXVJcWZKNFV4cXBrbGRKYWc3cWZ4YW5zMS9rQ0RtOU94eDNLUCtnZklOZUVOR2RlMVRxVGtaeEFvWXlFTVpJenRDS2hjaVZtRXRKL3F3SHdWMVlmSVZPWXN0c1hEbmw1THdjanptRHV2Nk1YZVdGZ05OLy9CQ3ZiZS9ST05xV28rdnlDbnBDWWJYbm43L3FvaFYwdk5HTjkxNGl2RThNSXJyemcrRk9OaXFKcGszQXU5SmRrcGJZTHcrS3pTdXljZkszSDFYeFdyRlg4ajlsaVVtUDMiLCJtYWMiOiI4MzQyMzA5OTkwYTM5MzM2OTQ2NjcyMTA0MDBhNDM0ZGQwMmQyZTJiNzUyMDhlY2Y4MzUyYzc4MDQ4M2IzMDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZ1Uis5UWFCR3Ayb0RaQ0JKWk5hZXc9PSIsInZhbHVlIjoidG9WTGNjNDdzVklEVlBvQXJBb2UzRWpFN3FKeG56UWdyRWEyZWEvSktRRUs2WDdPUVFxZVpNdTZxSTBoNWI5VkU1R1lvZUJBQVFEc09YUXVHamNJczdBRDlnTUtsMzFzbmtpRUJiaTJvbmRmS3hwZXlzN1c3L1lpeWRKQlBhKy93OWl1cGpWTDF2UHpCSVFDVmlmTWhnMUZ5ZTVWeklvNDEzcU4xVlNHUXAySCtZVk1pd2tpaVBCV2ZhK21nSUNLRmw2dzg0emlpT2craUhNN0pod0g2S0gveW9GS2xLK1AxVnA4VXJXZ1FKeDExWHgrOUt4S3ZmVy9SaXJlY1R6d2huN1NWV2dtRUlHc2krdEdzc1BHc01DaFRXT2tWZUNoQXNEVVFjcHRsRE1VYkQ1eVVmbC9iK0ZxOVRiL0ZMVzYzYTR6ZmF3L3VZVk1NRG9vUXQ5VXZGRVV2WnpwdG42WVEyMWJxcUFWTlJsaGh1MHRPR1d4SThFTkI2cWV6OHU4YXIrL1Z6OW1vNnFyTm9lWHJDWE1GY3VCcStiRndycFVSaEdDaXBNZUdjckhoTXd3c0poTUR6ckFMWUl6WS9mbkRSeC95UDJKM2UzRjlGbDE5cm9iZGx5ZkJ3eHhXUllXQnEzNFUyUHNSelYrWU9RRndQN1AxK3pmMDJNMHhPRlYiLCJtYWMiOiI4NWQwOTM3MmE4ZjI5ZGRhNzlmODM1ZTQ0ZGE2MzAxNzJmNzVjODg2ZDVjMzUzYmUwMWZjMzA0NjdiMDgwNWNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkswNGRnT2xwV2NMOHhhSVh2Nno4TGc9PSIsInZhbHVlIjoiWjZoRFE2VXNVMHFJRmlpYTVzdEZVaXNNMk9vdTAyT2FVQmUwcE0yUGdjWU1ndjNMam9BZnJmLzl0Uzk3TkIxaGhSZ2R6cjNaQWJHU3VKQUNFNXRUUmNUWVJDMFhwL1hCWlRSUTZMYi9pSUpGM1R2d2U2UWduTlpaYkloaUROdHlaTUJrREVZR1ZHWnZETTI3bmM5dVF6RmZPZ0JNMUUvbzhKS1ZtbGIwSk9tbEhTR1NGZEVXeStBbGZraTJJQVpsYVBXUWRTOFhxdXR1MTdRQlFNZHRIK0pVd0lXSE1HOVpLSEdJTkNjRnVRRGdPZCtXZmtlWkQyNFhBTzRVM3Z6RHBZWE5LLzJJUkVQWDhQcGxZTU1WZFJWNVRKajJVeGZSZ0ZpSXF1V3poWXV6YXVJcWZKNFV4cXBrbGRKYWc3cWZ4YW5zMS9rQ0RtOU94eDNLUCtnZklOZUVOR2RlMVRxVGtaeEFvWXlFTVpJenRDS2hjaVZtRXRKL3F3SHdWMVlmSVZPWXN0c1hEbmw1THdjanptRHV2Nk1YZVdGZ05OLy9CQ3ZiZS9ST05xV28rdnlDbnBDWWJYbm43L3FvaFYwdk5HTjkxNGl2RThNSXJyemcrRk9OaXFKcGszQXU5SmRrcGJZTHcrS3pTdXljZkszSDFYeFdyRlg4ajlsaVVtUDMiLCJtYWMiOiI4MzQyMzA5OTkwYTM5MzM2OTQ2NjcyMTA0MDBhNDM0ZGQwMmQyZTJiNzUyMDhlY2Y4MzUyYzc4MDQ4M2IzMDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180868500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-209279587 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209279587\", {\"maxDepth\":0})</script>\n"}}