{"__meta": {"id": "Xd89b6768a00ca3b9766d62a416e731af", "datetime": "2025-07-31 15:51:24", "utime": **********.71958, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977082.555402, "end": **********.719631, "duration": 2.164228916168213, "duration_str": "2.16s", "measures": [{"label": "Booting", "start": 1753977082.555402, "relative_start": 0, "end": **********.086034, "relative_end": **********.086034, "duration": 1.5306320190429688, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086068, "relative_start": 1.****************, "end": **********.719637, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "634ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "vNAJK2ibz5r4upfiVsmL1cM8rSkpf7d0lahkt6Fz", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-296333623 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-296333623\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-963578184 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-963578184\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-67445470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-67445470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1036795307 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036795307\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1329962924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1329962924\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1356724603 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:51:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNGa2VlbGRTVUxUdEFCc2piNTY2Y2c9PSIsInZhbHVlIjoiTUVyRVg1ZlFhUStlL0JmOUNMcjQyRStxZU8zMVB4Nkx2NVcrdzRvYlpmeFAybjRiNU9GK2dFUjdLeXR2VTJvc0V0NDJ4ZjRRYkdmNWlOVTBWV0pkWUJrU3h4VWdGV3FQOWJzVmhvb2svVC9aTFZYcTZHZUlnYXJzUkhTSzVaeXg0L3lmUk00MkgzWWZHZ0U4Tkc1N3h0SGtIa2RaY3BNdEJ1RGNPR1F3aUVQdHM1N1VldFk0WXhtOVlGRFFrY0tPY2Q0bHhQUGFzRm4vM3c0U24yT2h0eE5maXlScWNjOE5zbC96SEhHa3Z1M0oremg1OENFaFUrSWVUSzRMVVFPNDRFUDBGV3YvdTJFSTZnRU44VHloTUV6UGlkclBMQzFqZnVQNTgxOFdpKzZCbGpVczZOOENPVlc4TGtSNTh4bHlWRFhDRy9wa0ZTQ0VjT2hqTFo3bi93eHJWdWlrSG9WM1NSb1A4a2I5aFZ4MGt1eVdFa3JFQWhPSWgvMkQvRVFOZWxJR1czVGI4RnhkZFY0d0FOZDlibmpBaWc4UDBVUENFUVF5eHRYc1JSQ0JnYjI1ZWJkbWtlTFREUHkwZldoZTVyVlpKZzEzVWNoWG5hZ3BWY25sZzV5dWFkUng1L2ZNdllDRlA1Rlk5dEpta3ExQVpPaExzOStCOWoyUVVWbk4iLCJtYWMiOiJhYzJkMzAxMTQyZWE1OGQyNjg3MTRmYjQxNDM1OThjNzI3NzA2YjA0Y2M0MGY0ZWU1NDAyMDgzZjM3ZDVhNmQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:51:24 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVHWUxTMVkxUzJsWnVEOHIvZ2VUOVE9PSIsInZhbHVlIjoiTjFKZHo0ZXlBU3hKMzM2a2Y2SStHTFNJUk1GT05uY3BIbUp6cDJDVkEyTUkyMDQ0OTN1N3dpZ1RqVExacFMvcG90MjhoWVFOakNtN2w3MzRUQmRaZFkxckZnRW1wQUh0ZG1hR1N1a0V4TTQvUisyb09pNjJFVFNVYU5FMkRPTjBwQm1xVGxZRU04TS9qQ2hQRzJHeFJ0b3pGT0E1c1czRkRIYWI0UmlJdW94WmltWGtNZHlnQTVGRFVtdHdGNFoyOTJ5MnpBdGVTejArTVJpVTJCOVUyclQrUW10UERYb3RZSnAwT0EwR2NERys3WktTN1BJbmhydmF1b2FtV3dTZDlZTjNMZlhpSCt3ZG5pN1BwWVZTYVZsb0tkdXM1V1RpaUZBZWtJcWFYMTFRanBUZS91MUs5bUIvSnBkMGlkVUg5WHRTcElYbWRhT1pHTVd4cDRDMHpYMUVvOGUvRHRjNlVtdTdJOGxkbnNvWldVUEVKdEJhSnhEZ1k2ZmNSK1dRZWt5eGdySEZscmtKQk5aemZxUmRKcHJGajlZc01vblgxVzRLYjZ2VlBUUDg3dE5IYXd1SXEraXczVXBaMXYwNGl6bUt2ZkpKNzY1UWxjMWRqMGNlZVRVYU95V0JkbGVETEN6YTZZM3VXanhWRzNwYnJHMVJPS01wbTNvMW9rSCsiLCJtYWMiOiIxNmRkZDkyNjAzMjA3N2RhZmZlODUwOWMyZDE3NTgwMzgzMTdmYmQ2ODhlNWNmMWM1Mzg3YzViZDJhNWRjNjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:51:24 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNGa2VlbGRTVUxUdEFCc2piNTY2Y2c9PSIsInZhbHVlIjoiTUVyRVg1ZlFhUStlL0JmOUNMcjQyRStxZU8zMVB4Nkx2NVcrdzRvYlpmeFAybjRiNU9GK2dFUjdLeXR2VTJvc0V0NDJ4ZjRRYkdmNWlOVTBWV0pkWUJrU3h4VWdGV3FQOWJzVmhvb2svVC9aTFZYcTZHZUlnYXJzUkhTSzVaeXg0L3lmUk00MkgzWWZHZ0U4Tkc1N3h0SGtIa2RaY3BNdEJ1RGNPR1F3aUVQdHM1N1VldFk0WXhtOVlGRFFrY0tPY2Q0bHhQUGFzRm4vM3c0U24yT2h0eE5maXlScWNjOE5zbC96SEhHa3Z1M0oremg1OENFaFUrSWVUSzRMVVFPNDRFUDBGV3YvdTJFSTZnRU44VHloTUV6UGlkclBMQzFqZnVQNTgxOFdpKzZCbGpVczZOOENPVlc4TGtSNTh4bHlWRFhDRy9wa0ZTQ0VjT2hqTFo3bi93eHJWdWlrSG9WM1NSb1A4a2I5aFZ4MGt1eVdFa3JFQWhPSWgvMkQvRVFOZWxJR1czVGI4RnhkZFY0d0FOZDlibmpBaWc4UDBVUENFUVF5eHRYc1JSQ0JnYjI1ZWJkbWtlTFREUHkwZldoZTVyVlpKZzEzVWNoWG5hZ3BWY25sZzV5dWFkUng1L2ZNdllDRlA1Rlk5dEpta3ExQVpPaExzOStCOWoyUVVWbk4iLCJtYWMiOiJhYzJkMzAxMTQyZWE1OGQyNjg3MTRmYjQxNDM1OThjNzI3NzA2YjA0Y2M0MGY0ZWU1NDAyMDgzZjM3ZDVhNmQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:51:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVHWUxTMVkxUzJsWnVEOHIvZ2VUOVE9PSIsInZhbHVlIjoiTjFKZHo0ZXlBU3hKMzM2a2Y2SStHTFNJUk1GT05uY3BIbUp6cDJDVkEyTUkyMDQ0OTN1N3dpZ1RqVExacFMvcG90MjhoWVFOakNtN2w3MzRUQmRaZFkxckZnRW1wQUh0ZG1hR1N1a0V4TTQvUisyb09pNjJFVFNVYU5FMkRPTjBwQm1xVGxZRU04TS9qQ2hQRzJHeFJ0b3pGT0E1c1czRkRIYWI0UmlJdW94WmltWGtNZHlnQTVGRFVtdHdGNFoyOTJ5MnpBdGVTejArTVJpVTJCOVUyclQrUW10UERYb3RZSnAwT0EwR2NERys3WktTN1BJbmhydmF1b2FtV3dTZDlZTjNMZlhpSCt3ZG5pN1BwWVZTYVZsb0tkdXM1V1RpaUZBZWtJcWFYMTFRanBUZS91MUs5bUIvSnBkMGlkVUg5WHRTcElYbWRhT1pHTVd4cDRDMHpYMUVvOGUvRHRjNlVtdTdJOGxkbnNvWldVUEVKdEJhSnhEZ1k2ZmNSK1dRZWt5eGdySEZscmtKQk5aemZxUmRKcHJGajlZc01vblgxVzRLYjZ2VlBUUDg3dE5IYXd1SXEraXczVXBaMXYwNGl6bUt2ZkpKNzY1UWxjMWRqMGNlZVRVYU95V0JkbGVETEN6YTZZM3VXanhWRzNwYnJHMVJPS01wbTNvMW9rSCsiLCJtYWMiOiIxNmRkZDkyNjAzMjA3N2RhZmZlODUwOWMyZDE3NTgwMzgzMTdmYmQ2ODhlNWNmMWM1Mzg3YzViZDJhNWRjNjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:51:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356724603\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1638827750 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vNAJK2ibz5r4upfiVsmL1cM8rSkpf7d0lahkt6Fz</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1638827750\", {\"maxDepth\":0})</script>\n"}}