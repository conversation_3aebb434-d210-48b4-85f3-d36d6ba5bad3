{"__meta": {"id": "X3496464c9667c8d9f3fdc95d63c6a330", "datetime": "2025-07-31 17:06:33", "utime": **********.746443, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981590.941639, "end": **********.746496, "duration": 2.804857015609741, "duration_str": "2.8s", "measures": [{"label": "Booting", "start": 1753981590.941639, "relative_start": 0, "end": **********.446487, "relative_end": **********.446487, "duration": 2.504848003387451, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.446522, "relative_start": 2.***************, "end": **********.746501, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ox9Pm1EVTAFz9ePPcKnHUxm9QTXnjOuhLt2pobwL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-556422748 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-556422748\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-690070235 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-690070235\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832643966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-832643966\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-929071841 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929071841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1931398718 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1931398718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1271725186 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:06:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilk4YS9Rb2xBMTA2L3EveGRmditOSHc9PSIsInZhbHVlIjoib3krRHNXV3RXMmNnQml3RjNJKzdTWDU2UGdNMm5BcUJ2OXRDUy9NU0J0aFN5dktmbnlhQXBtM005c0xLNUhCWSt1THlnbTIrVDc0ZkNVeUtyOWJBbU9Sdmc1VUF5U3IzdndQaGYyWVVkTXFPSUZ0MkpNQXlaQ1hXUUQ1bjJvc3Z6Y2NnNHNrbXlQWDc5YmQwYVk4TVdycUhIVmhXbFFjU1B5RG9hUk1jMkpoNmlBekRab2tidEozcFBFb2d3bkhjYnllSEcybG9CcGxLMnlzUm4rMEtsR0RVcjRBYmxFcy9kZGV3YjFlSDU5QlRwY1c4VEpyRXFDU2MvNGlQZ01FZFFtaEpNWjh6ZlQrdlFoaG1ZVnVTZU90aW1TZE9WU3JLek5DUmhTS0hKdTlsN2k2ZzZZMkhkaSsrVTZZN0ZCQVM2VUtBaDhuZ2kzRGsrNzA2MDBVcmpQNXNFa3ZNNkhveVZkeVplcnJiZHJWeEMxZWM5ZWV4ZUFpQ3ZaNGkvYjVVdS9PNDY4UU5QUkRPVGdnRG4yV2I1RXpsRXplVFUwNGp2VTRBMG1wNFE2UzNVekpmNTlSQ245SmwzN1dSRjd5Uy9pblVsbmtTSHdTMmVUbHN2c0JqWkQ5eHBtVFM1QmZhdUNkamo3S1NBalhiNUxSRmlZWHRKNjJKc2ZwN0FhcHAiLCJtYWMiOiJlYTdiODhhYzE3OTUwZTg0NzNmZmZiY2NmMmEyNjI2MjE2NzU5Njg2OTExNDM2MmU1NTI0NWViZWQwZDNhNThiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:06:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtsY0xyaFl2ZC96R2hocVJNdTBGMnc9PSIsInZhbHVlIjoicHdySWxZWFNMVVpBSUJNaHF2TWh1TUhTaTF0b2dJczkwVENCVkJMdVZwZUk5b1BMWFhBdU1VR0JCT2w5K2N6N2N2Mzkrb3pCOXVDcnZCdldEakJ3a0pPY0tlSlVERkpzQVM5TGNFODdPMVplRUFnbVFkTXFNQk9SRG5PN3dRR2llVlZuRzVablRudGZoMkpHck9VTVNualhLS1FTSjFaa3RlcytDUHdPVjBFR1dqZzEwV1lwOTB0Ry9EMlZrd3pKMTdkMU1DbFRIWmNBMk1RNWJUSnROZkhBaEU4d0RqZDg4K2hsUlAwTjdRL2gzWUhNWEF4QjlaZUhoeHpNQjdGUk1sRFgzL2RHMGV3azAzc3g1VmJPL3Zya2JRZ05SN3BBVUd0TUU3NFpKbFhOeFFsS2lDQXlQbmRBQzJFMlp6ZkFRdWN5Q051cEZXTGNMeTRQZksraWtHQTA5MDZ5aTJnVEVIT21iNEpWcmtIVjhuUmRnTUxtVjV1amoyZ3IrZVFNenBuNFZwK2d1U1l2b1pFVlhPMkMzTHlJQXRzRE1WVXlVOTlWTXhSSnUrZWYwOXJrT2Y1SS85SjhRYVVXcDByN2MzandDZEtqSjBBdXJLRVdDRUF2eU9IM25CZmFFbkN1V0pNYVVibzFObXJseVhPRjdwa2RkS0VEOHB2RnRHdUYiLCJtYWMiOiI0MTdhMmU4MjNhNTQ3MDRhMWQ4ZTRlM2EyNTA1YTVmMTEzOGQyOTNmZTg1NTU5NjIzZmZiZGU4MzE1MGI2Y2IwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:06:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilk4YS9Rb2xBMTA2L3EveGRmditOSHc9PSIsInZhbHVlIjoib3krRHNXV3RXMmNnQml3RjNJKzdTWDU2UGdNMm5BcUJ2OXRDUy9NU0J0aFN5dktmbnlhQXBtM005c0xLNUhCWSt1THlnbTIrVDc0ZkNVeUtyOWJBbU9Sdmc1VUF5U3IzdndQaGYyWVVkTXFPSUZ0MkpNQXlaQ1hXUUQ1bjJvc3Z6Y2NnNHNrbXlQWDc5YmQwYVk4TVdycUhIVmhXbFFjU1B5RG9hUk1jMkpoNmlBekRab2tidEozcFBFb2d3bkhjYnllSEcybG9CcGxLMnlzUm4rMEtsR0RVcjRBYmxFcy9kZGV3YjFlSDU5QlRwY1c4VEpyRXFDU2MvNGlQZ01FZFFtaEpNWjh6ZlQrdlFoaG1ZVnVTZU90aW1TZE9WU3JLek5DUmhTS0hKdTlsN2k2ZzZZMkhkaSsrVTZZN0ZCQVM2VUtBaDhuZ2kzRGsrNzA2MDBVcmpQNXNFa3ZNNkhveVZkeVplcnJiZHJWeEMxZWM5ZWV4ZUFpQ3ZaNGkvYjVVdS9PNDY4UU5QUkRPVGdnRG4yV2I1RXpsRXplVFUwNGp2VTRBMG1wNFE2UzNVekpmNTlSQ245SmwzN1dSRjd5Uy9pblVsbmtTSHdTMmVUbHN2c0JqWkQ5eHBtVFM1QmZhdUNkamo3S1NBalhiNUxSRmlZWHRKNjJKc2ZwN0FhcHAiLCJtYWMiOiJlYTdiODhhYzE3OTUwZTg0NzNmZmZiY2NmMmEyNjI2MjE2NzU5Njg2OTExNDM2MmU1NTI0NWViZWQwZDNhNThiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:06:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtsY0xyaFl2ZC96R2hocVJNdTBGMnc9PSIsInZhbHVlIjoicHdySWxZWFNMVVpBSUJNaHF2TWh1TUhTaTF0b2dJczkwVENCVkJMdVZwZUk5b1BMWFhBdU1VR0JCT2w5K2N6N2N2Mzkrb3pCOXVDcnZCdldEakJ3a0pPY0tlSlVERkpzQVM5TGNFODdPMVplRUFnbVFkTXFNQk9SRG5PN3dRR2llVlZuRzVablRudGZoMkpHck9VTVNualhLS1FTSjFaa3RlcytDUHdPVjBFR1dqZzEwV1lwOTB0Ry9EMlZrd3pKMTdkMU1DbFRIWmNBMk1RNWJUSnROZkhBaEU4d0RqZDg4K2hsUlAwTjdRL2gzWUhNWEF4QjlaZUhoeHpNQjdGUk1sRFgzL2RHMGV3azAzc3g1VmJPL3Zya2JRZ05SN3BBVUd0TUU3NFpKbFhOeFFsS2lDQXlQbmRBQzJFMlp6ZkFRdWN5Q051cEZXTGNMeTRQZksraWtHQTA5MDZ5aTJnVEVIT21iNEpWcmtIVjhuUmRnTUxtVjV1amoyZ3IrZVFNenBuNFZwK2d1U1l2b1pFVlhPMkMzTHlJQXRzRE1WVXlVOTlWTXhSSnUrZWYwOXJrT2Y1SS85SjhRYVVXcDByN2MzandDZEtqSjBBdXJLRVdDRUF2eU9IM25CZmFFbkN1V0pNYVVibzFObXJseVhPRjdwa2RkS0VEOHB2RnRHdUYiLCJtYWMiOiI0MTdhMmU4MjNhNTQ3MDRhMWQ4ZTRlM2EyNTA1YTVmMTEzOGQyOTNmZTg1NTU5NjIzZmZiZGU4MzE1MGI2Y2IwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:06:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271725186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2009610398 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ox9Pm1EVTAFz9ePPcKnHUxm9QTXnjOuhLt2pobwL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009610398\", {\"maxDepth\":0})</script>\n"}}