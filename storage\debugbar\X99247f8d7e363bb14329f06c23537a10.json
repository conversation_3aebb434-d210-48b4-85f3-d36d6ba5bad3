{"__meta": {"id": "X99247f8d7e363bb14329f06c23537a10", "datetime": "2025-07-31 16:55:06", "utime": **********.630267, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980905.052754, "end": **********.630349, "duration": 1.5775949954986572, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1753980905.052754, "relative_start": 0, "end": **********.381476, "relative_end": **********.381476, "duration": 1.3287220001220703, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.3815, "relative_start": 1.****************, "end": **********.630354, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.022260000000000002, "accumulated_duration_str": "22.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5254362, "duration": 0.022260000000000002, "duration_str": "22.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-245436351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245436351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-474710440 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-474710440\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1291785305 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhOQVk3N21EZWd3aWRSY1djanVxRlE9PSIsInZhbHVlIjoibVVNU21KU1pZdms3NHdVZDVXS2duT2tkb1BxWXdhNk1TdUo0ZG1wVlZvOXFua0FmSWdEbnNKRHhYY3NHUGg0dkFydXh0VDlIY24yOHFuSEhMeEVPU3BrZUFpUGNWUDZqOUNIbHZVNFUxa1JQUlYyejU0aS9xNmM3M0JCNmdOSXJCL0szVmswZVRTL2pSeEp0RGV4UmEzcEhnTjYwWkx3V0FHQm1hMnJqelJnUDdwbHlXcHVmRGFyMTdhUWZhVmQzOE9Sb3FwWjN0NlBhYzBWcHFsbVVqU2lZVW9LeHhIalh3SkZwdlQ3L2xHKzhhWHdvRysybkRmZ2gwd2NESEJISk5lRFFKcFRqWFhSa202aU1leG1lOFNQZjdEYTlzV0l0Vjhra2JBSzF4Ulc5eEtXT2l2bUVjSENMNUZMUW5aZEQzTzMzQUplWlRnM0JhNG1pOU4vNU45amVBQTIyd1Nrdm15d3ZYVzZaaDdyN0VHZHE0WkJYYUtxQUw5akwxL2dVWk94eWU2S3JCSUFUM1Zmc2dhY0xTaS9rK1hEWmdDejJ4ZGNNd0hQNVpBcWpEMi9BTjRrYmpSVVpxRlBNeUJ2TGVCclhpaUFnbC9UNSsreXE2cmFoNFhraUFOOVprTVNIYWRaREhwelg2WWE5MnpTU3ZMalNpL0t3bEFHN3BSNkUiLCJtYWMiOiI3NmE3NzJlYzkzM2Q1NzVkNTM0ODc0N2JmZTQyMGEwNjE0YjUzODI0OTg5YTQ1NzRhNDIwMjljZDA1MzRlOTQ5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkhqQU5DSmpVZEJCa2sxbk54cDc2VlE9PSIsInZhbHVlIjoiNmRicG8xdDlBdEtWcTV6eHVFZng3clc2Nmw2NHJvT2Rna2pkYjdPYWwxbXJKV2p0SzJzMXdaM00yaVJhR3U5aWxCeVo3T3d2QlBPZjVoZEtRVnpUMG5RRHdMTHdUNWpFZk5KdGk5azVXNTVyNVVDOFBIbmdKNUNwblNFMFVIN1dxd1ZmYUVEMEFRUzJDYTMyS05RY2pKYzdGL29DdmJDaithV1FPcjAxTE9xR3NtUHp6V2ZtU0s1VGZLeTVxYzlJR2gyRHJXbDN5NU02aG9WcDE0OWlTcktJRXVFTFFjZXdnZ3ZaRVFPd0xVUHhBQmI4KzlvY3h0S1ZpQ3grUXczT1BOVkJQVzR5VEJwV0ZHT1VSeVNyT0V6dEZacGdrVnhOck1pSDRpUTFOc3VqLzJsdXMycmFOWDV2SHlhN1RkUmVyajhya1UxYTVXdytyY3V4WmRIR3cvc0hNbjJDM0Z4SkVLOHdpSWYxRE9mV2lmYWdzZ2w4dUdFVUREUkZtdmkwR3dJRUtTNHNXaDIydFgyNTFCWThZU21PVEp3ZHBDRXFEbTBHUkl1UTFHd0lOMllFcHB6M2tzNzRmUW1tci9OM3RRSG02WXlkWXVHTnVtTFV0Z01kaDBkREpzN0NzTzBvOEpjZ2JyUTJ4NytNTmN1VnYraFBnbTBKSThtbUd6TzYiLCJtYWMiOiI3NTlmMzJmNzJjYmJiMjliZWY1YWEzZWZlMjUxOTA1N2E1NjU0M2Q5Mzg2MGE3ZjM5ZjNjOGI3MWIxN2U3NGYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291785305\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1522475192 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522475192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-975162954 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:55:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ5NFgyU0NFc1FwYTgzVFNNUjIvVHc9PSIsInZhbHVlIjoieFMxaGQ3RVpPK1p5MjJZejBrcERLaHgvTEFqREU5OTN3ajM2T0dTVDIzNUNyaGZlU1dUN24zTEkrbUlIUHRNTjAyWENKVEs0a1JzWGQ5RGk1ODAxSjRkdTlnQXJyMzRSQnRPTXNkZjdUbmRqaWR0RDJTaTVoajNQU1RUdXNVa2VIaVpBR3pjeTZSb21LemlXNXF3R1N0TEo3cTlsVUxOQnEwWE0waHVaeGJ5LzZ2SkZ5VmZnOFdXUmdlWUx1ODVRMWZWUUhaRFpQWGhjeHl4eTB2VTI2Z015U2I3SDduY3MraUtxaGJwbjZZTlYxNVA5Z1lBREZZeThJcStlTHFUbVltNXRDU0RxbjNhamppSnplUEdCZTM2dVkvaENqR24ycTFxWGlONEVPS1dmblIvSHZmMWppOGJmNTFTUEl1dlkrM1pJSDdOUmdoUVcvMGM5THF4VkE3QUZScFhRMGttYlNtelFBNFhQc0RvUkMwUzVvRjhiZExwd084OEpjMTg2aEZRNTZsTFlZSUJGcy9mQmNJRUNxR0NrcnFXVjJ2amZPbjZVYzRkUklpc3JXbnNSRm42dlJmYkJrWC9WQzFDejVxMXJQWERlODh4MFJZbXltOElIekFuN2FoV0cvNkhld1NSRmlpeW81RkJXc01nY0FmaTZnL2VVSFBLYkd1azQiLCJtYWMiOiIyNWI1YjlmOGNiOTJlODg1NTRkYTZmZjgxYWU5OWE1MWM0NWM0NGVlZWMzM2EzMWY1NWRjYjlhOGEwMGJjODZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im95OWFBQVUyL1RkcmpqWkNWK3V0TkE9PSIsInZhbHVlIjoiY1dyU3hTVWhoRHMwQmllcXE2V0FuNVpHcGRFN1JiMFhGb3Z1UEtESmgzUDIyYWlCRVl0aTZkbGpqVXdGb1F3MDFIMndWZTBnNVkyVEhJZzRhMVRDaURhZTFiUmtuclRNK0FDVUcwSHRMZXZnTjRFTnh3MmIvZUx5UUkwYTkzUWdDaUNCelYyYktKMEFOTUxPMmpjOEQ3b3hCL3MrQ3JMVEVOTE1UV0trYjRkTi9LNVl0U1grZjR2WGZFNitNM0J5ZnJCd2F5UXV1ZnBVSWVpdE12MzBVcHFqNXBxdTh0MWtFbnNmSnNObFk1VGFVZkJQblA2RTI4TUFSbnRWaC96ME1lTGc1VHVxbFZUdzJtSlVHc0sySml2bGNEN1JyWVp2cWFJTFdWK2VEU28xSE9PYnZjNTYwM1pSUnkwQVExSnVReHdmc2sxb3dXU0N2THdNQnBnTm9WZDBJOU9OWlk5MURDZmRtVW9RTUpkanlrbmJGMFBFUU5zRGlVNGNqQTlWWGhjL3dmSFVOTVZ4cWhCdVF5OEZZVGhCMldlR0l4RlI5SldNcTZNV09SL0NsdDZMZGRReFZmSTM2b2JudmJlVUFhN0dLNlJOTmVqZlN1VUJESjVaNTNtWmRtR0lWcXltRkxTRmU3MWxLRHNwVDlIeGd3YVJMN1gyS3A5NXBQTVMiLCJtYWMiOiJjNTA4ZDg3MDg5YTJkMmQ1NDIxOTI1NmEwNDZhNzgzMDljOTk1MzUzZDQ0YTczMzVlY2ZlNGU0N2JhNTQ2Y2MxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:55:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ5NFgyU0NFc1FwYTgzVFNNUjIvVHc9PSIsInZhbHVlIjoieFMxaGQ3RVpPK1p5MjJZejBrcERLaHgvTEFqREU5OTN3ajM2T0dTVDIzNUNyaGZlU1dUN24zTEkrbUlIUHRNTjAyWENKVEs0a1JzWGQ5RGk1ODAxSjRkdTlnQXJyMzRSQnRPTXNkZjdUbmRqaWR0RDJTaTVoajNQU1RUdXNVa2VIaVpBR3pjeTZSb21LemlXNXF3R1N0TEo3cTlsVUxOQnEwWE0waHVaeGJ5LzZ2SkZ5VmZnOFdXUmdlWUx1ODVRMWZWUUhaRFpQWGhjeHl4eTB2VTI2Z015U2I3SDduY3MraUtxaGJwbjZZTlYxNVA5Z1lBREZZeThJcStlTHFUbVltNXRDU0RxbjNhamppSnplUEdCZTM2dVkvaENqR24ycTFxWGlONEVPS1dmblIvSHZmMWppOGJmNTFTUEl1dlkrM1pJSDdOUmdoUVcvMGM5THF4VkE3QUZScFhRMGttYlNtelFBNFhQc0RvUkMwUzVvRjhiZExwd084OEpjMTg2aEZRNTZsTFlZSUJGcy9mQmNJRUNxR0NrcnFXVjJ2amZPbjZVYzRkUklpc3JXbnNSRm42dlJmYkJrWC9WQzFDejVxMXJQWERlODh4MFJZbXltOElIekFuN2FoV0cvNkhld1NSRmlpeW81RkJXc01nY0FmaTZnL2VVSFBLYkd1azQiLCJtYWMiOiIyNWI1YjlmOGNiOTJlODg1NTRkYTZmZjgxYWU5OWE1MWM0NWM0NGVlZWMzM2EzMWY1NWRjYjlhOGEwMGJjODZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im95OWFBQVUyL1RkcmpqWkNWK3V0TkE9PSIsInZhbHVlIjoiY1dyU3hTVWhoRHMwQmllcXE2V0FuNVpHcGRFN1JiMFhGb3Z1UEtESmgzUDIyYWlCRVl0aTZkbGpqVXdGb1F3MDFIMndWZTBnNVkyVEhJZzRhMVRDaURhZTFiUmtuclRNK0FDVUcwSHRMZXZnTjRFTnh3MmIvZUx5UUkwYTkzUWdDaUNCelYyYktKMEFOTUxPMmpjOEQ3b3hCL3MrQ3JMVEVOTE1UV0trYjRkTi9LNVl0U1grZjR2WGZFNitNM0J5ZnJCd2F5UXV1ZnBVSWVpdE12MzBVcHFqNXBxdTh0MWtFbnNmSnNObFk1VGFVZkJQblA2RTI4TUFSbnRWaC96ME1lTGc1VHVxbFZUdzJtSlVHc0sySml2bGNEN1JyWVp2cWFJTFdWK2VEU28xSE9PYnZjNTYwM1pSUnkwQVExSnVReHdmc2sxb3dXU0N2THdNQnBnTm9WZDBJOU9OWlk5MURDZmRtVW9RTUpkanlrbmJGMFBFUU5zRGlVNGNqQTlWWGhjL3dmSFVOTVZ4cWhCdVF5OEZZVGhCMldlR0l4RlI5SldNcTZNV09SL0NsdDZMZGRReFZmSTM2b2JudmJlVUFhN0dLNlJOTmVqZlN1VUJESjVaNTNtWmRtR0lWcXltRkxTRmU3MWxLRHNwVDlIeGd3YVJMN1gyS3A5NXBQTVMiLCJtYWMiOiJjNTA4ZDg3MDg5YTJkMmQ1NDIxOTI1NmEwNDZhNzgzMDljOTk1MzUzZDQ0YTczMzVlY2ZlNGU0N2JhNTQ2Y2MxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:55:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975162954\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2111778648 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111778648\", {\"maxDepth\":0})</script>\n"}}