{"__meta": {"id": "X1b796d688db27f99c360f77a6912d517", "datetime": "2025-07-31 16:34:26", "utime": 1753979666.118971, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979661.878676, "end": 1753979666.119061, "duration": 4.240385055541992, "duration_str": "4.24s", "measures": [{"label": "Booting", "start": 1753979661.878676, "relative_start": 0, "end": **********.503762, "relative_end": **********.503762, "duration": 3.6250860691070557, "duration_str": "3.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.503807, "relative_start": 3.625131130218506, "end": 1753979666.11907, "relative_end": 9.059906005859375e-06, "duration": 0.6152629852294922, "duration_str": "615ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03424, "accumulated_duration_str": "34.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.843271, "duration": 0.0293, "duration_str": "29.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.572}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9577358, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.572, "width_percent": 7.418}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.990092, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 92.991, "width_percent": 7.009}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1673305029 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1673305029\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1135705742 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1135705742\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1425628915 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1425628915\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-469361671 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitsdTZnRndOV3c0clVyQmdxU1FoOUE9PSIsInZhbHVlIjoiRnA2MVR2NkFBZ25oM3dqbXZLQmtZWVZSNWd3VllEY2MrUzFCYTQ1am9BbUVZbUkvR1dvRnBLTm5PUGNvZjBIWnhiRFNEM1NjWkZIRTZYek43YncraXo2Mml2eTE4eitpUDVuY1ZPa3JTL0ZHenE3QzJ6UUlCNFhFKy9IOHYwcTNobUozeTJxMVI5SUh1RlNLUnN0cnhSVFJrVS9WNk1jb0R3R0RKV1BvaFpEQ3BmQ2dRYTdKR1RYVjZKZ1VjQy9pbFpzc2MyVUN1dm1lNUZrQ1BpM1ltczVhOUs5NEJZQlBGNDI5c3hZTGl4eWFUTmIvdzk2QVJhaDZzS0xrc0hPamlhdWphT1VhVkhxWTVwd21lbi9HVnVmSkJrQlRtVDdaQ2JzRkxJK2t5dmFtMnBsU1N1NUZBWFVwcGJBQ3N0c1JQbzVjOUp3M3pSMCtMaCtoc2xNdENtcXlRZTRJcGt4Y3hDMFpDUVFhbjBiQlQ5alZJY05JTklVWWhYanJDeEpkNzYrS1l4MGdhY21WV3NjYjY1Y3dGT0dUR0dJTmRPVTVEVWRLNFJwVUUrbHNqZHRwWWxRZ2lJZnpuTHZ4REVrWk90UWJnTUtSb29KamtrbUcwS1dCeHFSK1VwdHlSb0l2a0MwSWg1UXpObEdGU3BhYTNjRVRHTFZJTFdkQkY2MjUiLCJtYWMiOiJkYTg2YWYyNzExNjVhYTY2MDRlNGU5MzEzM2JjNTcyNDExZTliNTRkY2NmZmE4MTg4MDllZmZkMDkxZTBjODEwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjZqVFFYZVlqaldoUG1ackx0N1VzZ2c9PSIsInZhbHVlIjoiWFZ3bGZrRjlpQ0VmeXpKSkJxSmxSOXlJVnoxVUdsT3RDN252MHJhN2pyTlpyOUF1cVd3eVNSSFdYYjZuU0ZMYjRBUk9FaEpTQ3VMNHJ4c3RVUThhci9FWGpHcFFqQnpYVDFLdzdnbUlpclBrbXBldUJuc1RoMlh4M1pWODdQV2hmYVY2TUNuQWxDTTlJb0N0MjhwQkc3WURJdXFDZFU0UnBZZFhDOG1tejhXOWFabUc4bVNiQkNScWF2M1VlN1pWUXMxYm1TOTd4Q0h2NDJ3Nkh4ZUEwMUx2WittZTgwYkROWFExWHU5bklKY3VXWUtYS2tGbk5Fc3pDTUxaYitOanpsOVNqY29FODNFS0VlZ1BBSXo4VUNERlRuWkR0Y3owWTRkZmZtM0YycEVudWh5V1c3YThKcE5PUGxuME90RWliSVFOZzU5clVDVHF1RmZaTU5zaXRzNkVjTDNQUGNxSUsrd0xWYkZmRUp2dWV0eDVHZy9sYkhISTEvZWg4YWtmWGdiZHFBMTJwTmNMTDc3NEowcEFTLytEOUxWY21VYzU5dFpJbElxMzFmcHd1elBQMnFBQlZvQ3prUW5USkd6cmlvOHJpRjhZR3IvQkZ5K1l5L1VON0xMeTc4N093S053MzBJcFNFN0FEV2pMc2xObmp3aTNFMWh4MENHOEpYRzAiLCJtYWMiOiJmOGVlNTc2ZDNlOTc4N2ZlYTNjYWFiZDNiN2ViMTA0ZDQ0MjFlMTdmMWI3ZjY1YmZmYjgwNDBjYzI2MGNmN2RmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469361671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-84731597 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84731597\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1505910534 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:34:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFPMWlGUldOd1dwZGFhRGQzNHh4UFE9PSIsInZhbHVlIjoicjVxRElnSk9sQ1dPdi9PQVJZcVFhckc0aW1VTG1peXA5ZzVVc2xDaDRKVm56V1pCcHFtdWhpS0RxQXlMUzk1dkg1YXZVdDFCL0FRdGhUdHc3cHY5TFFPRW9yR3YxTXFRWkZ6Sm9QaVVRSld2VDFVVm1HOGtyUnlkcDMzRkUyYy9HWkNsVWhEVlc4Zy9UWTFzd01MWGtncUI2Zm1NajllSmg2WkNSMmgvc3JpcVBTSFQ3TlZuOWNwcWZGL0RHSEE1U1gvb1hTNURVN2xJYS9QY3dpRlZDcW9RdXNpZmJzaTZRcmowb01LL1psc1hFbHZLUExxQVhXcnNlSmxpVzl3ZlhmbURCS21pSkFxbjc4SGQ2cUMzMFhvSWRJWHpsdERzd3NHK1pqL0p5MGZKVGVsQjlLUWtYNHo2ZzVydGdJZlUyQURkNjhhNERCcENOblRLUGhRdG9KNGw0cUpOcmlXSk5CK2h3eWZPbFBFQVExNVp3V3RlajZXaHFiUVBXc042TFh2YzlOakdJVHprNjB2ZEtzU3BUY2Y1NmpTSE1lTUQ2Z3dIdmJDdWJqZ0EzaFA2RlkrWmhSelo1RXEyVHNVTitpK0pwbko4RTBCR1N3RGliTzJkUCtxTEVoSkdERWcvdkNIRE0vQ1R0TXczZ1JmVHpZMXdHczVKcDZwZFVTQlIiLCJtYWMiOiI1NjNhNTc3ZDRiYjVkODhjMjRiMjNkYjU3YTg3ZWMxYzg0NTZhZjk2ZWVmNzNmNGVjYmQ3NmM3ZDA2NjlmMDk3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:34:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkpIYUlwWVc4UjZoV28xNDdzOVZCcGc9PSIsInZhbHVlIjoicEJNc1FQRmJ5MGZwUXYvRktQU3RkalU0Y043Q2d2NGt5NkVMU0RVY3JWZjJWQ01sV3VQWHNkU1FNWmJRd1ZVTlkvcUZuTkZnSFZjOGZDNEFOejZ6NU05Q29rM0s1NGhBWHRrYk1xR1lXQjE1V2RjVXBDWElwZVBJaUx5RVRUblNzcnpkKzdBNjhrTWN5QkEreUVSVjlVMzZWNTNTSGw4KzFyWlErZVcxSjFNUm1BWllQWndrd3VlQ3F3VW53dUZkcE1UalVwSjA2R1ZkWVcxMmVLdkpsNmNYbG9SVEtGamVlTXM1Q1MzSWs3VUgzU3d1SlkxWWlCUys5U3JXVWhHMWQzVFpWOTZoN0RkdUpWSHUyZGkyVEtWcUNRcEpMM2lvcnloSERDUkdBcWlXcVFhV0lNVXVKTGhoMTNRSGUxUzY2YVc3SHBteGdrVzBzd1hpOThidGIwWXBwWnEwRG11L1FpOGhWZmtLdFN3TXhDZTdmenVqNGJVV3VHVHJiWS9pTERvTm1kLzdQZHhJU2NLb1N1MSs5YVpNd0NnUEl1U2lhbzFqa096RGpYeFNpR2ZyM1JJelZrVGRqN2tNSU5UZlBHM1JOaVBZOWNQRXJDc3YvSk0zVVVZVUJ2M1Y5VWhNWTh4R2tBZlo2Y05sdjJUMGdjbkhlWktoZSt3WVpTSU8iLCJtYWMiOiI0NDBhZDJjMmYzNGU0N2U0NDQyYTMxZDZhNzg0N2JhNzQzZTg5Y2ZlYzEwNzk0MzA1YzU3N2UxNTBkMjA0NjBmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:34:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFPMWlGUldOd1dwZGFhRGQzNHh4UFE9PSIsInZhbHVlIjoicjVxRElnSk9sQ1dPdi9PQVJZcVFhckc0aW1VTG1peXA5ZzVVc2xDaDRKVm56V1pCcHFtdWhpS0RxQXlMUzk1dkg1YXZVdDFCL0FRdGhUdHc3cHY5TFFPRW9yR3YxTXFRWkZ6Sm9QaVVRSld2VDFVVm1HOGtyUnlkcDMzRkUyYy9HWkNsVWhEVlc4Zy9UWTFzd01MWGtncUI2Zm1NajllSmg2WkNSMmgvc3JpcVBTSFQ3TlZuOWNwcWZGL0RHSEE1U1gvb1hTNURVN2xJYS9QY3dpRlZDcW9RdXNpZmJzaTZRcmowb01LL1psc1hFbHZLUExxQVhXcnNlSmxpVzl3ZlhmbURCS21pSkFxbjc4SGQ2cUMzMFhvSWRJWHpsdERzd3NHK1pqL0p5MGZKVGVsQjlLUWtYNHo2ZzVydGdJZlUyQURkNjhhNERCcENOblRLUGhRdG9KNGw0cUpOcmlXSk5CK2h3eWZPbFBFQVExNVp3V3RlajZXaHFiUVBXc042TFh2YzlOakdJVHprNjB2ZEtzU3BUY2Y1NmpTSE1lTUQ2Z3dIdmJDdWJqZ0EzaFA2RlkrWmhSelo1RXEyVHNVTitpK0pwbko4RTBCR1N3RGliTzJkUCtxTEVoSkdERWcvdkNIRE0vQ1R0TXczZ1JmVHpZMXdHczVKcDZwZFVTQlIiLCJtYWMiOiI1NjNhNTc3ZDRiYjVkODhjMjRiMjNkYjU3YTg3ZWMxYzg0NTZhZjk2ZWVmNzNmNGVjYmQ3NmM3ZDA2NjlmMDk3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:34:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkpIYUlwWVc4UjZoV28xNDdzOVZCcGc9PSIsInZhbHVlIjoicEJNc1FQRmJ5MGZwUXYvRktQU3RkalU0Y043Q2d2NGt5NkVMU0RVY3JWZjJWQ01sV3VQWHNkU1FNWmJRd1ZVTlkvcUZuTkZnSFZjOGZDNEFOejZ6NU05Q29rM0s1NGhBWHRrYk1xR1lXQjE1V2RjVXBDWElwZVBJaUx5RVRUblNzcnpkKzdBNjhrTWN5QkEreUVSVjlVMzZWNTNTSGw4KzFyWlErZVcxSjFNUm1BWllQWndrd3VlQ3F3VW53dUZkcE1UalVwSjA2R1ZkWVcxMmVLdkpsNmNYbG9SVEtGamVlTXM1Q1MzSWs3VUgzU3d1SlkxWWlCUys5U3JXVWhHMWQzVFpWOTZoN0RkdUpWSHUyZGkyVEtWcUNRcEpMM2lvcnloSERDUkdBcWlXcVFhV0lNVXVKTGhoMTNRSGUxUzY2YVc3SHBteGdrVzBzd1hpOThidGIwWXBwWnEwRG11L1FpOGhWZmtLdFN3TXhDZTdmenVqNGJVV3VHVHJiWS9pTERvTm1kLzdQZHhJU2NLb1N1MSs5YVpNd0NnUEl1U2lhbzFqa096RGpYeFNpR2ZyM1JJelZrVGRqN2tNSU5UZlBHM1JOaVBZOWNQRXJDc3YvSk0zVVVZVUJ2M1Y5VWhNWTh4R2tBZlo2Y05sdjJUMGdjbkhlWktoZSt3WVpTSU8iLCJtYWMiOiI0NDBhZDJjMmYzNGU0N2U0NDQyYTMxZDZhNzg0N2JhNzQzZTg5Y2ZlYzEwNzk0MzA1YzU3N2UxNTBkMjA0NjBmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:34:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505910534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1683973102 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683973102\", {\"maxDepth\":0})</script>\n"}}