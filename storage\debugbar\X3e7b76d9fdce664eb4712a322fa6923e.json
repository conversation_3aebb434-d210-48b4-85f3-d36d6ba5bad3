{"__meta": {"id": "X3e7b76d9fdce664eb4712a322fa6923e", "datetime": "2025-07-31 16:56:08", "utime": **********.952873, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980966.589337, "end": **********.952911, "duration": 2.3635737895965576, "duration_str": "2.36s", "measures": [{"label": "Booting", "start": 1753980966.589337, "relative_start": 0, "end": **********.775912, "relative_end": **********.775912, "duration": 2.186574935913086, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.775942, "relative_start": 2.****************, "end": **********.952916, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TlI5ekTjOs4I4hI1psHjDHvlw1RLIasGkuCs9k6k", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1834013576 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1834013576\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1368942787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1368942787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1021802467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1021802467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-833524126 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833524126\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-335581582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-335581582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-452723125 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:56:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhZOUFmK3d5czVFdzRLYllYSmNNMmc9PSIsInZhbHVlIjoiK0dZM1FBRmtEa05QTVF2a3lQSFMydUFTUERoSS92Q0NibXl1MGEwcWFsMjZKaUorNzNCREFIZVFIbDZGcmpMd241R1J2WmZYWUhKclFFZFpyMWplWkNyd3JOVjBFY2xLeko0ZGMyRE00eGZIUGZWTTZtbThJMm9TbVd3S0x0UXYxYmcxWVFvbVZBZmtBREI0QXp5c0tIZ2tBRmFzYm81VVNYZVVXVWNqR2E1SVVSY01aNXFWRmJKS2tXMEJqb0pMbU40eHIxcUxNSXdmamhObUFtRU0xc2Q0YjlOU2N3RkpYOHZBRnVmNDRSeU9MR2JHZm5ST0F4d1IxZVo1UTh1MHFBMHZXcW91R0JtQXM3cG5NejdqOHZIWHB0OHlmVnhrNXdNNE9GYkc1UUhYbkF0QzBQdHpVOS9kSzFjQWR4SkxwT3Z6c1FSeDVRdkYvb1RpVENSN0JZNGJCQ3hJTzhNNk9sTzYrN3FvOVpRRzllWG8wVnBRMUQ4OTB3bVk1ZCtQTTZKUDRxTFJKM1JiRVlZWnFBYTlkOUtFaGN5cGw3QnpRTmdERUh6cGNGNG1rbGRuL0RHcGlsYkROMXVNWGtZbjVmL3BCKy85NFVqOWh2T0xjSWhRRk9RV2Y4UUYveklPUWNFbDBaYXJMOS9RbW1RbXdXRU9EWC9IRXFiT0ZveS8iLCJtYWMiOiJlY2Y0MGVmZjQ0NThhOGEyNWY3ODg4Yzc5YmZlNGQ4ZjgwMmEzYWEwM2EzYjM5YTE2YjQwZWViNTQ4MzE1ZjMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:56:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inoyb0JDcG5QYzkyd3FUNFMyMWlxWXc9PSIsInZhbHVlIjoiT2Yzb1hJWGVkSU9wY2VtNnFod2pPQ1EwNW9SSkd6Y0tYQ2JMeFJpMGFYcWZ3WVhuSzhOblZKTVRRYUxFNU1SbTJYV001VDBycng3THJPMGo0SXlpUjZRZ0RqcGRVSDU4S1NtallmTVQzR2lPbXYxNFVzcDMxeVdrQmJVcFd4alJmTDEya1cwOHVsanVrRDZyQlZlLzlnbWVjaG5lcXhKWWJFU3NZeFM3YVVheERjZ0JxVXpPMTc0MFhEMkJOMGNmbnlQbXV0QTA1VU4vRzZTMVVncW1EN3NlV1BGOHFOZEYydTM5ZWJLdENYall0aU9sdVZUZXlOODFxQTRWOU9ud2hybU0yamVIOU5zamdtS1JhWlhUYXN4OUJuVFNHRmE5ZElMSm5Ca3o4RzYxWEEzQ2IxeVZzSFVKZnhlTEV4N3F3cG5EZE54SzhRWWxKZDM2S0ZINkp5dTJQR1NSbG55ZEd2Q0tEUHVWK2RRVkdFZ3pmRDh5b2JQOEdHMFVKdnlWT1EwVHU5Nnl0MFQxL0JXbmRKTkpOTzg5OUcxR1NlUXBZYWJwcm14YVIyY2NvQlBpU1pJUHdOWVYyZE5EOGZOaHNPc054SGl0cUZMdHZRNi90RFFGV1dsTnZQTkgrTEphdDFZYWY4L0Y3WGxjRmpOMHJaTE1ZZFVEeTU4Uy83TnEiLCJtYWMiOiI4MWY4ZmYyMGJiMTZiZDUyZmE5ZWUwYTU3OGVmYWU4YmE5MTkzNjE5MzliZTcwOTc5ODViMDUyYTExNjI4YTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:56:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhZOUFmK3d5czVFdzRLYllYSmNNMmc9PSIsInZhbHVlIjoiK0dZM1FBRmtEa05QTVF2a3lQSFMydUFTUERoSS92Q0NibXl1MGEwcWFsMjZKaUorNzNCREFIZVFIbDZGcmpMd241R1J2WmZYWUhKclFFZFpyMWplWkNyd3JOVjBFY2xLeko0ZGMyRE00eGZIUGZWTTZtbThJMm9TbVd3S0x0UXYxYmcxWVFvbVZBZmtBREI0QXp5c0tIZ2tBRmFzYm81VVNYZVVXVWNqR2E1SVVSY01aNXFWRmJKS2tXMEJqb0pMbU40eHIxcUxNSXdmamhObUFtRU0xc2Q0YjlOU2N3RkpYOHZBRnVmNDRSeU9MR2JHZm5ST0F4d1IxZVo1UTh1MHFBMHZXcW91R0JtQXM3cG5NejdqOHZIWHB0OHlmVnhrNXdNNE9GYkc1UUhYbkF0QzBQdHpVOS9kSzFjQWR4SkxwT3Z6c1FSeDVRdkYvb1RpVENSN0JZNGJCQ3hJTzhNNk9sTzYrN3FvOVpRRzllWG8wVnBRMUQ4OTB3bVk1ZCtQTTZKUDRxTFJKM1JiRVlZWnFBYTlkOUtFaGN5cGw3QnpRTmdERUh6cGNGNG1rbGRuL0RHcGlsYkROMXVNWGtZbjVmL3BCKy85NFVqOWh2T0xjSWhRRk9RV2Y4UUYveklPUWNFbDBaYXJMOS9RbW1RbXdXRU9EWC9IRXFiT0ZveS8iLCJtYWMiOiJlY2Y0MGVmZjQ0NThhOGEyNWY3ODg4Yzc5YmZlNGQ4ZjgwMmEzYWEwM2EzYjM5YTE2YjQwZWViNTQ4MzE1ZjMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:56:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inoyb0JDcG5QYzkyd3FUNFMyMWlxWXc9PSIsInZhbHVlIjoiT2Yzb1hJWGVkSU9wY2VtNnFod2pPQ1EwNW9SSkd6Y0tYQ2JMeFJpMGFYcWZ3WVhuSzhOblZKTVRRYUxFNU1SbTJYV001VDBycng3THJPMGo0SXlpUjZRZ0RqcGRVSDU4S1NtallmTVQzR2lPbXYxNFVzcDMxeVdrQmJVcFd4alJmTDEya1cwOHVsanVrRDZyQlZlLzlnbWVjaG5lcXhKWWJFU3NZeFM3YVVheERjZ0JxVXpPMTc0MFhEMkJOMGNmbnlQbXV0QTA1VU4vRzZTMVVncW1EN3NlV1BGOHFOZEYydTM5ZWJLdENYall0aU9sdVZUZXlOODFxQTRWOU9ud2hybU0yamVIOU5zamdtS1JhWlhUYXN4OUJuVFNHRmE5ZElMSm5Ca3o4RzYxWEEzQ2IxeVZzSFVKZnhlTEV4N3F3cG5EZE54SzhRWWxKZDM2S0ZINkp5dTJQR1NSbG55ZEd2Q0tEUHVWK2RRVkdFZ3pmRDh5b2JQOEdHMFVKdnlWT1EwVHU5Nnl0MFQxL0JXbmRKTkpOTzg5OUcxR1NlUXBZYWJwcm14YVIyY2NvQlBpU1pJUHdOWVYyZE5EOGZOaHNPc054SGl0cUZMdHZRNi90RFFGV1dsTnZQTkgrTEphdDFZYWY4L0Y3WGxjRmpOMHJaTE1ZZFVEeTU4Uy83TnEiLCJtYWMiOiI4MWY4ZmYyMGJiMTZiZDUyZmE5ZWUwYTU3OGVmYWU4YmE5MTkzNjE5MzliZTcwOTc5ODViMDUyYTExNjI4YTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:56:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452723125\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TlI5ekTjOs4I4hI1psHjDHvlw1RLIasGkuCs9k6k</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}