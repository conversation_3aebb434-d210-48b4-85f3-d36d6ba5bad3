{"__meta": {"id": "X80dede5011205e8a2b9e3dd5f35e67ac", "datetime": "2025-07-31 16:15:18", "utime": **********.59304, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753978513.480817, "end": **********.593098, "duration": 5.11228084564209, "duration_str": "5.11s", "measures": [{"label": "Booting", "start": 1753978513.480817, "relative_start": 0, "end": **********.368543, "relative_end": **********.368543, "duration": 4.887725830078125, "duration_str": "4.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.368875, "relative_start": 4.****************, "end": **********.593103, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "H0axlMIlXZpOOXJDEwyVDljXVTsy5C1qL8M4ZWym", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1825029490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1825029490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1113275268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1113275268\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-315114497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-315114497\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2115397905 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115397905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2127559189 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2127559189\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1910899335 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:15:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ6WFZIb2ZaYzVOMVBrTGdxUGRmalE9PSIsInZhbHVlIjoiM0FIUVJPUThPVCs5RnMzSzY4MHdJVWxhMzZFcFNiKy91Zzd2dWlPV0dyY2JUem9yRUEwQXgrTXE4NWJsMWVlT25NTTk4eXF5eTVmcWJvVCtUbWVmOVhkaHBvRXdWRlBNLzNlWVd4WU9FUjdWV0lVTE5xMFpsSE1XMStCN2FNTTdvZUkvTFBuR2c5Qk81VjQ5bmphMW5sVE9zSDZWRE8wWmxhVzQ5dlBuUGRkSzZQQS92VHYvQnRoVTRUZFpGZ3lTNjF1dVpuNktXMVBvSHZMNWlyZDc4bXZqV09QQm1reEtTMlRKUU9vQ3k1SzA2Uzg3OEZHbkp2Y2UraGNOTVh6YTZPOU0rTlZDZWg2RnJtc2M0UjI0ek0rbmFMdzVXQkVOTE1vbXFyd3JCSW5PZE15LzBzWVBMRWpmMHFOSFRQcElybUs1OERDbEdONVJtMFZzckF2KzB1UnRrcjNqd0w0SWdNem1XV2R3elhwb3hxZTJMSFhBT0J0TWtSVU43ZHdwbmFGOEgyRmp6YWVJRTNUcDUzTVlnTjN0c05hVTF6T1lrV1NhL010dzZoZUFNSzNKS0NIa1YwRXkydTdsUURYb2h4SmNpYjdFTHlvTEZCejZNcWxSanNYR2kzRkV0SUZMYmt2aXhydDRFeURzeGdBM0NYbnFYSTBhRkRRUzZxZ3IiLCJtYWMiOiI4ZTU4ZTM2N2Y1NzE3MDc0N2NkZDM4NzUwZGNmMmRiZmRjMTYzYTJlMzAwYmYwODczMDI4MmQ0NThkNTVkMGFiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:15:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkcyRTdNclBaalh0QUZnc0VnZVl2eXc9PSIsInZhbHVlIjoiaTVVZlBocXVycDc5dDVxVXg5aWdZQWlnMlgwUjNIZ2xKSmFSWHZtbjlNaENYNXRRa1N3dVlnb1ZrK1h6MC82aEUzS2lHRjVLUFo5YUk4bXlocndqdnJlVzJqd1pxMjRrZ1dLdm93NEpIN2NabFlPSlJEWE9KdDBkdExWazJSWmhPNEc0VTF6c3U5UlNSYzJCanFsdEI5Z05vUTZLbjRiakFxNnpCRkFRREZOdUJqWThpLzM1dS90MSt1N1FxUVFXS2UvcTVNamtnYW55Y29FN0kzYXc3cWwvTG5GZXphUjlzK0NQazNWQ2l6KzBsWDErSzRRb3AxbUswcWduaVc0YVI4WmN1Mk1NbUZHU3NkQjV3WjAwTTI3V1grZllsb3g1WGtoaUw3Tlc5VWU1NTJjclU0b1MzM1o2cmZWdXdobWtmc3VmR3Q0N0JIZzZpMEw3MnR0T1pOYkhnOENORERrNm55SlZRL3gzRWdyOHFBa0NHd2kzbDhONzhhS2VpWHFGbXQ1NEREMFNSM29VVi85VHo1UHpFcHNMdWtYSnM0cDJtb0kwTFYxVTA1b3doTjkrT2JxSjhyUTU3WGhNbG11Q2w4OE1odVZITlhFcTd2RHhOVTlyZC8yTmY4OTkvUkZ1UVVKY3NEYWtrN1Q1Q0ExQUVKMUdpL2hrVWp4Z29ObTEiLCJtYWMiOiJmZmUyODg3Y2NlZmE0OWM4ZGMwNjljNjIwYzMwNWFkOTY5ZThjM2ViOWExNGY1ZTBlYTUxOWU1NzE0NmNlYzEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:15:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ6WFZIb2ZaYzVOMVBrTGdxUGRmalE9PSIsInZhbHVlIjoiM0FIUVJPUThPVCs5RnMzSzY4MHdJVWxhMzZFcFNiKy91Zzd2dWlPV0dyY2JUem9yRUEwQXgrTXE4NWJsMWVlT25NTTk4eXF5eTVmcWJvVCtUbWVmOVhkaHBvRXdWRlBNLzNlWVd4WU9FUjdWV0lVTE5xMFpsSE1XMStCN2FNTTdvZUkvTFBuR2c5Qk81VjQ5bmphMW5sVE9zSDZWRE8wWmxhVzQ5dlBuUGRkSzZQQS92VHYvQnRoVTRUZFpGZ3lTNjF1dVpuNktXMVBvSHZMNWlyZDc4bXZqV09QQm1reEtTMlRKUU9vQ3k1SzA2Uzg3OEZHbkp2Y2UraGNOTVh6YTZPOU0rTlZDZWg2RnJtc2M0UjI0ek0rbmFMdzVXQkVOTE1vbXFyd3JCSW5PZE15LzBzWVBMRWpmMHFOSFRQcElybUs1OERDbEdONVJtMFZzckF2KzB1UnRrcjNqd0w0SWdNem1XV2R3elhwb3hxZTJMSFhBT0J0TWtSVU43ZHdwbmFGOEgyRmp6YWVJRTNUcDUzTVlnTjN0c05hVTF6T1lrV1NhL010dzZoZUFNSzNKS0NIa1YwRXkydTdsUURYb2h4SmNpYjdFTHlvTEZCejZNcWxSanNYR2kzRkV0SUZMYmt2aXhydDRFeURzeGdBM0NYbnFYSTBhRkRRUzZxZ3IiLCJtYWMiOiI4ZTU4ZTM2N2Y1NzE3MDc0N2NkZDM4NzUwZGNmMmRiZmRjMTYzYTJlMzAwYmYwODczMDI4MmQ0NThkNTVkMGFiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:15:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkcyRTdNclBaalh0QUZnc0VnZVl2eXc9PSIsInZhbHVlIjoiaTVVZlBocXVycDc5dDVxVXg5aWdZQWlnMlgwUjNIZ2xKSmFSWHZtbjlNaENYNXRRa1N3dVlnb1ZrK1h6MC82aEUzS2lHRjVLUFo5YUk4bXlocndqdnJlVzJqd1pxMjRrZ1dLdm93NEpIN2NabFlPSlJEWE9KdDBkdExWazJSWmhPNEc0VTF6c3U5UlNSYzJCanFsdEI5Z05vUTZLbjRiakFxNnpCRkFRREZOdUJqWThpLzM1dS90MSt1N1FxUVFXS2UvcTVNamtnYW55Y29FN0kzYXc3cWwvTG5GZXphUjlzK0NQazNWQ2l6KzBsWDErSzRRb3AxbUswcWduaVc0YVI4WmN1Mk1NbUZHU3NkQjV3WjAwTTI3V1grZllsb3g1WGtoaUw3Tlc5VWU1NTJjclU0b1MzM1o2cmZWdXdobWtmc3VmR3Q0N0JIZzZpMEw3MnR0T1pOYkhnOENORERrNm55SlZRL3gzRWdyOHFBa0NHd2kzbDhONzhhS2VpWHFGbXQ1NEREMFNSM29VVi85VHo1UHpFcHNMdWtYSnM0cDJtb0kwTFYxVTA1b3doTjkrT2JxSjhyUTU3WGhNbG11Q2w4OE1odVZITlhFcTd2RHhOVTlyZC8yTmY4OTkvUkZ1UVVKY3NEYWtrN1Q1Q0ExQUVKMUdpL2hrVWp4Z29ObTEiLCJtYWMiOiJmZmUyODg3Y2NlZmE0OWM4ZGMwNjljNjIwYzMwNWFkOTY5ZThjM2ViOWExNGY1ZTBlYTUxOWU1NzE0NmNlYzEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:15:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910899335\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2609003 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">H0axlMIlXZpOOXJDEwyVDljXVTsy5C1qL8M4ZWym</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2609003\", {\"maxDepth\":0})</script>\n"}}