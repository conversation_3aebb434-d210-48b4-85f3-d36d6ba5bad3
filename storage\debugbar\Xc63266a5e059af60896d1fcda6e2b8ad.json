{"__meta": {"id": "Xc63266a5e059af60896d1fcda6e2b8ad", "datetime": "2025-07-31 17:11:30", "utime": **********.827041, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981885.581138, "end": **********.827103, "duration": 5.245965003967285, "duration_str": "5.25s", "measures": [{"label": "Booting", "start": 1753981885.581138, "relative_start": 0, "end": **********.277005, "relative_end": **********.277005, "duration": 4.69586706161499, "duration_str": "4.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.277061, "relative_start": 4.695923089981079, "end": **********.827127, "relative_end": 2.4080276489257812e-05, "duration": 0.5500659942626953, "duration_str": "550ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421536, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0152, "accumulated_duration_str": "15.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.645221, "duration": 0.01225, "duration_str": "12.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.592}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.736472, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.592, "width_percent": 9.803}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.754022, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 90.395, "width_percent": 9.605}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-129723124 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-129723124\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1328130830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1328130830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-97674879 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-97674879\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-648972434 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IncreXQwaXp1MXdrQ0RyTkVOL3UrVGc9PSIsInZhbHVlIjoiYmFqRVVnd0svQU1jRWxqZ2FsbE90ZW1TbGNxMDZsSVhnNGwzSEN0eTRiQXdHalJRVHdwbDYwNmcyR2VhYUt3Y2c5YnJCZ1VoMk05UVZZNGJMa09VYjJGeHRPb281bzJvS2swclNlY29vMytYM2pzcjdjb2owUFpqWmlyQlBTOXE3YzkrTzhGR3B0UmJvemhVUmFha3JoRmtEbGhIbVdEazRyZU5wNC9wZHRHT1pKaXVhMFNGcm82U21UZEczalQ3Tm90NUZlUW42WFFjSnR6UXdIREZEUDhKaVpHYmpSTVV6aDgxTGZLY09DdEpoYTBYc0hWK21qV1IrbU1wTTY2QVpUMUpIVXpFcmtLdEwvS1phd25WTHZXTFMvMVRJZ1lGTUk3N0FVSEtyeS8wejdBT1JvZzRQS1pnK2JLVnp3TlFRNnlrQlJlOU0rdnUwWUNMQTVzWG9IV2J6Ny9Cbzg1ZXZSZXdvblFRS3pXMHNJdllXZFpZalJZUG5zVE9qcUdvTEdURXNGUGhIVzhaYzJDU2ptd0hlRHh0OHdXVUpmdlhmWndQUjh3eGw5dW11TjZPKzBRbU9ENEs1QWdoTkN2TzVnTVBYVnZFa3ZHOGw4bHlBb1d1RzA0MFRoeHNYR3ZRdFFjQ01mUnVGYnBqL2FVcHRGZE1LZWdzVHBld2ppUnQiLCJtYWMiOiJhZmNkMTY0NDQzNGJmM2RiNDQwODkwZWJjYTFkZDg0MTg0YjMzMDJhYzk2MWQyNmIwZjZlNTBkNzQ1OTQ2YzQyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjlMUlJGTUIrakhvQlB2KzNtMUFsT1E9PSIsInZhbHVlIjoiaTBoeVlpa2JFYUM0NWp0ZG13WFNxdTVFcUF0STNra0kya2Zhay8yRW83MXVReEFRYW56UWhLQkZZeVhqMUlnT2cyaVBnbnY4ZVhZRlJiempJb0FZYmljL041bU9CN0luRzI1bTVmM3JIRTdETmM4QmJLc3dHclpVbGkrSHZkcUw2ak00NktFTWY2eHhRQlhrMytlSmlMWGFuWUJUeW9PZlZIa2piWnFERzNlNDRzQ3ZBcjZNU0lCV3lYNzlLMFZOMTJJOXRxL2owc01Wc2xsY1RKcHA5TnM3VTVLRU5UV3ZWeGlTd1F3a01CbjJ2dXFWNCtxT2tzK1Jkd1RLd0lybG04V1JlWWQvcG9BZStFZ3dBdzBGYk43eTJyZmI4NncxcHJlMTFwcWVqNjU4dGt6TGRqb0RPc2JOS2xkRzBBQm5JK3FmYjc5ZWUwM040L2tBMkZJaDNuOFZYQmQzbUlNQXN0bEwzdXhXQ3Z1RGt5MTBHK3k3bHFIN0szbklmdkZacEpDajd1SG9OVnJDU2hzVVNOaXp1aEpvdjVpVWtzNHU4blAwYjk5MUYxam9xUHhSZkttb014UllIcDlQVngyTkRWSVlSUTYzV1NUb3lSMkNoTEQ2QStURmR0RkovQ0VBL0crTHNYS0toMWdzRzYyc0ZRL2V4L0xsdTBCdStVd1kiLCJtYWMiOiI4NmU1ZjQxM2IzZjNiM2Y2NWQ4MDEyOTRiNWZmYWRmYTU3MmZmN2IyZDhlYmU0YTMzMWUxNmY5NmVhYzZmZGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648972434\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-299424256 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299424256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1443364025 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 17:11:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhMMmJxUjZqQS95Vk5LT1ExMkwrZFE9PSIsInZhbHVlIjoiWU1kT1hYbDBqWnNOaWZEOXV2bnlZVGUremdnV1draXRTak5qOHp6NGg2ekJnSFlaN3UzbVFWRWdsOWtpdUR3VC91U0s2UndLWnpHZ29VWXZuNW9wenBHa04zcmcrUzZVekl4RVl2Um9ITFpCVStlWmFJMlRoaGp2ejdYeUhFcXpqUUxXam9tZy9BSzJDZUU3N3JCbFN6WjhwY0FxenBCTzZXTm5Od2RaeThjL1NxUWZBVFFmUmNWdVJUNlBVeWlManJmblA3a3AxVXRNUTBsM2FzSmlLdlQvM3hUMzR4RVk2UW1Ea2NOZXJZT3ZJZ0RST3MvU3pTUjExV3BJcmRCQk9USGo3Z2l0MUladk15TEQrT3ROY29uRUFwL24wYlhlWmFpalZteGNRSHdlOTV5Rm5JUjM0UTRHb01TYkc3d2dCK0xoNUZVTXJrS0ROVnYvSlRMelExSTJ0cFZhK3RGa0lDSVZnb255dkR0ME9rTkpkSEplbGRXNkNHVFMzOU5vSWduaHAyMm4reUlrNVpHcno2TXZIMmM3Wkk1Tm1oU1RhamJReGIzUzlDdVhCOFZadURGUjZ4L3A5dk9mci8yazJFSm1iWHpSZjI1WkZxWTVwd1Q3THRta0tRNUprOW9tK1NZWFgwdVJPRW5aZUk2aGEyUzBEV1V0dlNCV0hta2YiLCJtYWMiOiI3NzM0Mjg5NDAxZjczN2Q2NWEwZmE0ZTdmMzQ5OTc4NmQ4NWJhMGExMWM5ZGE0ZWI3YzRlMjk5NmY1ZWVhNjZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:11:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFhSlpxQ05BQ3NIcTladDdxR2cwQ3c9PSIsInZhbHVlIjoiSFQxZUFDdXRqOVRQL3NEYitST3VYTGJtRklrQ0JBKzZkMlBBN3h3dk5jMU4zY1hYUHJiUFNGMjBiZFFCMDBkWE9GOWVBRkdZejFNNGh2UGdYUnB5LzYvZmdLdEZnQlhrOXloS3hNL0lTaTZqYWlLYTNUb09pNzFqNzViRWZremx2RjB5Sk54aklGS1BXVHNidWFEOHIvUHpIbjRWWlZlTHdtSUladkhUYXVvQ2xvY2JNTXVQVlVHWUZPNnRaT3BmRTZPREVId24rWklTZTZXeW1VbVovbHlsTFZzSHQrNGQyU1ZVVXJESTBqcXIrL3FQUWlZNVJPVzllbTZ5dTIyVkVESGE2RTVzMmVHdFR6Q01pbHJFdkdsRTRvamlVdEdXQTVGZi9NdkVlMUh5OUpBVEtPL3VDTmN3MWN4bCtHMWIzeXNnUHBGaHJ3cVZxaHl0M1E0bUR1SkxCVFRGQWZsM1pTWnFwdFdnRThsOUtlbHJEdXdwMHRodlo0OFB1bmpxN2c5WFA4SUsxL1l6NXVtcXJoZG1NakNxZTZRTGMrbVgxV2g0N0N3NS83L0xWaVlOUXZjVHRRV3ZYSWptZzBZRldkR244RUI2OWlkd2xPSXpPSVJDNGNVdGJTNVA5aHNjU1Z0dHdCRmRWNmhCUFNFcFpod3ByNHNFQTlXelI5RTMiLCJtYWMiOiJkZmVlZmQ2Y2I3ZDgwNzU3ODRkN2NmMjY3NmY2ZjFjYjQ4NzIxZGJiNmUxZTlhNWY4NWE0NTYwMzI1YjBlZTVkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 19:11:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhMMmJxUjZqQS95Vk5LT1ExMkwrZFE9PSIsInZhbHVlIjoiWU1kT1hYbDBqWnNOaWZEOXV2bnlZVGUremdnV1draXRTak5qOHp6NGg2ekJnSFlaN3UzbVFWRWdsOWtpdUR3VC91U0s2UndLWnpHZ29VWXZuNW9wenBHa04zcmcrUzZVekl4RVl2Um9ITFpCVStlWmFJMlRoaGp2ejdYeUhFcXpqUUxXam9tZy9BSzJDZUU3N3JCbFN6WjhwY0FxenBCTzZXTm5Od2RaeThjL1NxUWZBVFFmUmNWdVJUNlBVeWlManJmblA3a3AxVXRNUTBsM2FzSmlLdlQvM3hUMzR4RVk2UW1Ea2NOZXJZT3ZJZ0RST3MvU3pTUjExV3BJcmRCQk9USGo3Z2l0MUladk15TEQrT3ROY29uRUFwL24wYlhlWmFpalZteGNRSHdlOTV5Rm5JUjM0UTRHb01TYkc3d2dCK0xoNUZVTXJrS0ROVnYvSlRMelExSTJ0cFZhK3RGa0lDSVZnb255dkR0ME9rTkpkSEplbGRXNkNHVFMzOU5vSWduaHAyMm4reUlrNVpHcno2TXZIMmM3Wkk1Tm1oU1RhamJReGIzUzlDdVhCOFZadURGUjZ4L3A5dk9mci8yazJFSm1iWHpSZjI1WkZxWTVwd1Q3THRta0tRNUprOW9tK1NZWFgwdVJPRW5aZUk2aGEyUzBEV1V0dlNCV0hta2YiLCJtYWMiOiI3NzM0Mjg5NDAxZjczN2Q2NWEwZmE0ZTdmMzQ5OTc4NmQ4NWJhMGExMWM5ZGE0ZWI3YzRlMjk5NmY1ZWVhNjZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:11:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFhSlpxQ05BQ3NIcTladDdxR2cwQ3c9PSIsInZhbHVlIjoiSFQxZUFDdXRqOVRQL3NEYitST3VYTGJtRklrQ0JBKzZkMlBBN3h3dk5jMU4zY1hYUHJiUFNGMjBiZFFCMDBkWE9GOWVBRkdZejFNNGh2UGdYUnB5LzYvZmdLdEZnQlhrOXloS3hNL0lTaTZqYWlLYTNUb09pNzFqNzViRWZremx2RjB5Sk54aklGS1BXVHNidWFEOHIvUHpIbjRWWlZlTHdtSUladkhUYXVvQ2xvY2JNTXVQVlVHWUZPNnRaT3BmRTZPREVId24rWklTZTZXeW1VbVovbHlsTFZzSHQrNGQyU1ZVVXJESTBqcXIrL3FQUWlZNVJPVzllbTZ5dTIyVkVESGE2RTVzMmVHdFR6Q01pbHJFdkdsRTRvamlVdEdXQTVGZi9NdkVlMUh5OUpBVEtPL3VDTmN3MWN4bCtHMWIzeXNnUHBGaHJ3cVZxaHl0M1E0bUR1SkxCVFRGQWZsM1pTWnFwdFdnRThsOUtlbHJEdXdwMHRodlo0OFB1bmpxN2c5WFA4SUsxL1l6NXVtcXJoZG1NakNxZTZRTGMrbVgxV2g0N0N3NS83L0xWaVlOUXZjVHRRV3ZYSWptZzBZRldkR244RUI2OWlkd2xPSXpPSVJDNGNVdGJTNVA5aHNjU1Z0dHdCRmRWNmhCUFNFcFpod3ByNHNFQTlXelI5RTMiLCJtYWMiOiJkZmVlZmQ2Y2I3ZDgwNzU3ODRkN2NmMjY3NmY2ZjFjYjQ4NzIxZGJiNmUxZTlhNWY4NWE0NTYwMzI1YjBlZTVkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 19:11:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443364025\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-367844383 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367844383\", {\"maxDepth\":0})</script>\n"}}