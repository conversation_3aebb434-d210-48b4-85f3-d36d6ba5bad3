{"__meta": {"id": "Xe26ea17a9b6804e146c0a7f8b759a500", "datetime": "2025-07-31 16:59:28", "utime": **********.661403, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753981165.502679, "end": **********.661454, "duration": 3.1587748527526855, "duration_str": "3.16s", "measures": [{"label": "Booting", "start": 1753981165.502679, "relative_start": 0, "end": **********.292237, "relative_end": **********.292237, "duration": 2.789557933807373, "duration_str": "2.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.292275, "relative_start": 2.78959584236145, "end": **********.66146, "relative_end": 5.9604644775390625e-06, "duration": 0.3691849708557129, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47421568, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=869\" onclick=\"\">app/Http/Controllers/FinanceController.php:869-943</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03249, "accumulated_duration_str": "32.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5125918, "duration": 0.02956, "duration_str": "29.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 90.982}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.588339, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 90.982, "width_percent": 4.863}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 872}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.600933, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 95.845, "width_percent": 4.155}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-757438514 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-757438514\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1356046960 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356046960\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-445787229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-445787229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1706864904 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImtPekNCSzQyNmgyYjR1bEVpL1JEYUE9PSIsInZhbHVlIjoiSnBmUG1oMzhPNVplN2NBdFp0SHlXQVVZZ2xZOThhSjl1UmJoQ0x0MkJNUmtzWmRBTVM0WlFvSWFDR0V1STBuYVVhUFY3NWRSUGkyWXVRVXBxSjNjVmIvVENBWFpiNkVyNE9vZmVPeFB4eG52dTNYeWthRjA4U1RuMXY4TGV4UWpBNlNiVVNIVytQN3FyZ29hSDVUMEpXdmFmUGlKT0s4NC9OcmZHTHNHUncvbEhWUlpMb1hVYzNJNVAyZCtpZURpMGhtbm41aHBTeEpRWVk1MTR6ak1OaXA3UGhoRXM0MGJKYWk4WVNpcHlHYWpsVk05dWoxMEVsMmtuMnRENExiTnEvS2NCMks0c2FxQzUrNWlpRHk5WDhtN2Y4UXg0ZTFyTVkwRFFnVXd2OVNvOVNpa1RLVHJVemZ3Yjd0NnYxM1oreG9iWTd6WjMwRk4wN2tHRHN6Y3FiV2Rqck8yNmZVanZlNDN0KzFGUlBqTEs5emZRSW40eFdVMy9XT3g1S2lPSDVMMVoyWC9waG9IZmVDdERRd0ZaQTBONHFueW1rRVAvUG83SHM2d3dWQWp4N1VTdHdIOHpDVm9lcVVpa1M4SzAvSnQxQ2xPYTYzL3BMSENCcUlTNzBsdDhGK3QvQmN5RVJXRHdqcElOcndBZ0VlbGFlRm43dFF3eDhCMnJUNHUiLCJtYWMiOiI5ZThkZTg1MGZjMDA5YTAyZmI2MzkyNWNhMjU2MTllN2Q5NDFlZGRmYThiOGNlN2MzODg5MTkxZjk0NGI1YmJhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IktlanR6U3l2TzQ5eTZHME1zaFh2VUE9PSIsInZhbHVlIjoieWlteVRKT2IyZEl2T3MrTVpMQ0p1VmRJa01EaXZiNTdlN3g4UXIvLzJIRUc1akw0Uit2MGxOWmVHUnRwWFY1empiWE9vMFpHUk4zNkQ5ZHVyZ3R0T3JmNDVlM3o1aU1ndzFrZnJhVHl1cUtINzJHaEtnS1lHRFErK3NrTnVsWmt4N0U1SWFFZDFxbU5oVStzYzNaQ1MzaC9QYXZick1XbUlJdi8rZHVTdUZEaTZGRXJCUXcxWHlVV1k1NzcvMTFjeWVXQnluOGsyaXdxcFlKRjdlSThwVzlVcWRmc2J5WXovelZHTjBJYkVOK01XMzYwVzdVdjRzbDdWOHROVUNCU3BQOTNLMWpJSlV5cGl4cTZXS3RLZkVWNGlneWdkS2lnelFlQm5PTXBsT0VKY0FrZmhkZzV0aFBWU1U2RUlEMFBvNEp2Z3JoV2FDTVZkWFc5bGVyN0NVMmZRZWNqM3BLSHBJNFhyeXZIMXg1NzFYT3JUYzZDNWpsRVNSa0YvWDVzUWxldkFwbDJEelh1YklYM1JoM3JoTlpFYlEwYTVtc3VDL2xjS0xpalJ2bUJjZTZjUmltL014MldUSUE1dU04SG5ibjZhUmlRL3IybXhyZFBuNzlySWgzRERhaFZ4bVRML01ITG5FdkJETGNXNU15dk5LWDFzQnA5Z1cyaEJuckgiLCJtYWMiOiIyNTEyNzM4MGZlNjY5NzU5Njc1YWE4OGYyYjc5MTk2ODI1ZDNhZmEyZTAwMzViM2IzNTYzMmRjMGE2Y2RiMTk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706864904\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1792483025 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792483025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1817372673 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:59:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNvbDg5V2tZRmJ4UlMyTnhDOGpGUmc9PSIsInZhbHVlIjoiMGQ4QklMemd0bTFLUWMxbVB3UCt4UEp2aThTMDY5TEE2NzhzK3lrZnpoMGk3MnNOQUxGODJiMGtDcGFxaVdVN2VxYWJTSmhTWVVtajJtZWpTZ0cyWDA0QTcyM0oxOHRLR0RZVW5Ud0dsbG1JZ3Q4N1ZxU0dVRVNmalQ0elJyRCs5cnluZ25wZ3Z4Z0g5MExTdExaaWF6dXlORitYTnc4dlpuYzQwM2oyVy80eHpMVmgxUDY0NnVYbGRmOU4zZVFVZittRllLODVWL295M1FoUjBuVmtuVk9jS2JvZlRHS3krOUF5Q3hod1dtM1lsQ2dkYUVhNklQaEtLTEpXbzhaV01nVWIvWGg4UGhaazZOQVdoQ2xxRDR3aUFCWkI4aFVjSjgyblhNNitrZkpWMDRmems5eXhnSmtzamFCcXdabUxJUGNGOGtpVmFEUEdmN3l1akpCSEVueXpwMk9TUUNDVUhkWFQ0VVV2clBLMkdSWHpESW94S0RBR2Z6b25NOTFZQzk3d2RVM2hlUW9YK0dlNGRnVXQ3ZnBzd1hrWWhHMTNsNDBzeHMwODRMVVd6UmxhSUk5TDhzWWVHSElpMEsrelhHT0VkazFHZCtJcld0S1dMRE1EcXlSMHFyN3grWm80RnZoQjRoQ3hvYzVnNlFIZ1VkaUlGL0o3cnNkcFUrN08iLCJtYWMiOiJjZmMwMGNjY2RiZTM2MDhiNzY2MGJjM2U0ZmMxYjhiN2JlNzBhMmU2YmVjOTEyNmRiNDBlMDcwZDMxOTcwY2I4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:59:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InY4OERmOFE2NDRJSlFjL1cvWVlSL0E9PSIsInZhbHVlIjoic0VGQlE1YmM3bmdiaWZFUlRKTGEzSmQzY2FpWTJscElIbDhuVDVRQW1mM1daUU1DeWVxZHE3NytYYUlBRm5FOGRaWDdDNkpJWGNsN3VyZ0piZzVMZERXdWxXSTF1MkJId0k3bEVNbmtzSGczZUdjNC9ubVJkdm1SaEtqbjZxV1c5QkdPdC9XazBYUEZoWG9OK0xSSmtiZEJMTWp4WnFja2hzTStWcDh0ZE9lbkUyYkI1Z2JxWDZRL3JHRENwZmxHcHd0UjBGeUx0S0RtYzdqWG9tUTZJMjRxT2QzUFB3Wkxrak9aK3p6Qi83OEg0aHR0U0QvY0cvSm9ZVmxrb1BEMTNSUHYwbXRRd2k5VmdLNE9ESjJpOFp5V3F0czNPMERSbmFkNjU2Zi9Dak5hZW11V0tPRUxKR1o1UUJsOENCTUpaMWlYWUdBQ3JqWElhblBtVkZmWDFTbzNkZE9GL3p0MlFXdk1TMXQzY0Z1S1k0VWZ5YzZSREhlRFlWV3FHdklKVWpxYyt2Y2d1SDZkV3lDK0dac0VQaVhTbDFaMld6VGZaZ1JWL0d5TStoaVhVeHBobUsvNmJualordWpITENXVlEwczVZa0xLbU5ZUk82K1VRNEhFMG9SUGI0QUtGOFZyU1creWJHcFBMY0ovSUJqUlA0LzFLa3JHdDQ5SHpGeVkiLCJtYWMiOiIxOTdjNTBjYjY0NTc0MGY0NTc3YWVhOWY4NTFlOWY5ZTIzOGUxNjQxY2M1ZTBjNjFiYjdlMmNkN2RkNzQ5M2IxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:59:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNvbDg5V2tZRmJ4UlMyTnhDOGpGUmc9PSIsInZhbHVlIjoiMGQ4QklMemd0bTFLUWMxbVB3UCt4UEp2aThTMDY5TEE2NzhzK3lrZnpoMGk3MnNOQUxGODJiMGtDcGFxaVdVN2VxYWJTSmhTWVVtajJtZWpTZ0cyWDA0QTcyM0oxOHRLR0RZVW5Ud0dsbG1JZ3Q4N1ZxU0dVRVNmalQ0elJyRCs5cnluZ25wZ3Z4Z0g5MExTdExaaWF6dXlORitYTnc4dlpuYzQwM2oyVy80eHpMVmgxUDY0NnVYbGRmOU4zZVFVZittRllLODVWL295M1FoUjBuVmtuVk9jS2JvZlRHS3krOUF5Q3hod1dtM1lsQ2dkYUVhNklQaEtLTEpXbzhaV01nVWIvWGg4UGhaazZOQVdoQ2xxRDR3aUFCWkI4aFVjSjgyblhNNitrZkpWMDRmems5eXhnSmtzamFCcXdabUxJUGNGOGtpVmFEUEdmN3l1akpCSEVueXpwMk9TUUNDVUhkWFQ0VVV2clBLMkdSWHpESW94S0RBR2Z6b25NOTFZQzk3d2RVM2hlUW9YK0dlNGRnVXQ3ZnBzd1hrWWhHMTNsNDBzeHMwODRMVVd6UmxhSUk5TDhzWWVHSElpMEsrelhHT0VkazFHZCtJcld0S1dMRE1EcXlSMHFyN3grWm80RnZoQjRoQ3hvYzVnNlFIZ1VkaUlGL0o3cnNkcFUrN08iLCJtYWMiOiJjZmMwMGNjY2RiZTM2MDhiNzY2MGJjM2U0ZmMxYjhiN2JlNzBhMmU2YmVjOTEyNmRiNDBlMDcwZDMxOTcwY2I4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:59:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InY4OERmOFE2NDRJSlFjL1cvWVlSL0E9PSIsInZhbHVlIjoic0VGQlE1YmM3bmdiaWZFUlRKTGEzSmQzY2FpWTJscElIbDhuVDVRQW1mM1daUU1DeWVxZHE3NytYYUlBRm5FOGRaWDdDNkpJWGNsN3VyZ0piZzVMZERXdWxXSTF1MkJId0k3bEVNbmtzSGczZUdjNC9ubVJkdm1SaEtqbjZxV1c5QkdPdC9XazBYUEZoWG9OK0xSSmtiZEJMTWp4WnFja2hzTStWcDh0ZE9lbkUyYkI1Z2JxWDZRL3JHRENwZmxHcHd0UjBGeUx0S0RtYzdqWG9tUTZJMjRxT2QzUFB3Wkxrak9aK3p6Qi83OEg0aHR0U0QvY0cvSm9ZVmxrb1BEMTNSUHYwbXRRd2k5VmdLNE9ESjJpOFp5V3F0czNPMERSbmFkNjU2Zi9Dak5hZW11V0tPRUxKR1o1UUJsOENCTUpaMWlYWUdBQ3JqWElhblBtVkZmWDFTbzNkZE9GL3p0MlFXdk1TMXQzY0Z1S1k0VWZ5YzZSREhlRFlWV3FHdklKVWpxYyt2Y2d1SDZkV3lDK0dac0VQaVhTbDFaMld6VGZaZ1JWL0d5TStoaVhVeHBobUsvNmJualordWpITENXVlEwczVZa0xLbU5ZUk82K1VRNEhFMG9SUGI0QUtGOFZyU1creWJHcFBMY0ovSUJqUlA0LzFLa3JHdDQ5SHpGeVkiLCJtYWMiOiIxOTdjNTBjYjY0NTc0MGY0NTc3YWVhOWY4NTFlOWY5ZTIzOGUxNjQxY2M1ZTBjNjFiYjdlMmNkN2RkNzQ5M2IxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:59:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817372673\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-988387964 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988387964\", {\"maxDepth\":0})</script>\n"}}