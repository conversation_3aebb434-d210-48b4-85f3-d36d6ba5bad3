{"__meta": {"id": "Xbc1f7f691bff01ff7d238a48db336499", "datetime": "2025-07-31 16:39:18", "utime": **********.064681, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753979954.743708, "end": **********.06475, "duration": 3.321042060852051, "duration_str": "3.32s", "measures": [{"label": "Booting", "start": 1753979954.743708, "relative_start": 0, "end": **********.757148, "relative_end": **********.757148, "duration": 3.0134401321411133, "duration_str": "3.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.757211, "relative_start": 3.013503074645996, "end": **********.064754, "relative_end": 4.0531158447265625e-06, "duration": 0.3075430393218994, "duration_str": "308ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47013072, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1954\" onclick=\"\">app/Http/Controllers/FinanceController.php:1954-2021</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0124, "accumulated_duration_str": "12.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.961788, "duration": 0.00751, "duration_str": "7.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 60.565}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0050862, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 60.565, "width_percent": 13.71}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1970}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.019984, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1970", "source": "app/Http/Controllers/FinanceController.php:1970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1970", "ajax": false, "filename": "FinanceController.php", "line": "1970"}, "connection": "radhe_same", "start_percent": 74.274, "width_percent": 10}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.031223, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1994", "source": "app/Http/Controllers/FinanceController.php:1994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1994", "ajax": false, "filename": "FinanceController.php", "line": "1994"}, "connection": "radhe_same", "start_percent": 84.274, "width_percent": 15.726}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-623545923 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-623545923\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-485560691 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485560691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1529497254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529497254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1925760094 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVHL1FtSWswT3dva3JxYVp0MHFwVWc9PSIsInZhbHVlIjoiQXNtSHdiQ2F3andHMmxLcTZmbEh4cFlxZGZnQ3Bud1J0QUhsdFVPcG9jNlVwam82aXFBWUp4aE1yRTVHZ2FBeWx1VC9NNTNrRkNnYzR6eUdjZjVhU0lXUTZrNVdsUm1UVzYwY0JuTTJFcGJoQUo0NnBaOHFhRGxydkVyY2lZMkZGSzFVZzZ5cno2ejluNzZESjd1ZEhqVUVtV1hrSGI2RTZZQSsrOWV3dW1Pb2JCYldvQkY4bUhqd1VINk1UZEx5YlRhaDRyNVY4UU5Tejk4MnhlZWZQU1FXSGljbWJrSFRtbElTTWtlcTNwTndCQ1NwajI0N0JqMFlWYU8wUVRWUTYwbGJ5TGVLT2I1RFE4QkNheFRoWDVLNDE5NnFSc1p5MU1YMEwxanRrNW9PMTI5TDhZbEttTzFNTTQrR3ZmbmErbXFtNDVaM0RVK3p4Tk5pZ2o0emZzTGlOdXlWRTFjWE8zdEtSNTNQcG5Nb3VndTNqNSt6NHNsaWladTJONE1yN3BDcC9sNUVDTW5wYnFNUFZwczNFMEZLZ1kvY0g4dFpFakJWaXRwK1owWUJjVTIweFFtcFo4azQ2UjlyQlp1VFY1N3dqck9XckltdmhVL09TUnFGZmc4bFBzd1ZnZ0YzYzFOTXRzbjN6cERSZDhWL1I3bm1XdVM3LytjR2Z3ZDQiLCJtYWMiOiIzNzE1ZGU0Y2U3NTQ3YWRlM2M4YzBlYmU1ZGY1MjJhYzhhMjliYjRmNGIyODdlYmVmMmQ3MTk0YTE0ZTA0MjljIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InYrL0JwZnRVK1RkUTM0eXV2Y1JjM3c9PSIsInZhbHVlIjoiWmJoVUxMM0NZUXRuUHlZaitvWGc2RDk5WWJFMDd0dzdCLzBDd0h1c01qWEl3bVZYbDhxaWNpQVZUcmZxTVdRcnJ5OHJMdG4waGdwSHlObzBBUUVSN2JSODRtQXAwVUp0SWcrakNWeE1rQXh0VG5PeE04SEhSWTlYakhHN0dyRlE4Y3hCT0NIbmpGNzB3M2wxMUJPOHVsblBydWpkQWx4TnIrbVptWFpqdzdrUDR3NnlLWE9JWVlRTEhFcVgza2dyNmYzZzhLeHlyMVR6bkdTOHYvYmVwNWZwZ0RieHRTakxVTHZVWHlVQWp2ZTR0aXV5YVZyNUJCSDVHSUs1UHg0citicGR0V0hFL0lnYWU3N1pwMzBUUHVSVFBJQXdab1JuOUJtekIzc25hblVETUVWcmNUSncyb3RFRkxXYWw5TE5JeFJueElQdTlSRWFwcEJmT0NiU1ZWUFlveGRJc0dpY3JCMk9DL2VMRm81TmFyZmxaR2ZTZUdJcUJBQ2psUTl2SHBsekFhbkFGU2VWSmR3SGMvRVV0S3pnYXRDZDJ0MlNzck5PK3A2VkFjcmt3M1ZBN1A2REN4T2ZIR3lORGtjdzhndi9jczIrcDl4N1V0cFBid1hVUWVwV1ZDcHozUDBaclNvdENDZlBMWGJJRHVLM3c4NHNqMUJXV2RvWnA4Z2QiLCJtYWMiOiIyMDUwYmZkMjc5ZDJiZGRkNTg5ZDc0YTJiOGZmYzczMjI1OWRkNDhlMDZhNTE5NmYyYTEwMjQwM2UwMmNhMWE3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ino2WkNVMUdEWkFxd0JodTlTQXZtRlE9PSIsInZhbHVlIjoiMnMyWHE4ckNtblFuaThNdWQ2RjB1QUVRNm5uWndoR1FuVzJUU0hrMi9NMVIxT3Q2OU00QTNYL3hweUc2SjEyR3E4M2RTYnBrMkxWTGozdFFBZTAvcVFOWkhBN2RYa3NrVDBVUytxandkNzNZbTZ2WGIrSkcya085REFzSS8vRFFLSHl5ZXlPUmk5REx6cCtpckdSZ0QzUno1dElkM2RVZGJ2NXpHcHIxWnQ5VFJhUGxPbEdWUFFnNTNDNkZKNHJDVzVacC9YdFlMWGtwdEVIVE1pdkw1TGlhcENsaTc3NktpUHFMS1V1RjhKY05hRkpYS2I2M1JrVnlvcHFESFlSd05IK2VkMkxNZi82Vnh1SW1IdW5DYTJ1dTRhc0xpU0drdloxRXR3TGUwbmRvVDlKTzAvb0dsdm41aGtCTjNaUVorSlNQM3VoQm94WVBJeFUzOGNORS9HWTBvckJpNWRTWUMyYnd3TEdtTXlqNFBGMlduNEljSlRrZVpnVUFJK2ZFdUdrVDVYcStvK2FjYkJHRDRGUXJGcHhvVUczUDVvYlFoL3pmSS80aTBwUWFvSEozcWQ3V1p1aTBxakM4bFpsZWJqLzlGdWQvRXRSUzlGaXZtaHdFdEx6cHVtYnAxL3Z6TkVBd21HMks4aGg5eUFDREo0Y01qVjMvTnduSUdGNVoiLCJtYWMiOiIxY2I3ZmQ4YWZlYjk4OThlOWI3ZDRlNjA1MWEwNjM2NzM5MjJiYWEzNGRmOGY3ZWViYzEzNzc3NmI5ODRiNGE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925760094\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1993425436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xD7TBt3NIk1LuIa99god1PLIZ6HqBs4hFYAVKUFx</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iegPCUPTk5BCtpPOYlMKkbIYY1yZUCGoMOZasv64</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993425436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1558224379 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:39:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdlZlZXTTJLcHdSbEtjMEdWekVJdlE9PSIsInZhbHVlIjoibVd1ckR2NDFGaURSVHdLM2VnRUc2bWlsb1lQcy9JQ0VlL0xXQ1JXWHZTNkI4cXFOV1VIajVhUzFaU2t0OU13WGZMcDRBYW15VlFESnBhck43SXF6Vjl6MW5naWZIeHV2R3J5NkExQmIwcVRJMVFCR211T3BvemRkT3ZwZ3dkNFpMQ3hoY2xpOVIvc0NmTEQ2eEp3WmRVcXJKcnBKT0l1VEJHNW5wVXJIS0owZ3REenRuR2piSk1oN0ZkcTUxSWdxZHUzVXowdmNvVm1mVnJqZklOcmU5REMvOVByVUNlSXFCV1YyZVlHMTFVZiswTnZZRHNpR3hWUG1SRGdKZ0JacU13M29yZVBqZ21qUTZkVWtKUk9zbWlTcXlRZG9pZVM3bTNuTFRNT3JORXdwU2U0NjY0dXZWREpCRzhrVUNHWFpBeDYvNWVQZ2Z5ZmJQWWF0U2VnblJUU2xYWGdUVjcybTVMaFgwTzlERjN3eDVrWGhKUkkyOEZoSkZ6Y1UyNTdMTGw5citZN1pSZXU1aUNqa1pVSjJqdWRzRENxSmJUSU9aNHpqV2tteWRtZndLS0hiZk8yNm9DeTd6am4xeGZmdE1hU1pETEVuMkRVMFJJK3FRSkpSUGc4REtUa0NwanNzMjhIMlVONGNhS0M0RldUblNYU3RaZlJZbEdzcG8yeDciLCJtYWMiOiI2MGVlOTJhODNiMmM4YjA5YjRkOTMyMTc3OTYxM2QyOGY3NTI4YmNiYjBmYjY0M2NhMWIyN2E0NTIzOTNmNGIwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:39:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZPbEsvT0d1Q3dCYnp2VFQ0L3VqeVE9PSIsInZhbHVlIjoiY01FUDQ4Rm82Q08xWDZyNnNocVN4NVBXL04xamVMa2plb2ZHS1l6aXVvZlp5QkxWblB6Q2lCTHU4a0NlZnUyMDlDWTg2SHdlbkppT1ZobTFxVEl0OHR0NE1QY1NMaFZjZmxOWlJLQzFHZml0SlZiRlF3RDlKY1ZNSjNtR1V1RHIyK2xHR0o3L3QxZ3A0VExEQXVpM3Avc2xrOFRKNUp4N0hRc3ZXRmFWYVdEUll6Z2tvUEpFbDZpY0tBeENSSUZjVFVxU0FvUUFLc1VwZGhpRlZVak14OFh4ZGJwWnZtMTFxaGVuaUtrNE1wUXRWSlVROGEwbHBtTWhjcTJ0M1g4bE4wMElmWkUrTlcybHhYaS8zSmNkZTZwS0hFM3NRL3BFM05xS21DZ1k3UWZPcDV3Tm5IdlJxaXU2NGN6cXlmSU9GRW9Wd1RjWlVZTWRtMndLZWhLSXdLcExkMExCQURKUDlodGpYRDRiT0NZZGdGWlRVdkhlbFRJRTM5bVVRaElGN3Q5TzNJNTdCT05lbW5qQWppWWhOV3IxSXpwMk50TVdrcGx3UVl0ZzBvd0FvY3ZVbEpwczNUQWwwRlEwaHBwNGt0U3Z4c3F0MGQ2bW9TVGZSY3krdnRRSDIySFltWmlvQ0xoUEhuWE5rcTlxcllhT2hrQmFzNytpWUVPczVUUmYiLCJtYWMiOiJlOWEzNDZjY2I3ZGUxNzI5MmQ1NDA1NDc4MGZjMzkxMmQwMDMxZjM2NjY4YTUxNDRlMWM1ZTU2ODM4YjAyOGQyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:39:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdlZlZXTTJLcHdSbEtjMEdWekVJdlE9PSIsInZhbHVlIjoibVd1ckR2NDFGaURSVHdLM2VnRUc2bWlsb1lQcy9JQ0VlL0xXQ1JXWHZTNkI4cXFOV1VIajVhUzFaU2t0OU13WGZMcDRBYW15VlFESnBhck43SXF6Vjl6MW5naWZIeHV2R3J5NkExQmIwcVRJMVFCR211T3BvemRkT3ZwZ3dkNFpMQ3hoY2xpOVIvc0NmTEQ2eEp3WmRVcXJKcnBKT0l1VEJHNW5wVXJIS0owZ3REenRuR2piSk1oN0ZkcTUxSWdxZHUzVXowdmNvVm1mVnJqZklOcmU5REMvOVByVUNlSXFCV1YyZVlHMTFVZiswTnZZRHNpR3hWUG1SRGdKZ0JacU13M29yZVBqZ21qUTZkVWtKUk9zbWlTcXlRZG9pZVM3bTNuTFRNT3JORXdwU2U0NjY0dXZWREpCRzhrVUNHWFpBeDYvNWVQZ2Z5ZmJQWWF0U2VnblJUU2xYWGdUVjcybTVMaFgwTzlERjN3eDVrWGhKUkkyOEZoSkZ6Y1UyNTdMTGw5citZN1pSZXU1aUNqa1pVSjJqdWRzRENxSmJUSU9aNHpqV2tteWRtZndLS0hiZk8yNm9DeTd6am4xeGZmdE1hU1pETEVuMkRVMFJJK3FRSkpSUGc4REtUa0NwanNzMjhIMlVONGNhS0M0RldUblNYU3RaZlJZbEdzcG8yeDciLCJtYWMiOiI2MGVlOTJhODNiMmM4YjA5YjRkOTMyMTc3OTYxM2QyOGY3NTI4YmNiYjBmYjY0M2NhMWIyN2E0NTIzOTNmNGIwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:39:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZPbEsvT0d1Q3dCYnp2VFQ0L3VqeVE9PSIsInZhbHVlIjoiY01FUDQ4Rm82Q08xWDZyNnNocVN4NVBXL04xamVMa2plb2ZHS1l6aXVvZlp5QkxWblB6Q2lCTHU4a0NlZnUyMDlDWTg2SHdlbkppT1ZobTFxVEl0OHR0NE1QY1NMaFZjZmxOWlJLQzFHZml0SlZiRlF3RDlKY1ZNSjNtR1V1RHIyK2xHR0o3L3QxZ3A0VExEQXVpM3Avc2xrOFRKNUp4N0hRc3ZXRmFWYVdEUll6Z2tvUEpFbDZpY0tBeENSSUZjVFVxU0FvUUFLc1VwZGhpRlZVak14OFh4ZGJwWnZtMTFxaGVuaUtrNE1wUXRWSlVROGEwbHBtTWhjcTJ0M1g4bE4wMElmWkUrTlcybHhYaS8zSmNkZTZwS0hFM3NRL3BFM05xS21DZ1k3UWZPcDV3Tm5IdlJxaXU2NGN6cXlmSU9GRW9Wd1RjWlVZTWRtMndLZWhLSXdLcExkMExCQURKUDlodGpYRDRiT0NZZGdGWlRVdkhlbFRJRTM5bVVRaElGN3Q5TzNJNTdCT05lbW5qQWppWWhOV3IxSXpwMk50TVdrcGx3UVl0ZzBvd0FvY3ZVbEpwczNUQWwwRlEwaHBwNGt0U3Z4c3F0MGQ2bW9TVGZSY3krdnRRSDIySFltWmlvQ0xoUEhuWE5rcTlxcllhT2hrQmFzNytpWUVPczVUUmYiLCJtYWMiOiJlOWEzNDZjY2I3ZGUxNzI5MmQ1NDA1NDc4MGZjMzkxMmQwMDMxZjM2NjY4YTUxNDRlMWM1ZTU2ODM4YjAyOGQyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:39:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558224379\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-953101804 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aksMKhZUhfAMDbFFECiVn0Sn1XQKZvBTWEO54Bse</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953101804\", {\"maxDepth\":0})</script>\n"}}