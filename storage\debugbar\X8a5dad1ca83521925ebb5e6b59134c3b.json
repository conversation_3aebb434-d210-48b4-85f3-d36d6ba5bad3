{"__meta": {"id": "X8a5dad1ca83521925ebb5e6b59134c3b", "datetime": "2025-07-31 15:52:12", "utime": **********.92268, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753977131.155965, "end": **********.92273, "duration": 1.7667648792266846, "duration_str": "1.77s", "measures": [{"label": "Booting", "start": 1753977131.155965, "relative_start": 0, "end": **********.796723, "relative_end": **********.796723, "duration": 1.6407577991485596, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.796748, "relative_start": 1.****************, "end": **********.922736, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YIAmaKPo5PMDYaHfSyZIZ47NDu9NicK6H1bOxPDW", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-1869373649 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1869373649\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-469348932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469348932\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2069141162 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2069141162\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1110587434 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110587434\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-168418847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-168418847\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1360978721 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 15:52:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis5YjJRUjdnNkViN1lEaHRDNkFrY0E9PSIsInZhbHVlIjoiZGJmSkpzZHhrOXlNVFZBWERtU0piVmV1c1g3YXdDM3JENU5pWUEzYVpXUjc1bjJyMi9sMU1PS2ZsdUlhdGlqQmM3UFNLaFFvWExqQmNzZ3VJL3pxK3RBenRySG51d2g3NkFpempRbTY5aWFGU3M1RHc2TDQ1a3pXY2hhYnhOOE1QQWdHQVJTSWk0aUVkdFhiNVdBK0gxb0NQdTBRU0FweWpOWm9WdFNmUU9kUXE2MDVhWEw1TGQ2SERqM241RlFVVVdRL3Fzd0JBdFd0RjUrZk8ySThETkplVEg5VHFhYlRSZVRHMTZTaEk4MklyNGkxRXJYcGptNmNvR3pJYmRVK0tlYWNqRXBYWXg1aDZhd0E3SU5UbGNqVFppemxFTzdxc3diVTNBUGlTU3VPc21lNkE5bklNdHFlcGVBWnNSVXNJQ2VZd3NUNXdGdlZpb3pKMnZoQ2VhTldITWN6M05neWc0OWFDN3NXc3AvVkladDd6ZnI3TG03QytYR1B0MjF1UmMzQktlNUJMenRGN3pCbDEzSGtaeU4ramN5dE9pb0YwdWFaanlMZHA2Z1h2L2NmUjBadW5xSDhjaDk5SHZXTnVxZTdWM3lTdkpHOG9odVIreFNpUEp0aExJODdOZEN5emt0cm5MdUNUbEdEYlEraEhSZVpsNVNLQ01jWUthT2IiLCJtYWMiOiIyNTYwNWM4NGYxN2RiNTM1MDBjZGFjNDNjMmQ1ZDk5NWQ2YmI2MjU2ZDk2ZGM1ZTA0YzE1NGIyYjY0OGUxYzlkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNGWThTakJqOHE4am5icC9mbW1rOHc9PSIsInZhbHVlIjoiaDV3czkrZSs5WE1CalRUQnZSV2hMMkNlaVRFczMxUUpKYy9zeUZSZC9lY2FuM21lM1UyVFhFVGw5dkpXRktLK3ZTRG15SEFmNTErQk1RS25BQ2FUVVdUdnhTMGlTMms4ZFQ3MnhXMlFZN055SVRlaDVRRWU4YW5mWXljWlVLbXdvZUkwUmtVREw3RENxYk1KaGFGcUsreUxhVXU1azhvRW5wMjl6aGRCeFdJSThyMTFGODUyM0JzYThOR3RuenZTWGl4b2RqVGkzN1VENFRSUjQzQnM1emdKMTczVG9BR0p3SXpKdVZDZU8wbnBEMkNYZ0xsWWpmY3BmOTNjbG02a1g1eXgzdjJVSU96a1lqaThqVjZKMXJxWjlkbExUYmIvYzZ5WjQ4akJoWThPMWgrcDJxa2xQSVNHMGorSmc1U0VhemZSZlExNWt6TWNlMFg5RnluOFREbDdpT0lxczEyTjJxYnRKTTNPNllFZHVJUGtLWG5vYUpNQzk3N2ViWktobzJmN0tOemczVks3VHpReTZoYkdETkhMSWdvUlQ3N1Yyemg4U1l4czRWaVc4ZDFQZkpteW40R3VNeGVVakUybExPWWdJS3VvcmRBV2hrVHgrcnNlcFJYSkhCaFoyRGljeS9qN2RXa1FkcThPK09rdTNvTUZ2MkViUXZpMVBocGciLCJtYWMiOiJiOWI1MWJlZDIzMTRlNzRiNWYzZWJlNWQ2YmRlZWUzYzcyZDdlMTJkMjJmYjY5MDU3MWUyNTg1MzA4Mzg0MzUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 17:52:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis5YjJRUjdnNkViN1lEaHRDNkFrY0E9PSIsInZhbHVlIjoiZGJmSkpzZHhrOXlNVFZBWERtU0piVmV1c1g3YXdDM3JENU5pWUEzYVpXUjc1bjJyMi9sMU1PS2ZsdUlhdGlqQmM3UFNLaFFvWExqQmNzZ3VJL3pxK3RBenRySG51d2g3NkFpempRbTY5aWFGU3M1RHc2TDQ1a3pXY2hhYnhOOE1QQWdHQVJTSWk0aUVkdFhiNVdBK0gxb0NQdTBRU0FweWpOWm9WdFNmUU9kUXE2MDVhWEw1TGQ2SERqM241RlFVVVdRL3Fzd0JBdFd0RjUrZk8ySThETkplVEg5VHFhYlRSZVRHMTZTaEk4MklyNGkxRXJYcGptNmNvR3pJYmRVK0tlYWNqRXBYWXg1aDZhd0E3SU5UbGNqVFppemxFTzdxc3diVTNBUGlTU3VPc21lNkE5bklNdHFlcGVBWnNSVXNJQ2VZd3NUNXdGdlZpb3pKMnZoQ2VhTldITWN6M05neWc0OWFDN3NXc3AvVkladDd6ZnI3TG03QytYR1B0MjF1UmMzQktlNUJMenRGN3pCbDEzSGtaeU4ramN5dE9pb0YwdWFaanlMZHA2Z1h2L2NmUjBadW5xSDhjaDk5SHZXTnVxZTdWM3lTdkpHOG9odVIreFNpUEp0aExJODdOZEN5emt0cm5MdUNUbEdEYlEraEhSZVpsNVNLQ01jWUthT2IiLCJtYWMiOiIyNTYwNWM4NGYxN2RiNTM1MDBjZGFjNDNjMmQ1ZDk5NWQ2YmI2MjU2ZDk2ZGM1ZTA0YzE1NGIyYjY0OGUxYzlkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNGWThTakJqOHE4am5icC9mbW1rOHc9PSIsInZhbHVlIjoiaDV3czkrZSs5WE1CalRUQnZSV2hMMkNlaVRFczMxUUpKYy9zeUZSZC9lY2FuM21lM1UyVFhFVGw5dkpXRktLK3ZTRG15SEFmNTErQk1RS25BQ2FUVVdUdnhTMGlTMms4ZFQ3MnhXMlFZN055SVRlaDVRRWU4YW5mWXljWlVLbXdvZUkwUmtVREw3RENxYk1KaGFGcUsreUxhVXU1azhvRW5wMjl6aGRCeFdJSThyMTFGODUyM0JzYThOR3RuenZTWGl4b2RqVGkzN1VENFRSUjQzQnM1emdKMTczVG9BR0p3SXpKdVZDZU8wbnBEMkNYZ0xsWWpmY3BmOTNjbG02a1g1eXgzdjJVSU96a1lqaThqVjZKMXJxWjlkbExUYmIvYzZ5WjQ4akJoWThPMWgrcDJxa2xQSVNHMGorSmc1U0VhemZSZlExNWt6TWNlMFg5RnluOFREbDdpT0lxczEyTjJxYnRKTTNPNllFZHVJUGtLWG5vYUpNQzk3N2ViWktobzJmN0tOemczVks3VHpReTZoYkdETkhMSWdvUlQ3N1Yyemg4U1l4czRWaVc4ZDFQZkpteW40R3VNeGVVakUybExPWWdJS3VvcmRBV2hrVHgrcnNlcFJYSkhCaFoyRGljeS9qN2RXa1FkcThPK09rdTNvTUZ2MkViUXZpMVBocGciLCJtYWMiOiJiOWI1MWJlZDIzMTRlNzRiNWYzZWJlNWQ2YmRlZWUzYzcyZDdlMTJkMjJmYjY5MDU3MWUyNTg1MzA4Mzg0MzUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 17:52:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360978721\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-514232521 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YIAmaKPo5PMDYaHfSyZIZ47NDu9NicK6H1bOxPDW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514232521\", {\"maxDepth\":0})</script>\n"}}