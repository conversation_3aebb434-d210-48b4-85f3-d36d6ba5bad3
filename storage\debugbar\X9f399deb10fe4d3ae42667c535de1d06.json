{"__meta": {"id": "X9f399deb10fe4d3ae42667c535de1d06", "datetime": "2025-07-31 16:52:00", "utime": **********.023586, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753980717.849406, "end": **********.023664, "duration": 2.17425799369812, "duration_str": "2.17s", "measures": [{"label": "Booting", "start": 1753980717.849406, "relative_start": 0, "end": 1753980719.791248, "relative_end": 1753980719.791248, "duration": 1.9418420791625977, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753980719.791274, "relative_start": 1.****************, "end": **********.023671, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3065\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1897 to 1903\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1897\" onclick=\"\">routes/web.php:1897-1903</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "06HnAcoj4HASeOYQjbwnaIEGlAA1fDugzBtuIaXp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1731072641 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1731072641\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-487253947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-487253947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1678617367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1678617367\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-184210010 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184210010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1535484601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1535484601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1188658102 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 16:51:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNabUkrdUs0WFhCUE42U0RLUk03Qmc9PSIsInZhbHVlIjoieXdnY3A4TE5UUVhtc3lnNFJJQ2toVWFhek9NS1pGZFgzQVRVZVFtN2NxOGlDTjFVbUVMSktPZjFaZlRNbGhIK2NoYXF0YWRpUjZsWGFnaHRCc040bkkwS3hwbHFRUHdtL3JkZXNjSGZGMHNqSjZ5YVJQZW9zekIzQ0F0K25xZ0tQS1ZJVVN6ZllGMVRBUDFVaHpuVldOaXNvSWxNR2hwRmRKVnpGVzNhYTE3czVMRkluTGZBQ0xWWjllL2hZK0dKRFJTRUF3bEpsblRQd0l3WHNSUnNra1dXMW1CbTQ1cUxZUDJiOVhIQkR4VG8wTXBQRXhMelYrbEZGN0QxY05KQnloTmpPTDd1RUNKSlJMZVFaUmJHbmxUOTlEcmZWQzIzUmowaGVyVzZmLzY0K3lOTWFZZFhmZG92alkwVGE5VVZQa0FZVEs5WURzaXo4VXFScW5NYVZrR29YTUExR3ZXWCtpTXhNTklZQjZrTTJNNW9HdVdnT0lqb0Q3UnNSc1p4OFhDRGN3VHhMVFk1UWhLOHBaUGN1SWhYS0R2L0VnQmhuT0FyWWxJN3lZck1pUENWNVdHTDNWaTFIVjV6YnRIeVdyL1R5R1V6Q2ZrWTBLQW1oT1hSMG5NRG1mblRuK1daU3JQSWE3UzQ2cHBNZ1FKMnQ4cDYwZ1U2Y1ViaVNCdGoiLCJtYWMiOiIzNmZjNWJkNGRlNDBkYjA3ZDM5MmUyYWNjMTcxZDU2MGRjNTAxNjkyOTk2YTUzYjViM2E3M2QyNThlYzlhYzI1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:51:59 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkJhdDg3ekdpTzRPZ0NkQnYzMUFVN2c9PSIsInZhbHVlIjoieWlrZmdpckI2b1p4bUIrZThodFpBN21RMW1OV2F4RlF6VUZabEJqY1pnVjh1Ym1JWlI5NHY0STlsNmdwVWxnTXlqK0NHdUd6VWRvZEVtb1hBcXJCZ0Rha1dBYkpobmtHcU4wcjJ6SUZmNWR0czhJS1NXOTNDSGh5Y3FJTXo1Q1VDRDZtMDdnbldISGczN1VnUFY1YzI5dUdrZ29iQ3NPanVNcENPeVVMOTFoK2UwdkNyWC91bUhsV01ZS3Y1MWtUaW9CNDBzaENJNnBoemRSOVcyNHFsNmVnUzFHR2VwemUwYXQwS3liYTFIR1FuakpibkIwN3Y5WmxWQ1YwSTJRbWxIQjY3R0VJNGFnaWFObzdsTk9kRUN1bW5HbTNVR2NwSDhsUFlVYTRSb2hZZEFJZWc2NE5jTVlRb1VreDdqaFJoNXlIRXhQTklvakcweUhHWXhCQkd2bzFERzBEVzFyL1hXSEVNN1R2TDc1TUhxTE5sZ0Y0ais5UHNlSnFVS3M5ekF3WTAzN2Jza0I1TWlvMENqMWIwbHkvRmVGU0ptblFxRWdYczVPVUM1eDd3Q2ZQa3pKdktkcWE1bUsrTXBpckE1LzZ0Z1B5SEpvMXl1M3BHb0RvWE1DZjFiTkduV3FKYjV0aXAzckJsVTBpTC8yb2FTVk5QVmpTTytOOTVSSnoiLCJtYWMiOiI2OWZiOGQ2ZDg0OGE3Zjk0YmU5NGFmYmE3NjI3OWJiOTk3MWYwYTZiMjg5ZDEwMmY4YTc5MDI2NWE1OGUzYTcwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 18:51:59 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNabUkrdUs0WFhCUE42U0RLUk03Qmc9PSIsInZhbHVlIjoieXdnY3A4TE5UUVhtc3lnNFJJQ2toVWFhek9NS1pGZFgzQVRVZVFtN2NxOGlDTjFVbUVMSktPZjFaZlRNbGhIK2NoYXF0YWRpUjZsWGFnaHRCc040bkkwS3hwbHFRUHdtL3JkZXNjSGZGMHNqSjZ5YVJQZW9zekIzQ0F0K25xZ0tQS1ZJVVN6ZllGMVRBUDFVaHpuVldOaXNvSWxNR2hwRmRKVnpGVzNhYTE3czVMRkluTGZBQ0xWWjllL2hZK0dKRFJTRUF3bEpsblRQd0l3WHNSUnNra1dXMW1CbTQ1cUxZUDJiOVhIQkR4VG8wTXBQRXhMelYrbEZGN0QxY05KQnloTmpPTDd1RUNKSlJMZVFaUmJHbmxUOTlEcmZWQzIzUmowaGVyVzZmLzY0K3lOTWFZZFhmZG92alkwVGE5VVZQa0FZVEs5WURzaXo4VXFScW5NYVZrR29YTUExR3ZXWCtpTXhNTklZQjZrTTJNNW9HdVdnT0lqb0Q3UnNSc1p4OFhDRGN3VHhMVFk1UWhLOHBaUGN1SWhYS0R2L0VnQmhuT0FyWWxJN3lZck1pUENWNVdHTDNWaTFIVjV6YnRIeVdyL1R5R1V6Q2ZrWTBLQW1oT1hSMG5NRG1mblRuK1daU3JQSWE3UzQ2cHBNZ1FKMnQ4cDYwZ1U2Y1ViaVNCdGoiLCJtYWMiOiIzNmZjNWJkNGRlNDBkYjA3ZDM5MmUyYWNjMTcxZDU2MGRjNTAxNjkyOTk2YTUzYjViM2E3M2QyNThlYzlhYzI1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:51:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkJhdDg3ekdpTzRPZ0NkQnYzMUFVN2c9PSIsInZhbHVlIjoieWlrZmdpckI2b1p4bUIrZThodFpBN21RMW1OV2F4RlF6VUZabEJqY1pnVjh1Ym1JWlI5NHY0STlsNmdwVWxnTXlqK0NHdUd6VWRvZEVtb1hBcXJCZ0Rha1dBYkpobmtHcU4wcjJ6SUZmNWR0czhJS1NXOTNDSGh5Y3FJTXo1Q1VDRDZtMDdnbldISGczN1VnUFY1YzI5dUdrZ29iQ3NPanVNcENPeVVMOTFoK2UwdkNyWC91bUhsV01ZS3Y1MWtUaW9CNDBzaENJNnBoemRSOVcyNHFsNmVnUzFHR2VwemUwYXQwS3liYTFIR1FuakpibkIwN3Y5WmxWQ1YwSTJRbWxIQjY3R0VJNGFnaWFObzdsTk9kRUN1bW5HbTNVR2NwSDhsUFlVYTRSb2hZZEFJZWc2NE5jTVlRb1VreDdqaFJoNXlIRXhQTklvakcweUhHWXhCQkd2bzFERzBEVzFyL1hXSEVNN1R2TDc1TUhxTE5sZ0Y0ais5UHNlSnFVS3M5ekF3WTAzN2Jza0I1TWlvMENqMWIwbHkvRmVGU0ptblFxRWdYczVPVUM1eDd3Q2ZQa3pKdktkcWE1bUsrTXBpckE1LzZ0Z1B5SEpvMXl1M3BHb0RvWE1DZjFiTkduV3FKYjV0aXAzckJsVTBpTC8yb2FTVk5QVmpTTytOOTVSSnoiLCJtYWMiOiI2OWZiOGQ2ZDg0OGE3Zjk0YmU5NGFmYmE3NjI3OWJiOTk3MWYwYTZiMjg5ZDEwMmY4YTc5MDI2NWE1OGUzYTcwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 18:51:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188658102\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2066183276 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">06HnAcoj4HASeOYQjbwnaIEGlAA1fDugzBtuIaXp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066183276\", {\"maxDepth\":0})</script>\n"}}